# Installation
> `npm install --save @types/pouchdb-browser`

# Summary
This package contains type definitions for pouchdb-browser (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-browser.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-browser/index.d.ts)
````ts
/// <reference types="pouchdb-core" />
/// <reference types="pouchdb-adapter-idb" />
/// <reference types="pouchdb-adapter-websql" />
/// <reference types="pouchdb-adapter-http" />
/// <reference types="pouchdb-mapreduce" />
/// <reference types="pouchdb-replication" />

declare const PouchDb: PouchDB.Static;
export = PouchDb;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-adapter-http](https://npmjs.com/package/@types/pouchdb-adapter-http), [@types/pouchdb-adapter-idb](https://npmjs.com/package/@types/pouchdb-adapter-idb), [@types/pouchdb-adapter-websql](https://npmjs.com/package/@types/pouchdb-adapter-websql), [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core), [@types/pouchdb-mapreduce](https://npmjs.com/package/@types/pouchdb-mapreduce), [@types/pouchdb-replication](https://npmjs.com/package/@types/pouchdb-replication)

# Credits
These definitions were written by [Simon Paulger](https://github.com/spaulg), [Brian Geppert](https://github.com/geppy), and [Frederico Galvão](https://github.com/fredgalvao).
