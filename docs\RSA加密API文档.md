# RSA加密API文档

## 📋 概述

本文档描述了新增的RSA加密相关API端点，用于支持安全的密码传输和认证。

## 🔐 新增API端点

### 1. 获取RSA公钥

**端点**: `GET /crypto/public-key`

**描述**: 获取服务器的RSA公钥，用于前端加密密码

**请求**:
```http
GET /crypto/public-key HTTP/1.1
Host: localhost:3000
Content-Type: application/json
```

**响应**:
```json
{
    "success": true,
    "publicKey": "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----"
}
```

**响应字段说明**:
- `success`: 布尔值，表示请求是否成功
- `publicKey`: 字符串，PEM格式的RSA公钥

**错误响应**:
```json
{
    "success": false,
    "message": "获取公钥失败"
}
```

## 🔄 更新的API端点

### 1. 用户注册

**端点**: `POST /auth/register`

**描述**: 用户注册，现在需要使用RSA加密的密码

**请求**:
```http
POST /auth/register HTTP/1.1
Host: localhost:3000
Content-Type: application/json

{
    "name": "张三",
    "email": "<EMAIL>",
    "password": "RSA加密后的密码(Base64编码)"
}
```

**成功响应**:
```json
{
    "success": true,
    "name": "张三",
    "email": "<EMAIL>",
    "id": "user_abc123def456"
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "密码格式错误"
}
```

### 2. 用户登录

**端点**: `POST /auth/login`

**描述**: 用户登录，现在需要使用RSA加密的密码

**请求**:
```http
POST /auth/login HTTP/1.1
Host: localhost:3000
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "RSA加密后的密码(Base64编码)"
}
```

**成功响应**:
```json
{
    "success": true,
    "user_id": "user_abc123def456",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**错误响应**:
```json
{
    "success": false,
    "message": "邮箱或密码错误"
}
```

或

```json
{
    "success": false,
    "message": "密码格式错误"
}
```

## 🔧 使用流程

### 完整的认证流程

1. **获取公钥**
   ```javascript
   const response = await fetch('/crypto/public-key');
   const { publicKey } = await response.json();
   ```

2. **加密密码**
   ```javascript
   const key = new NodeRSA(publicKey);
   const encryptedPassword = key.encrypt(password, 'base64');
   ```

3. **注册/登录**
   ```javascript
   const response = await fetch('/auth/login', {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify({
           email: email,
           password: encryptedPassword
       })
   });
   ```

## ⚠️ 重要说明

### 安全性要求

1. **密码必须加密**: 所有密码字段现在都必须使用RSA公钥加密后传输
2. **Base64编码**: 加密后的密码必须使用Base64编码
3. **公钥获取**: 每次加密前都应该获取最新的公钥
4. **错误处理**: 必须正确处理"密码格式错误"的情况

### 向后兼容性

- ❌ 不再支持明文密码传输
- ❌ 旧的前端应用必须更新才能继续使用
- ✅ JWT Token认证机制保持不变
- ✅ 其他API端点保持不变

### 性能考虑

- RSA加密/解密相对较慢，但对于认证场景是可接受的
- 建议缓存公钥以减少网络请求
- 服务器端会自动管理密钥对的生成和存储

## 📊 测试结果

系统已通过完整测试：

- ✅ 公钥获取: 450字符PEM格式
- ✅ 密码加密: 344字符Base64编码
- ✅ 注册流程: 正常工作
- ✅ 登录流程: 正常工作
- ✅ Token认证: 正常工作

## 🔍 故障排除

### 常见错误码

- `400 - 密码格式错误`: RSA解密失败，检查加密过程
- `400 - 字段不完整`: 缺少必要的请求字段
- `401 - 邮箱或密码错误`: 用户不存在或密码错误
- `500 - 服务器内部错误`: 服务器端错误，检查日志

### 调试建议

1. 使用浏览器开发者工具检查网络请求
2. 验证公钥格式是否正确
3. 确认加密后的密码是Base64格式
4. 检查服务器日志获取详细错误信息
