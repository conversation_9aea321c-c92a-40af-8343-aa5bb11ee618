# Installation
> `npm install --save @types/pouchdb-adapter-websql`

# Summary
This package contains type definitions for pouchdb-adapter-websql (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-websql.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-websql/index.d.ts)
````ts
/// <reference types="pouchdb-core" />

declare namespace PouchDB {
    namespace Core {
        interface DatabaseInfo {
            sqlite_plugin?: boolean | undefined;
            websql_encoding?: "UTF-8" | "UTF-16" | undefined;
        }
    }

    namespace AdapterWebSql {
        interface Configuration extends Configuration.LocalDatabaseConfiguration {
            /**
             * Amount in MB to request for storage.
             */
            size?: number | undefined;
            adapter: "websql";
        }
    }

    interface Static {
        new<Content extends {}>(name: string | null, options: AdapterWebSql.Configuration): Database<Content>;
    }
}

declare module "pouchdb-adapter-websql" {
    const plugin: PouchDB.Plugin;
    export = plugin;
}

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core)

# Credits
These definitions were written by [Simon Paulger](https://github.com/spaulg), [Brian Geppert](https://github.com/geppy), and [Frederico Galvão](https://github.com/fredgalvao).
