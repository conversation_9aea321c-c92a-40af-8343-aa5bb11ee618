"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskPool = exports.TaskThread = exports.TimeUtils = void 0;
const log_1 = require("../config/log");
class TimeUtils {
    static parseTimeToMinutes(timeStr) {
        const [hours, minutes] = timeStr.split(':').map(Number);
        return hours * 60 + minutes;
    }
    static minutesToTimeString(minutes) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
    }
    static parseDate(dateStr) {
        const [year, month, day] = dateStr.split('-').map(Number);
        return new Date(year, month - 1, day);
    }
    static dateToString(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    static getDateWithOffset(offset) {
        const date = new Date();
        date.setDate(date.getDate() + offset);
        return this.dateToString(date);
    }
    static getDayOfWeek(date) {
        return date.getDay();
    }
    static calculateNextExecutionTime(task, currentTime = new Date()) {
        if (!task.active)
            return null;
        const executeTime = this.parseTimeToMinutes(task.execute.time);
        const executeHours = Math.floor(executeTime / 60);
        const executeMinutes = executeTime % 60;
        if (task.repetive && task.repeat) {
            return this.calculateRepetitiveTaskTime(task, currentTime, executeHours, executeMinutes);
        }
        else {
            return this.calculateSingleTaskTime(task, currentTime, executeHours, executeMinutes);
        }
    }
    static calculateRepetitiveTaskTime(task, currentTime, executeHours, executeMinutes) {
        if (!task.repeat)
            return null;
        const now = new Date(currentTime);
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        for (let dayOffset = 0; dayOffset < 9; dayOffset++) {
            const checkDate = new Date(today);
            checkDate.setDate(today.getDate() + dayOffset);
            const dayOfWeek = this.getDayOfWeek(checkDate);
            if (task.repeat[dayOfWeek]) {
                const bookingDate = new Date(checkDate);
                const executionDate = new Date(bookingDate);
                executionDate.setDate(bookingDate.getDate() - task.execute.day);
                executionDate.setHours(executeHours, executeMinutes, 0, 0);
                if (executionDate.getTime() > currentTime.getTime()) {
                    return executionDate.getTime();
                }
            }
        }
        return null;
    }
    static calculateSingleTaskTime(task, currentTime, executeHours, executeMinutes) {
        const bookingDate = this.parseDate(task.target.date);
        const executionDate = new Date(bookingDate);
        executionDate.setDate(bookingDate.getDate() - task.execute.day);
        executionDate.setHours(executeHours, executeMinutes, 0, 0);
        if (executionDate.getTime() > currentTime.getTime()) {
            return executionDate.getTime();
        }
        return null;
    }
}
exports.TimeUtils = TimeUtils;
class TaskThread {
    constructor(task, userId, executeCallback, destroyCallback, updateTaskStatusCallback, preLoginCallback) {
        this.isDestroyed = false;
        this.task = task;
        this.userId = userId;
        this.executeCallback = executeCallback;
        this.destroyCallback = destroyCallback;
        this.updateTaskStatusCallback = updateTaskStatusCallback;
        this.preLoginCallback = preLoginCallback;
        this.initializeTaskStatus().then(() => {
            this.scheduleNextExecution();
        }).catch((error) => {
            log_1.ServiceLogger.error('任务初始化失败', {
                taskId: this.getTaskId(),
                userId: this.userId,
                error: error.message
            });
        });
    }
    getTaskId() {
        return this.task.task_id || '';
    }
    getUserId() {
        return this.userId;
    }
    getTask() {
        return { ...this.task };
    }
    async initializeTaskStatus() {
        if (!this.task.status) {
            this.task.status = 'pending';
            this.task.createdAt = new Date().toISOString();
            this.task.updatedAt = new Date().toISOString();
            try {
                await this.updateTaskStatusCallback(this.userId, this.task);
            }
            catch (error) {
                log_1.ServiceLogger.error('初始化任务状态失败', {
                    taskId: this.getTaskId(),
                    userId: this.userId,
                    error: error.message
                });
            }
        }
    }
    updateTask(newTask) {
        if (this.isDestroyed)
            return;
        this.task = newTask;
        this.clearTimer();
        this.scheduleNextExecution();
        log_1.ServiceLogger.info(`任务线程已更新`, {
            taskId: this.getTaskId(),
            userId: this.userId
        });
    }
    scheduleNextExecution() {
        if (this.isDestroyed || !this.task.active)
            return;
        const nextExecutionTime = TimeUtils.calculateNextExecutionTime(this.task);
        if (nextExecutionTime === null) {
            log_1.ServiceLogger.info(`任务无需再执行，销毁线程`, {
                taskId: this.getTaskId(),
                userId: this.userId
            });
            this.destroy();
            return;
        }
        const delay = nextExecutionTime - Date.now();
        const long_wait = 24 * 60 * 60 * 1000;
        const pre_login = 10 * 60 * 1000;
        if (delay <= 0) {
            this.executeTask();
        }
        else if (delay > long_wait + pre_login) {
            this.timer = setTimeout(() => {
                this.scheduleNextExecution();
            }, long_wait);
            log_1.ServiceLogger.info(`任务延迟超过1小时，设置中间检查点`, {
                taskId: this.getTaskId(),
                userId: this.userId,
                nextExecution: new Date(nextExecutionTime).toLocaleString(),
                nextCheck: new Date(Date.now() + 1 * 60 * 60 * 1000).toLocaleString()
            });
        }
        else if (delay > pre_login) {
            this.timer = setTimeout(() => {
                this.executeTask();
            }, delay - pre_login);
            if (this.timer.unref) {
                this.timer.unref();
            }
            log_1.ServiceLogger.info(`任务线程已调度`, {
                taskId: this.getTaskId(),
                userId: this.userId,
                nextExecution: new Date(nextExecutionTime).toLocaleString(),
                delay: Math.round(delay / 1000) + '秒'
            });
        }
        else {
            this.timer = setTimeout(() => {
                this.executeTask();
            }, delay);
            if (this.timer.unref) {
                this.timer.unref();
            }
            this.preLoginCallback(this.userId);
            log_1.ServiceLogger.info(`任务线程已调度，已进行预登录`, {
                taskId: this.getTaskId(),
                userId: this.userId,
                nextExecution: new Date(nextExecutionTime).toLocaleString(),
                delay: Math.round(delay / 1000) + '秒'
            });
        }
    }
    async executeTask() {
        if (this.isDestroyed)
            return;
        const executionTime = new Date().toISOString();
        try {
            this.task.status = 'executing';
            this.task.updatedAt = executionTime;
            await this.updateTaskStatusCallback(this.userId, this.task);
            log_1.ServiceLogger.info(`开始执行任务`, {
                taskId: this.getTaskId(),
                userId: this.userId,
                taskType: this.task.type
            });
            let success = false;
            if (this.task.repetive) {
                const task = this.task;
                task.target.date = TimeUtils.getDateWithOffset(task.execute.day);
                success = await this.executeCallback(this.userId, task);
            }
            else {
                success = await this.executeCallback(this.userId, this.task);
            }
            this.task.status = success ? 'success' : 'failed';
            this.task.lastExecutionTime = executionTime;
            if (!this.task.lastExecutionResult) {
                this.task.lastExecutionResult = {
                    success,
                    message: success ? '任务执行成功' : '任务执行失败',
                    timestamp: executionTime
                };
            }
            else {
                this.task.lastExecutionResult.success = success;
                this.task.lastExecutionResult.timestamp = executionTime;
            }
            this.task.updatedAt = new Date().toISOString();
            await this.updateTaskStatusCallback(this.userId, this.task);
            if (success) {
                log_1.ServiceLogger.info(`任务执行成功`, {
                    taskId: this.getTaskId(),
                    userId: this.userId
                });
            }
            else {
                log_1.ServiceLogger.error(`任务执行失败`, {
                    taskId: this.getTaskId(),
                    userId: this.userId
                });
            }
            if (this.task.repetive) {
                this.task.status = 'pending';
                this.task.updatedAt = new Date().toISOString();
                await this.updateTaskStatusCallback(this.userId, this.task);
                this.scheduleNextExecution();
            }
            else {
                log_1.ServiceLogger.info(`单次任务执行完成，销毁线程`, {
                    taskId: this.getTaskId(),
                    userId: this.userId
                });
                this.destroy();
            }
        }
        catch (error) {
            this.task.status = 'failed';
            this.task.lastExecutionTime = executionTime;
            this.task.lastExecutionResult = {
                success: false,
                message: `任务执行异常: ${error.message}`,
                timestamp: executionTime
            };
            this.task.updatedAt = new Date().toISOString();
            try {
                await this.updateTaskStatusCallback(this.userId, this.task);
            }
            catch (updateError) {
                log_1.ServiceLogger.error(`更新任务状态失败`, {
                    taskId: this.getTaskId(),
                    userId: this.userId,
                    error: updateError.message
                });
            }
            log_1.ServiceLogger.error(`任务执行异常`, {
                taskId: this.getTaskId(),
                userId: this.userId,
                error: error.message
            });
            if (this.task.repetive) {
                this.task.status = 'pending';
                this.task.updatedAt = new Date().toISOString();
                try {
                    await this.updateTaskStatusCallback(this.userId, this.task);
                }
                catch (updateError) {
                    log_1.ServiceLogger.error(`重置任务状态失败`, {
                        taskId: this.getTaskId(),
                        userId: this.userId,
                        error: updateError.message
                    });
                }
                this.scheduleNextExecution();
            }
            else {
                this.destroy();
            }
        }
    }
    clearTimer() {
        if (this.timer) {
            clearTimeout(this.timer);
            this.timer = undefined;
        }
    }
    destroy() {
        if (this.isDestroyed)
            return;
        this.isDestroyed = true;
        this.clearTimer();
        this.destroyCallback(this.getTaskId());
        log_1.ServiceLogger.info(`任务线程已销毁`, {
            taskId: this.getTaskId(),
            userId: this.userId
        });
    }
    isDestroyed_() {
        return this.isDestroyed;
    }
}
exports.TaskThread = TaskThread;
class TaskPool {
    constructor(executeCallback, updateTaskStatusCallback, preLoginCallback) {
        this.threads = new Map();
        this.executeCallback = executeCallback;
        this.updateTaskStatusCallback = updateTaskStatusCallback;
        this.preLoginCallback = preLoginCallback;
        log_1.ServiceLogger.info('任务池已初始化');
    }
    createTask(userId, task) {
        if (!task.task_id) {
            log_1.ServiceLogger.error('任务缺少task_id', { userId, task });
            return false;
        }
        if (task.status === "success" || task.status === "failed")
            return false;
        const threadId = task.task_id;
        if (this.threads.has(threadId)) {
            this.deleteTask(threadId);
        }
        try {
            const thread = new TaskThread(task, userId, this.executeCallback, (destroyedThreadId) => {
                this.threads.delete(destroyedThreadId);
            }, this.updateTaskStatusCallback, this.preLoginCallback);
            this.threads.set(threadId, thread);
            log_1.ServiceLogger.info('任务线程已创建', {
                threadId,
                userId,
                taskType: task.type,
                repetive: task.repetive
            });
            return true;
        }
        catch (error) {
            log_1.ServiceLogger.error('创建任务线程失败', {
                threadId,
                userId,
                error: error.message
            });
            return false;
        }
    }
    queryTask(taskId) {
        return this.threads.get(taskId) || null;
    }
    queryUserTasks(userId) {
        const userTasks = [];
        for (const thread of this.threads.values()) {
            if (thread.getUserId() === userId) {
                userTasks.push(thread);
            }
        }
        return userTasks;
    }
    updateTask(taskId, newTask) {
        const thread = this.threads.get(taskId);
        if (!thread) {
            log_1.ServiceLogger.warn('尝试更新不存在的任务线程', { taskId });
            return false;
        }
        try {
            thread.updateTask(newTask);
            log_1.ServiceLogger.info('任务线程已更新', { taskId });
            return true;
        }
        catch (error) {
            log_1.ServiceLogger.error('更新任务线程失败', {
                taskId,
                error: error.message
            });
            return false;
        }
    }
    deleteTask(taskId) {
        const thread = this.threads.get(taskId);
        if (!thread) {
            log_1.ServiceLogger.warn('尝试删除不存在的任务线程', { taskId });
            return false;
        }
        try {
            thread.destroy();
            this.threads.delete(taskId);
            log_1.ServiceLogger.info('任务线程已删除', { taskId });
            return true;
        }
        catch (error) {
            log_1.ServiceLogger.error('删除任务线程失败', {
                taskId,
                error: error.message
            });
            return false;
        }
    }
    deleteUserTasks(userId) {
        let deletedCount = 0;
        const threadsToDelete = [];
        for (const [threadId, thread] of this.threads.entries()) {
            if (thread.getUserId() === userId) {
                threadsToDelete.push(threadId);
            }
        }
        for (const threadId of threadsToDelete) {
            if (this.deleteTask(threadId)) {
                deletedCount++;
            }
        }
        log_1.ServiceLogger.info('用户任务线程批量删除完成', {
            userId,
            deletedCount,
            totalAttempted: threadsToDelete.length
        });
        return deletedCount;
    }
    getStats() {
        const stats = {
            totalThreads: this.threads.size,
            activeThreads: 0,
            userTaskCounts: {},
            taskTypes: {}
        };
        for (const thread of this.threads.values()) {
            if (!thread.isDestroyed_()) {
                stats.activeThreads++;
            }
            const userId = thread.getUserId();
            const taskType = thread.getTask().type;
            stats.userTaskCounts[userId] = (stats.userTaskCounts[userId] || 0) + 1;
            stats.taskTypes[taskType] = (stats.taskTypes[taskType] || 0) + 1;
        }
        return stats;
    }
    getAllTasksInfo() {
        const tasksInfo = [];
        for (const thread of this.threads.values()) {
            const task = thread.getTask();
            tasksInfo.push({
                taskId: thread.getTaskId(),
                userId: thread.getUserId(),
                taskType: task.type,
                repetive: task.repetive,
                active: task.active,
                isDestroyed: thread.isDestroyed_()
            });
        }
        return tasksInfo;
    }
    cleanup() {
        let cleanedCount = 0;
        const threadsToDelete = [];
        for (const [threadId, thread] of this.threads.entries()) {
            if (thread.isDestroyed_()) {
                threadsToDelete.push(threadId);
            }
        }
        for (const threadId of threadsToDelete) {
            this.threads.delete(threadId);
            cleanedCount++;
        }
        if (cleanedCount > 0) {
            log_1.ServiceLogger.info('任务池清理完成', { cleanedCount });
        }
        return cleanedCount;
    }
    shutdown() {
        log_1.ServiceLogger.info('正在关闭任务池', { totalThreads: this.threads.size });
        for (const thread of this.threads.values()) {
            try {
                thread.destroy();
            }
            catch (error) {
                log_1.ServiceLogger.error('销毁任务线程时出错', {
                    taskId: thread.getTaskId(),
                    error: error.message
                });
            }
        }
        this.threads.clear();
        log_1.ServiceLogger.info('任务池已关闭');
    }
}
exports.TaskPool = TaskPool;
