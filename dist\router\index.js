"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authentication_1 = __importDefault(require("./authentication"));
const user_1 = __importDefault(require("./user"));
const crypto_1 = __importDefault(require("./crypto"));
const log_1 = require("../config/log");
const apiLoggerMiddleWare = (req, res, next) => {
    const requestId = `req_${Date.now()}`;
    const startTime = Date.now();
    req.headers['x-request-id'] = requestId;
    log_1.APILogger.info('收到API请求', {
        requestId,
        method: req.method,
        url: req.url,
        query: req.query,
        headers: {
            'x-forwarded-for': req.headers['x-forwarded-for'],
            'content-type': req.headers['content-type']
        }
    });
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        if (res.statusCode < 400) {
            log_1.APILogger.info('API请求完成', {
                requestId,
                method: req.method,
                url: req.url,
                statusCode: res.statusCode,
                duration: `${duration}ms`
            });
        }
    });
    next();
};
const router = express_1.default.Router();
exports.default = () => {
    router.use(apiLoggerMiddleWare);
    (0, authentication_1.default)(router);
    (0, user_1.default)(router);
    (0, crypto_1.default)(router);
    return router;
};
