# 快速部署指南

## 一键部署脚本

### Docker快速部署

```bash
#!/bin/bash
# 图书馆预约系统一键部署脚本

set -e

echo "🚀 开始部署图书馆预约系统..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，正在安装..."
    curl -fsSL https://get.docker.com | sh
    sudo usermod -aG docker $USER
    echo "✅ Docker安装完成，请重新登录后运行此脚本"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，正在安装..."
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# 创建项目目录
PROJECT_DIR="/opt/lib-api"
sudo mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# 下载项目文件（假设从Git仓库）
if [ ! -d ".git" ]; then
    echo "📥 下载项目代码..."
    git clone <your-repository-url> .
fi

# 配置环境变量
if [ ! -f ".env.production" ]; then
    echo "⚙️  配置环境变量..."
    cp .env.example .env.production
    
    # 生成随机JWT密钥
    JWT_SECRET=$(openssl rand -base64 64)
    sed -i "s/your-super-secret-jwt-key-change-this-in-production/$JWT_SECRET/" .env.production
    
    echo "📝 请编辑 .env.production 文件配置您的域名和其他设置"
    echo "   nano .env.production"
    read -p "配置完成后按回车继续..."
fi

# 生成SSL证书
if [ ! -f "ssl/server.crt" ]; then
    echo "🔐 生成SSL证书..."
    ./scripts/generate-ssl.sh
fi

# 启动服务
echo "🚀 启动服务..."
docker-compose -f docker-compose.prod.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 部署成功！"
    echo "🌐 访问地址: https://localhost:3000"
    echo "📊 健康检查: http://localhost:3000/health"
    echo "📋 查看日志: docker-compose -f docker-compose.prod.yml logs -f"
else
    echo "❌ 部署失败，请检查日志"
    docker-compose -f docker-compose.prod.yml logs
fi
```

### 传统部署脚本

```bash
#!/bin/bash
# 传统方式一键部署脚本

set -e

echo "🚀 开始传统方式部署..."

# 安装Node.js
if ! command -v node &> /dev/null; then
    echo "📦 安装Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# 安装PM2
if ! command -v pm2 &> /dev/null; then
    echo "📦 安装PM2..."
    sudo npm install -g pm2
fi

# 创建用户
if ! id "libapi" &>/dev/null; then
    sudo useradd -m -s /bin/bash libapi
fi

# 部署代码
PROJECT_DIR="/home/<USER>/lib-api"
sudo mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# 下载代码
if [ ! -d ".git" ]; then
    git clone <your-repository-url> .
fi

# 安装依赖
npm ci --only=production

# 构建应用
npm run build

# 配置环境
cp .env.example .env
JWT_SECRET=$(openssl rand -base64 64)
sed -i "s/your-super-secret-jwt-key-change-this-in-production/$JWT_SECRET/" .env

# 生成SSL证书
./scripts/generate-ssl.sh

# 设置权限
sudo chown -R libapi:libapi $PROJECT_DIR

# 启动服务
sudo -u libapi pm2 start dist/index.js --name "lib-api"
sudo -u libapi pm2 startup
sudo -u libapi pm2 save

echo "✅ 部署完成！"
```

## 云服务商特定部署

### 阿里云ECS部署

```bash
# 阿里云安全组配置
# 开放端口：22(SSH), 80(HTTP), 443(HTTPS), 3000(应用)

# 安装Docker
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker

# 部署应用
git clone <repository> /opt/lib-api
cd /opt/lib-api
docker-compose -f docker-compose.prod.yml up -d
```

### 腾讯云CVM部署

```bash
# 腾讯云安全组配置
# 入站规则：TCP:80,443,3000 源：0.0.0.0/0

# Ubuntu系统
sudo apt update
sudo apt install -y docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker

# 部署
cd /opt && git clone <repository> lib-api
cd lib-api && docker-compose -f docker-compose.prod.yml up -d
```

### AWS EC2部署

```bash
# Amazon Linux 2
sudo yum update -y
sudo amazon-linux-extras install docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 部署
git clone <repository> /home/<USER>/lib-api
cd /home/<USER>/lib-api
docker-compose -f docker-compose.prod.yml up -d
```

## 域名和SSL配置

### Let's Encrypt自动化

```bash
#!/bin/bash
# 自动配置Let's Encrypt SSL证书

DOMAIN="yourdomain.com"
EMAIL="<EMAIL>"

# 安装Certbot
sudo apt install -y certbot

# 获取证书
sudo certbot certonly --standalone -d $DOMAIN --email $EMAIL --agree-tos --non-interactive

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem ./ssl/server.crt
sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem ./ssl/server.key
sudo chown $USER:$USER ./ssl/server.*

# 设置自动更新
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -

echo "✅ SSL证书配置完成"
```

### 域名解析配置

```bash
# DNS记录配置示例
# A记录：yourdomain.com -> 服务器IP
# CNAME记录：www.yourdomain.com -> yourdomain.com

# 验证DNS解析
nslookup yourdomain.com
dig yourdomain.com
```

## 监控和告警设置

### 简单监控脚本

```bash
#!/bin/bash
# 服务监控脚本

SERVICE_URL="http://localhost:3000/health"
EMAIL="<EMAIL>"

if ! curl -f $SERVICE_URL > /dev/null 2>&1; then
    echo "Service is down at $(date)" | mail -s "Alert: Library API Down" $EMAIL
    
    # 尝试重启服务
    docker-compose -f docker-compose.prod.yml restart app
fi
```

### 系统资源监控

```bash
#!/bin/bash
# 资源监控脚本

# 检查磁盘使用率
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is ${DISK_USAGE}%" | mail -s "Alert: High Disk Usage" <EMAIL>
fi

# 检查内存使用率
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ $MEM_USAGE -gt 80 ]; then
    echo "Memory usage is ${MEM_USAGE}%" | mail -s "Alert: High Memory Usage" <EMAIL>
fi
```

## 备份和恢复

### 自动备份脚本

```bash
#!/bin/bash
# 自动备份脚本

BACKUP_DIR="/backup/lib-api"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据
tar -czf $BACKUP_DIR/data_$DATE.tar.gz data/
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz logs/
cp .env $BACKUP_DIR/env_$DATE

# 上传到云存储（可选）
# aws s3 cp $BACKUP_DIR/data_$DATE.tar.gz s3://your-backup-bucket/

# 清理旧备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

### 恢复脚本

```bash
#!/bin/bash
# 数据恢复脚本

BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# 停止服务
docker-compose -f docker-compose.prod.yml down

# 恢复数据
tar -xzf $BACKUP_FILE -C ./

# 重启服务
docker-compose -f docker-compose.prod.yml up -d

echo "Restore completed"
```

通过这些脚本和指南，您可以快速部署图书馆预约系统到各种云服务器环境。
