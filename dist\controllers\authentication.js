"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.register = exports.login = exports.auth = void 0;
const dbConnection_1 = require("../config/dbConnection");
const helpers_1 = require("../helpers");
const error_1 = require("../config/error");
const sessionPool_1 = require("../service/sessionPool");
const auth = async (req, res, next) => {
    try {
        if (!req.headers.authorization)
            return next(new error_1.AuthError('缺失鉴权头', 401));
        const token = req.headers.authorization.split(' ')[1];
        if (!token)
            return next(new error_1.AuthError('缺失Token', 401));
        const id = (0, helpers_1.verifyToken)(token);
        if (!id)
            return next(new error_1.AuthError('无效的Token', 401));
        const user = await dbConnection_1.db.findUser({ _id: id });
        if (!user)
            return next(new error_1.AuthError('用户不存在', 401));
        if (user.services?.account) {
            try {
                await sessionPool_1.sp.checkAndRefreshSessionIfNeeded(id, user);
            }
            catch (error) {
                console.warn(`会话检查失败，用户: ${id}, 错误: ${error.message}`);
            }
        }
        req.user = user;
        next();
    }
    catch (err) {
        next(err);
    }
};
exports.auth = auth;
const login = async (req, res, next) => {
    try {
        const { email, password } = req.body;
        if (!email || !password)
            return next(new error_1.AuthError('字段不完整', 400));
        const user = await dbConnection_1.db.findUser({ email });
        if (!user)
            return next(new error_1.AuthError('邮箱或密码错误', 401));
        try {
            if ((0, helpers_1.verifyPassword)(password, user.authentication.password)) {
                res.status(200).json({ success: true, user_id: user._id, token: (0, helpers_1.generateToken)(user) }).end();
                next();
            }
            else {
                return next(new error_1.AuthError('邮箱或密码错误', 401));
            }
        }
        catch (decryptError) {
            return next(new error_1.AuthError('密码格式错误', 400));
        }
    }
    catch (err) {
        next(err);
    }
};
exports.login = login;
const register = async (req, res, next) => {
    try {
        const { name, email, password } = req.body;
        if (!name || !email || !password) {
            return next(new error_1.AuthError('字段不完整', 400));
        }
        const existingUser = await dbConnection_1.db.findUser({ email });
        if (existingUser) {
            return next(new error_1.AuthError('用户已存在', 400));
        }
        try {
            const user = await dbConnection_1.db.createUser({
                name,
                email,
                authentication: {
                    password: (0, helpers_1.authentication)(password),
                }
            });
            const safeUser = ((user) => {
                const { _id, name, email } = user;
                return { name, email, id: _id };
            })(user);
            res.status(200).json({ success: true, ...safeUser }).end();
            next();
        }
        catch (decryptError) {
            return next(new error_1.AuthError('密码格式错误', 400));
        }
    }
    catch (err) {
        next(err);
    }
};
exports.register = register;
