# 房间收藏功能API说明

## 概述

新增了房间收藏功能，允许用户收藏和取消收藏房间。功能包括数据库字段扩展和两个新的API接口。

## 数据库变更

### User 接口扩展

```typescript
interface User {
    services?: {
        account?: LibraryAccount;
        tasks?: BookingTask[];
        sessionLoginTime?: string;
        stars?: string[]; // 新增：收藏的房间ID数组
    }
}
```

### 新增数据库方法

```typescript
async setRoomStar(id: string, room_id: string, starred: boolean): Promise<number>
```

**功能**：设置用户的房间收藏状态
- `id`: 用户ID
- `room_id`: 房间ID
- `starred`: true=添加收藏，false=取消收藏
- 返回值：1=成功，0=失败

**特性**：
- 自动避免重复收藏
- 正确处理取消不存在的收藏
- 完整的错误处理和日志记录

## API 接口

### 1. 获取用户收藏 - GET /user/getstars

**请求**：
```http
GET /user/getstars
Authorization: Bearer JWT_TOKEN
```

**响应**：
```json
{
    "success": true,
    "stars": ["room_001", "room_002", "room_003"]
}
```

**说明**：
- 返回用户收藏的所有房间ID数组
- 如果用户没有收藏任何房间，返回空数组 `[]`
- 需要用户认证

### 2. 设置收藏状态 - POST /user/star

**请求**：
```http
POST /user/star
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
    "room_id": "room_001",
    "starred": true
}
```

**参数说明**：
- `room_id` (string, 必需): 房间ID
- `starred` (boolean, 必需): true=添加收藏，false=取消收藏

**响应**：
```json
{
    "success": true
}
```

**错误响应**：
```json
{
    "success": false,
    "error": "参数不完整或格式错误"
}
```

## 使用示例

### JavaScript/TypeScript 示例

```typescript
// 获取用户收藏列表
async function getUserStars(token: string): Promise<string[]> {
    const response = await fetch('/user/getstars', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    
    const data = await response.json();
    return data.success ? data.stars : [];
}

// 添加收藏
async function addStar(token: string, roomId: string): Promise<boolean> {
    const response = await fetch('/user/star', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            room_id: roomId,
            starred: true
        })
    });
    
    const data = await response.json();
    return data.success;
}

// 取消收藏
async function removeStar(token: string, roomId: string): Promise<boolean> {
    const response = await fetch('/user/star', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            room_id: roomId,
            starred: false
        })
    });
    
    const data = await response.json();
    return data.success;
}

// 切换收藏状态
async function toggleStar(token: string, roomId: string): Promise<boolean> {
    const stars = await getUserStars(token);
    const isStarred = stars.includes(roomId);
    
    return isStarred 
        ? await removeStar(token, roomId)
        : await addStar(token, roomId);
}
```

### cURL 示例

```bash
# 获取收藏列表
curl -X GET "http://localhost:3000/user/getstars" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 添加收藏
curl -X POST "http://localhost:3000/user/star" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"room_id": "room_001", "starred": true}'

# 取消收藏
curl -X POST "http://localhost:3000/user/star" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"room_id": "room_001", "starred": false}'
```

## 兼容性说明

### 原有接口保持不变

**GET /user/info** 接口完全保持原样，不包含 `stars` 字段：

```json
{
    "success": true,
    "user_id": "user_123",
    "name": "用户名",
    "email": "<EMAIL>",
    "services": {
        "account": { ... },
        "tasks": [ ... ]
    }
}
```

### 向后兼容

- 现有用户的 `stars` 字段为 `undefined`，API 会返回空数组 `[]`
- 所有原有功能不受影响
- 客户端可以选择性地使用新的收藏功能

## 错误处理

### 常见错误情况

1. **参数错误**：
   ```json
   {
       "success": false,
       "error": "参数不完整或格式错误"
   }
   ```

2. **用户不存在**：
   ```json
   {
       "success": false,
       "error": "设置收藏失败"
   }
   ```

3. **认证失败**：
   ```json
   {
       "success": false,
       "error": "无效的Token"
   }
   ```

### 错误处理建议

```typescript
async function handleStarOperation(token: string, roomId: string, starred: boolean) {
    try {
        const response = await fetch('/user/star', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ room_id: roomId, starred })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || '操作失败');
        }
        
        return true;
    } catch (error) {
        console.error('收藏操作失败:', error);
        return false;
    }
}
```

## 数据库操作特性

### 智能去重
- 重复添加同一房间的收藏不会产生重复记录
- 取消不存在的收藏不会报错

### 原子操作
- 每次收藏状态变更都是原子操作
- 确保数据一致性

### 日志记录
- 完整记录所有收藏操作
- 包含用户ID、房间ID、操作类型和结果

## 性能考虑

### 数组操作优化
- 使用高效的数组操作方法
- 避免不必要的数据库写入

### 索引建议
- 如果收藏功能使用频繁，可考虑为 `services.stars` 字段建立索引
- 当前实现已经足够高效，适合中等规模的用户群体

## 安全考虑

### 输入验证
- API层面验证 `room_id` 和 `starred` 参数
- 防止恶意输入和类型错误

### 权限控制
- 用户只能管理自己的收藏
- 通过JWT认证确保操作安全

### 数据限制
- 建议在业务层面限制单个用户的最大收藏数量
- 防止恶意用户创建过多收藏记录

房间收藏功能现已完全实现，提供了简洁高效的API接口，同时保持了完全的向后兼容性！
