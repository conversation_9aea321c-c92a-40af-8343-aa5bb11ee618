"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSubscription = exports.spaceDetail = exports.quickSelect = exports.setStar = exports.getStars = exports.getTaskStatus = exports.deleteTasks = exports.getTasks = exports.updateTask = exports.newTask = exports.book = exports.login = exports.getStatus = exports.setAccount = exports.info = void 0;
const dbConnection_1 = require("../config/dbConnection");
const helpers_1 = require("../helpers");
const error_1 = require("../config/error");
const sessionPool_1 = require("../service/sessionPool");
const info = async (req, res, next) => {
    try {
        const user = req.user;
        let { _id, name, email, services } = user;
        if (!services)
            services = {};
        res.status(200).json({ success: true, user_id: _id, name, email, services: {
                ...services, account: null
            } });
        next();
    }
    catch (err) {
        next(err);
    }
};
exports.info = info;
const setAccount = async (req, res, next) => {
    try {
        const user = req.user;
        const { username, password, mobile } = req.body;
        if (!username || !password || !mobile)
            return next(new error_1.UserError('字段不完整', 400));
        const account = {
            username: (0, helpers_1.decryptPassword)(username),
            password: (0, helpers_1.decryptPassword)(password),
            mobile: (0, helpers_1.decryptPassword)(mobile)
        };
        await dbConnection_1.db.setAccount(user._id, account);
        const result = await sessionPool_1.sp.updateUser(user._id);
        if (!result)
            return next(new error_1.UserError('设置账户失败', 500));
        res.status(200).json({ success: true }).end();
        next();
    }
    catch (err) {
        next(err);
    }
};
exports.setAccount = setAccount;
const getStatus = async (req, res, next) => {
    try {
        const user = req.user;
        const result = await sessionPool_1.sp.getSessionStatus(user._id);
        if (!result)
            return next(new error_1.UserError('检查状态失败', 500));
        res.status(200).json({ success: true, exists: result.exists, is_logged_in: result.isLoggedIn });
    }
    catch (err) {
        next(err);
    }
};
exports.getStatus = getStatus;
const login = async (req, res, next) => {
    try {
        const user = req.user;
        const result = await sessionPool_1.sp.startSession(user._id);
        if (!result)
            return next(new error_1.UserError('登录会话失败', 500));
        res.status(200).json({ success: true });
    }
    catch (err) {
        next(err);
    }
};
exports.login = login;
const book = async (req, res, next) => {
    try {
        const user = req.user;
        const { type, space, seat } = req.body;
        if (!type)
            return next(new error_1.UserError('字段不完整', 400));
        switch (type) {
            case 'space':
                if (!space || !space.room_id || !space.day || !space.start_time || !space.end_time)
                    return next(new error_1.UserError('字段不完整', 400));
                const book_space = space;
                if (!await sessionPool_1.sp.executeTask(user._id, { type: 'space', space: book_space }))
                    return next(new error_1.UserError('预约失败', 500));
                res.status(200).json({ success: true });
                break;
            case 'seat':
                if (!seat)
                    return next(new error_1.UserError('字段不完整', 400));
                const book_seat = seat;
                if (!await sessionPool_1.sp.executeTask(user._id, { type: 'seat', seat: book_seat }))
                    return next(new error_1.UserError('预约失败', 500));
                res.status(200).json({ success: true });
                break;
            default:
                return next(new error_1.UserError('未知的预约类型', 400));
        }
    }
    catch (err) {
        next(err);
    }
};
exports.book = book;
const newTask = async (req, res, next) => {
    try {
        const user = req.user;
        const { active, repetive, repeat, type, target, execute } = req.body;
        if (active === undefined || repetive === undefined)
            return next(new error_1.UserError('字段不完整', 400));
        if (repetive && !Array.isArray(repeat))
            return next(new error_1.UserError('字段不完整', 400));
        if (!type || !target || !execute || execute.day === undefined || !execute.time)
            return next(new error_1.UserError('字段不完整', 400));
        const now = new Date().toISOString();
        const task = {
            active,
            repetive,
            repeat,
            type,
            target,
            execute: {
                day: execute.day,
                time: execute.time
            },
            task_id: (0, helpers_1.generateTaskId)(),
            status: 'pending',
            createdAt: now,
            updatedAt: now
        };
        await dbConnection_1.db.addTask(user._id, task);
        const result = await sessionPool_1.sp.updateUser(user._id);
        if (!result)
            return next(new error_1.UserError('添加任务失败', 500));
        res.status(200).json({ success: true, task_id: task.task_id });
    }
    catch (err) {
        next(err);
    }
};
exports.newTask = newTask;
const updateTask = async (req, res, next) => {
    try {
        const user = req.user;
        const { task_id, active } = req.body;
        if (!task_id || active === undefined)
            return next(new error_1.UserError('字段不完整', 400));
        const update = {
            task_id,
            active,
        };
        await dbConnection_1.db.updateTask(user._id, update);
        const result = await sessionPool_1.sp.updateUser(user._id);
        if (!result)
            return next(new error_1.UserError('更新任务失败', 500));
        res.status(200).json({ success: true });
    }
    catch (err) {
        next(err);
    }
};
exports.updateTask = updateTask;
const getTasks = async (req, res, next) => {
    try {
        const user = req.user;
        const tasks = sessionPool_1.sp.getTasks(user._id);
        res.status(200).json({ success: true, tasks });
    }
    catch (err) {
        next(err);
    }
};
exports.getTasks = getTasks;
const deleteTasks = async (req, res, next) => {
    try {
        const user = req.user;
        const { task_id } = req.body;
        if (!task_id)
            return next(new error_1.UserError('任务ID不能为空', 400));
        for (const id of task_id) {
            const deleteResult = await dbConnection_1.db.deleteTask(user._id, id);
            if (deleteResult === 0)
                return next(new error_1.UserError('任务不存在', 404));
        }
        const result = await sessionPool_1.sp.updateUser(user._id);
        if (!result)
            return next(new error_1.UserError('删除任务失败', 500));
        res.status(200).json({ success: true, message: '任务删除成功' });
    }
    catch (err) {
        next(err);
    }
};
exports.deleteTasks = deleteTasks;
const getTaskStatus = async (req, res, next) => {
    try {
        const user = req.user;
        const tasks = sessionPool_1.sp.getTasks(user._id);
        const statusStats = {
            total: tasks.length,
            pending: tasks.filter(t => t.status === 'pending').length,
            executing: tasks.filter(t => t.status === 'executing').length,
            success: tasks.filter(t => t.status === 'success').length,
            failed: tasks.filter(t => t.status === 'failed').length,
            repetitive: tasks.filter(t => t.repetive).length,
            single: tasks.filter(t => !t.repetive).length
        };
        res.status(200).json({ success: true, stats: statusStats });
    }
    catch (err) {
        next(err);
    }
};
exports.getTaskStatus = getTaskStatus;
const getStars = async (req, res, next) => {
    try {
        const user = req.user;
        const stars = user.services?.stars || [];
        res.status(200).json({ success: true, stars });
    }
    catch (err) {
        next(err);
    }
};
exports.getStars = getStars;
const setStar = async (req, res, next) => {
    try {
        const user = req.user;
        const { room_id, starred } = req.body;
        if (!room_id || typeof starred !== 'boolean') {
            return next(new error_1.UserError('参数不完整或格式错误', 400));
        }
        const result = await dbConnection_1.db.setRoomStar(user._id, room_id, starred);
        if (!result) {
            return next(new error_1.UserError('设置收藏失败', 500));
        }
        res.status(200).json({ success: true });
    }
    catch (err) {
        next(err);
    }
};
exports.setStar = setStar;
const quickSelect = async (req, res, next) => {
    try {
        const user = req.user;
        const { date, booking_type } = req.body;
        if (!date || !booking_type)
            return next(new error_1.UserError('字段不完整', 400));
        const result = await sessionPool_1.sp.fetchQuickSelect(user._id, date, booking_type);
        if (!result)
            return next(new error_1.UserError('获取quickSelect失败', 500));
        res.status(200).json({ success: true, quick_select: result });
    }
    catch (err) {
        next(err);
    }
};
exports.quickSelect = quickSelect;
const spaceDetail = async (req, res, next) => {
    try {
        const user = req.user;
        const { room_id } = req.body;
        if (!room_id)
            return next(new error_1.UserError('字段不完整', 400));
        const result = await sessionPool_1.sp.getSpaceDetail(user._id, room_id);
        if (!result)
            return next(new error_1.UserError('获取空间详情失败', 500));
        res.status(200).json({ success: true, space_detail: result });
    }
    catch (err) {
        next(err);
    }
};
exports.spaceDetail = spaceDetail;
const getSubscription = async (req, res, next) => {
    try {
        const user = req.user;
        const result = await sessionPool_1.sp.getSubscription(user._id);
        if (!result)
            return next(new error_1.UserError('获取预约信息失败', 500));
        res.status(200).json({ success: true, subscription: result });
    }
    catch (err) {
        next(err);
    }
};
exports.getSubscription = getSubscription;
