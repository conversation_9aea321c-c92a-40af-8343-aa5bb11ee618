{"name": "is-extglob", "description": "Returns true if a string has an extglob.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/is-extglob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": "jonschlinkert/is-extglob", "bugs": {"url": "https://github.com/jonschlinkert/is-extglob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["bash", "braces", "check", "exec", "extglob", "expression", "glob", "globbing", "globstar", "match", "matches", "pattern", "regex", "regular", "string", "test"]}