"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.env = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const envPath = path_1.default.join(process.cwd(), '.env');
if (fs_1.default.existsSync(envPath)) {
    dotenv_1.default.config({ path: envPath });
}
else {
    console.warn('⚠️  未找到.env文件，使用默认配置');
}
function parseBoolean(value, defaultValue = false) {
    if (!value)
        return defaultValue;
    return value.toLowerCase() === 'true' || value === '1';
}
function parseNumber(value, defaultValue) {
    if (!value)
        return defaultValue;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
}
function parseArray(value, defaultValue = []) {
    if (!value)
        return defaultValue;
    return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
}
function generateJWTSecret() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < 64; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
function validateRequiredEnvVars() {
    const required = ['JWT_SECRET'];
    const missing = required.filter(key => !process.env[key]);
    if (missing.length > 0) {
        console.error('❌ 缺少必需的环境变量:', missing.join(', '));
        console.error('请创建.env文件并设置这些变量，或参考.env.example文件');
        process.exit(1);
    }
}
exports.env = {
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: parseNumber(process.env.PORT, 3000),
    HOST: process.env.HOST || '0.0.0.0',
    FRONTEND_URL: process.env.FRONTEND_URL,
    SSL_CERT: process.env.SSL_CERT,
    SSL_KEY: process.env.SSL_KEY,
    JWT_SECRET: process.env.JWT_SECRET || generateJWTSecret(),
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',
    RSA_PUBLIC_KEY_PATH: process.env.RSA_PUBLIC_KEY_PATH || './keys/public.pem',
    RSA_PRIVATE_KEY_PATH: process.env.RSA_PRIVATE_KEY_PATH || './keys/private.pem',
    DB_PATH: process.env.DB_PATH || './data/users.db',
    DB_BACKUP_PATH: process.env.DB_BACKUP_PATH || './data/backups',
    LOG_LEVEL: process.env.LOG_LEVEL || 'info',
    LOG_DIR: process.env.LOG_DIR || './logs',
    NETWORK_LOG_MAX_SIZE: parseNumber(process.env.NETWORK_LOG_MAX_SIZE, 20971520),
    SESSION_TIMEOUT_HOURS: parseNumber(process.env.SESSION_TIMEOUT_HOURS, 1),
    AUTO_LOGIN_INTERVAL_HOURS: parseNumber(process.env.AUTO_LOGIN_INTERVAL_HOURS, 1),
    MAX_TASK_THREADS: parseNumber(process.env.MAX_TASK_THREADS, 10),
    TASK_RETRY_COUNT: parseNumber(process.env.TASK_RETRY_COUNT, 3),
    TASK_TIMEOUT_MS: parseNumber(process.env.TASK_TIMEOUT_MS, 30000),
    LIBRARY_API_BASE_URL: process.env.LIBRARY_API_BASE_URL || 'http://127.0.0.1:5000',
    PROXY_HOST: process.env.PROXY_HOST,
    PROXY_PORT: parseNumber(process.env.PROXY_PORT, 0) || undefined,
    CORS_ORIGINS: parseArray(process.env.CORS_ORIGINS, ['http://localhost:3000', 'https://localhost:3000']),
    REQUEST_BODY_LIMIT: process.env.REQUEST_BODY_LIMIT || '10mb',
    STATIC_CACHE_MAX_AGE: process.env.STATIC_CACHE_MAX_AGE || (process.env.NODE_ENV === 'production' ? '1d' : '0'),
    HEALTH_CHECK_ENABLED: parseBoolean(process.env.HEALTH_CHECK_ENABLED, true),
    PERFORMANCE_MONITORING: parseBoolean(process.env.PERFORMANCE_MONITORING, false),
    CONSOLE_ADMIN_ENABLED: parseBoolean(process.env.CONSOLE_ADMIN_ENABLED, process.env.NODE_ENV !== 'production'),
    DETAILED_ERRORS: parseBoolean(process.env.DETAILED_ERRORS, process.env.NODE_ENV !== 'production'),
    API_LOG_VERBOSE: parseBoolean(process.env.API_LOG_VERBOSE, false)
};
if (exports.env.NODE_ENV === 'production') {
    validateRequiredEnvVars();
}
if (exports.env.NODE_ENV === 'development') {
    console.log('🔧 环境变量配置加载完成');
    console.log(`   - 运行环境: ${exports.env.NODE_ENV}`);
    console.log(`   - 服务端口: ${exports.env.PORT}`);
    console.log(`   - SSL启用: ${exports.env.SSL_CERT && exports.env.SSL_KEY ? '是' : '否'}`);
    console.log(`   - 控制台管理: ${exports.env.CONSOLE_ADMIN_ENABLED ? '启用' : '禁用'}`);
    if (!process.env.JWT_SECRET) {
        console.warn('⚠️  使用自动生成的JWT密钥，生产环境请设置JWT_SECRET环境变量');
    }
}
exports.default = exports.env;
