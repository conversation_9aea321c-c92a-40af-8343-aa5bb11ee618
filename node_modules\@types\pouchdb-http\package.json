{"name": "@types/pouchdb-http", "version": "6.1.5", "description": "TypeScript definitions for pouchdb-http", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-http", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "spaulg", "url": "https://github.com/spaulg"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-http"}, "scripts": {}, "dependencies": {"@types/pouchdb-adapter-http": "*", "@types/pouchdb-core": "*"}, "typesPublisherContentHash": "080a216fdc131e49260cb4fe9c998a88d53fe0bd5d1cff335315d6d2be3a4384", "typeScriptVersion": "4.5"}