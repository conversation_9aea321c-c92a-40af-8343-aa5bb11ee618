{"name": "@types/pouchdb-adapter-memory", "version": "6.1.6", "description": "TypeScript definitions for pouchdb-adapter-memory", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-memory", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "spaulg", "url": "https://github.com/spaulg"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-adapter-memory"}, "scripts": {}, "dependencies": {"@types/pouchdb-core": "*"}, "typesPublisherContentHash": "2aced5a86aa503623bc2b81e56ef6b1a07bee6dd4f45ac996ddeb763ad911f19", "typeScriptVersion": "4.5"}