"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.consoleAdmin = exports.ConsoleAdmin = void 0;
const readline = __importStar(require("readline"));
const dbConnection_1 = require("../config/dbConnection");
const sessionPool_1 = require("../service/sessionPool");
const log_1 = require("../config/log");
const bcrypt_1 = __importDefault(require("bcrypt"));
class ConsoleAdmin {
    constructor() {
        this.isRunning = false;
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }
    start() {
        if (this.isRunning) {
            console.log('⚠️  控制台管理界面已在运行中');
            return;
        }
        this.isRunning = true;
        this.showWelcome();
        this.showMenu();
        this.waitForCommand();
    }
    stop() {
        if (!this.isRunning)
            return;
        this.isRunning = false;
        this.rl.close();
        console.log('\n👋 控制台管理界面已关闭');
    }
    showWelcome() {
        console.log('\n' + '='.repeat(60));
        console.log('🛠️  图书馆预约系统 - 控制台管理界面');
        console.log('='.repeat(60));
    }
    showMenu() {
        console.log('\n📋 可用指令：');
        console.log('  1. list-users     - 查询所有用户');
        console.log('  2. add-user       - 注册用户');
        console.log('  3. delete-user    - 删除用户');
        console.log('  4. session-status - 查询会话池状态');
        console.log('  5. task-status    - 查询任务池状态');
        console.log('  6. help           - 显示帮助信息');
        console.log('  7. exit           - 退出管理界面');
        console.log('-'.repeat(60));
    }
    waitForCommand() {
        if (!this.isRunning)
            return;
        this.rl.question('\n🔧 请输入指令: ', async (command) => {
            if (!this.isRunning)
                return;
            await this.handleCommand(command.trim());
            if (this.isRunning) {
                this.waitForCommand();
            }
        });
    }
    async handleCommand(command) {
        try {
            switch (command.toLowerCase()) {
                case '1':
                case 'list-users':
                    await this.listUsers();
                    break;
                case '2':
                case 'find-user':
                    await this.findUser();
                    break;
                case '3':
                case 'add-user':
                    await this.addUser();
                    break;
                case '4':
                case 'delete-user':
                    await this.deleteUser();
                    break;
                case '5':
                case 'session-status':
                    await this.showSessionStatus();
                    break;
                case '6':
                case 'task-status':
                    await this.showTaskStatus();
                    break;
                case '7':
                case 'help':
                    this.showMenu();
                    break;
                case '8':
                case 'exit':
                    this.stop();
                    break;
                default:
                    console.log('❌ 未知指令，请输入 help 查看可用指令');
                    break;
            }
        }
        catch (error) {
            console.error('❌ 执行指令时发生错误:', error.message);
            log_1.ServiceLogger.error('控制台管理指令执行失败', {
                command,
                error: error.message
            });
        }
    }
    async listUsers() {
        console.log('\n📊 正在查询所有用户...');
        try {
            const users = await dbConnection_1.db.findAllUsers();
            if (users.length === 0) {
                console.log('📭 暂无用户数据');
                return;
            }
            console.log(`\n👥 共找到 ${users.length} 个用户：`);
            console.log('-'.repeat(80));
            console.log('ID'.padEnd(15) + 'Name'.padEnd(20) + 'Email'.padEnd(30) + 'Created');
            console.log('-'.repeat(80));
            users.forEach(user => {
                const id = (user._id || '').substring(0, 12) + '...';
                const name = user.name.substring(0, 18);
                const email = user.email.substring(0, 28);
                const created = user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A';
                console.log(id.padEnd(15) + name.padEnd(20) + email.padEnd(30) + created);
            });
            console.log('-'.repeat(80));
        }
        catch (error) {
            console.error('❌ 查询用户失败:', error.message);
        }
    }
    async findUser() {
        console.log('\n🔍 查询用户');
        try {
            const identifier = await this.askQuestion('请输入用户ID或邮箱: ');
            if (!identifier.trim()) {
                console.log('❌ 请输入有效的用户ID或邮箱');
                return;
            }
            let user = null;
            if (identifier.includes('@')) {
                user = await dbConnection_1.db.findUser({ email: identifier.trim() });
            }
            else {
                user = await dbConnection_1.db.findUser({ _id: identifier.trim() });
            }
            if (!user) {
                console.log('❌ 未找到指定用户');
                return;
            }
            console.log(`\n找到用户: ${user.name} (${user.email})`);
            console.log(`   用户ID: ${user._id}`);
            console.log(`   用户服务: ${user.services ? JSON.stringify(user.services) : 'N/A'}`);
            console.log(`   创建时间: ${user.createdAt}`);
            console.log(`   更新时间: ${user.updatedAt}`);
        }
        catch (error) {
            console.error('❌ 查询用户失败:', error.message);
        }
    }
    async addUser() {
        console.log('\n➕ 注册新用户');
        try {
            const name = await this.askQuestion('请输入用户名: ');
            if (!name.trim()) {
                console.log('❌ 用户名不能为空');
                return;
            }
            const email = await this.askQuestion('请输入邮箱: ');
            if (!email.trim() || !this.isValidEmail(email)) {
                console.log('❌ 请输入有效的邮箱地址');
                return;
            }
            const password = await this.askQuestion('请输入密码: ');
            if (!password.trim()) {
                console.log('❌ 密码不能为空');
                return;
            }
            const existingUser = await dbConnection_1.db.findUser({ email });
            if (existingUser) {
                console.log('❌ 该邮箱已被注册');
                return;
            }
            const hashedPassword = bcrypt_1.default.hashSync(password.trim(), 10);
            const user = await dbConnection_1.db.createUser({
                name: name.trim(),
                email: email.trim(),
                authentication: {
                    password: hashedPassword
                }
            });
            console.log('✅ 用户注册成功！');
            console.log(`   用户ID: ${user._id}`);
            console.log(`   用户名: ${user.name}`);
            console.log(`   邮箱: ${user.email}`);
        }
        catch (error) {
            console.error('❌ 注册用户失败:', error.message);
        }
    }
    async deleteUser() {
        console.log('\n🗑️  删除用户');
        try {
            const identifier = await this.askQuestion('请输入用户ID或邮箱: ');
            if (!identifier.trim()) {
                console.log('❌ 请输入有效的用户ID或邮箱');
                return;
            }
            let user = null;
            if (identifier.includes('@')) {
                user = await dbConnection_1.db.findUser({ email: identifier.trim() });
            }
            else {
                user = await dbConnection_1.db.findUser({ _id: identifier.trim() });
            }
            if (!user) {
                console.log('❌ 未找到指定用户');
                return;
            }
            console.log(`\n找到用户: ${user.name} (${user.email})`);
            const confirm = await this.askQuestion('确认删除此用户？(y/N): ');
            if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
                console.log('❌ 取消删除操作');
                return;
            }
            const result = await dbConnection_1.db.deleteUser(user._id);
            if (result > 0) {
                sessionPool_1.sp.removeSession(user._id);
                console.log('✅ 用户删除成功！');
            }
            else {
                console.log('❌ 删除用户失败');
            }
        }
        catch (error) {
            console.error('❌ 删除用户失败:', error.message);
        }
    }
    async showSessionStatus() {
        console.log('\n🔐 会话池状态查询');
        try {
            const poolStats = sessionPool_1.sp.getPoolStats();
            const systemStatus = sessionPool_1.sp.getSystemStatus();
            const allSessions = sessionPool_1.sp.getAllSessions();
            console.log('\n📊 会话池统计信息：');
            console.log('-'.repeat(50));
            console.log(`总会话数: ${poolStats.totalSessions}`);
            console.log(`总任务数: ${poolStats.totalTasks}`);
            console.log(`平均每会话任务数: ${poolStats.averageTasksPerSession}`);
            console.log('\n⚙️  系统配置：');
            console.log('-'.repeat(50));
            console.log(`会话超时时间: ${systemStatus.sessionTimeout.timeoutHours} 小时`);
            if (allSessions.length > 0) {
                console.log('\n👥 活跃会话列表：');
                console.log('-'.repeat(60));
                console.log('User ID'.padEnd(20) + 'Login Status'.padEnd(15) + 'Tasks');
                console.log('-'.repeat(60));
                allSessions.forEach(session => {
                    const userId = session.id.substring(0, 18);
                    const loginStatus = session.isLoggedIn ? '✅ 已登录' : '❌ 未登录';
                    const taskCount = session.taskCount.toString();
                    console.log(userId.padEnd(20) + loginStatus.padEnd(15) + taskCount);
                });
                console.log('-'.repeat(60));
            }
            else {
                console.log('\n📭 当前无活跃会话');
            }
        }
        catch (error) {
            console.error('❌ 查询会话池状态失败:', error.message);
        }
    }
    async showTaskStatus() {
        console.log('\n⚡ 任务池状态查询');
        try {
            const taskStats = sessionPool_1.sp.getTaskPoolStats();
            const allTasks = sessionPool_1.sp.getAllTasksInfo();
            console.log('\n📊 任务池统计信息：');
            console.log('-'.repeat(50));
            console.log(`总线程数: ${taskStats.totalThreads}`);
            console.log(`活跃线程数: ${taskStats.activeThreads}`);
            if (Object.keys(taskStats.userTaskCounts).length > 0) {
                console.log('\n👥 用户任务分布：');
                console.log('-'.repeat(40));
                Object.entries(taskStats.userTaskCounts).forEach(([userId, count]) => {
                    const shortUserId = userId.substring(0, 15);
                    console.log(`${shortUserId.padEnd(18)} ${count} 个任务`);
                });
            }
            if (Object.keys(taskStats.taskTypes).length > 0) {
                console.log('\n📋 任务类型分布：');
                console.log('-'.repeat(40));
                Object.entries(taskStats.taskTypes).forEach(([type, count]) => {
                    console.log(`${type.padEnd(10)} ${count} 个任务`);
                });
            }
            if (allTasks.length > 0) {
                console.log('\n📋 任务详情：');
                console.log('-'.repeat(80));
                console.log('Task ID'.padEnd(15) + 'User ID'.padEnd(15) + 'Type'.padEnd(10) + 'Repetive'.padEnd(10) + 'Active');
                console.log('-'.repeat(80));
                allTasks.forEach(task => {
                    const taskId = task.taskId.substring(0, 13);
                    const userId = task.userId.substring(0, 13);
                    const type = task.taskType;
                    const repetive = task.repetive ? '✅' : '❌';
                    const active = task.active ? '✅' : '❌';
                    console.log(taskId.padEnd(15) + userId.padEnd(15) + type.padEnd(10) + repetive.padEnd(10) + active);
                });
                console.log('-'.repeat(80));
            }
            else {
                console.log('\n📭 当前无任务');
            }
        }
        catch (error) {
            console.error('❌ 查询任务池状态失败:', error.message);
        }
    }
    askQuestion(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer);
            });
        });
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}
exports.ConsoleAdmin = ConsoleAdmin;
exports.consoleAdmin = new ConsoleAdmin();
