"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LibrarySession = void 0;
const jsdom_1 = require("jsdom");
const date_fns_1 = require("date-fns");
const log_1 = require("../config/log");
const networkLogger_1 = require("../utils/networkLogger");
const tough_cookie_1 = require("tough-cookie");
const crypto_1 = __importDefault(require("crypto"));
const got_1 = __importDefault(require("got"));
let ZJUAM_LOGIN_URL;
let ZJUAM_PUBKEY_URL;
let BOOKING_USER_URL;
let BOOKING_SELECT_TIME_URL;
let BOOKING_TIME_URL;
let BOOKING_DETAIL_URL;
let BOOKING_SUBSCRIPTION_URL;
let BOOKING_CONFIRM_URL;
if (process.env.NODE_ENV === 'production') {
    ZJUAM_LOGIN_URL = "http://zjuam.zju.edu.cn/cas/login?service=https%3A%2F%2Fbooking.lib.zju.edu.cn%2Fapi%2Fcas%2Fcas";
    ZJUAM_PUBKEY_URL = 'https://zjuam.zju.edu.cn/cas/v2/getPubKey';
    BOOKING_USER_URL = 'https://booking.lib.zju.edu.cn/api/cas/user';
    BOOKING_SELECT_TIME_URL = 'https://booking.lib.zju.edu.cn/reserve/index/quickSelect';
    BOOKING_TIME_URL = 'https://booking.lib.zju.edu.cn/api/Seminar/v1seminar';
    BOOKING_DETAIL_URL = 'https://booking.lib.zju.edu.cn/reserve/index/detail';
    BOOKING_SUBSCRIPTION_URL = 'https://booking.lib.zju.edu.cn/api/index/subscribe';
    BOOKING_CONFIRM_URL = 'http://booking.lib.zju.edu.cn/reserve/index/confirm';
}
else {
    ZJUAM_LOGIN_URL = "http://127.0.0.1:5000/cas/login?service=https%3A%2F%2Fbooking.lib.zju.edu.cn%2Fapi%2Fcas%2Fcas";
    ZJUAM_PUBKEY_URL = 'http://127.0.0.1:5000/cas/v2/getPubKey';
    BOOKING_USER_URL = 'http://127.0.0.1:5000/api/cas/user';
    BOOKING_SELECT_TIME_URL = 'http://127.0.0.1:5000/reserve/index/quickSelect';
    BOOKING_TIME_URL = 'http://127.0.0.1:5000/api/Seminar/v1seminar';
    BOOKING_DETAIL_URL = 'http://127.0.0.1:5000/reserve/index/detail';
    BOOKING_SUBSCRIPTION_URL = 'http://127.0.0.1:5000/api/index/subscribe';
    BOOKING_CONFIRM_URL = 'http://127.0.0.1:5000/reserve/index/confirm';
}
const formattedDate = () => {
    const today = new Date();
    return (0, date_fns_1.format)(today, 'yyyy-MM-dd');
};
class LibrarySession {
    constructor(account) {
        this.cookieJar = new tough_cookie_1.CookieJar();
        this.instance = got_1.default.extend({
            headers: this.mergeHeaders(),
            timeout: {
                request: 10000
            },
            retry: {
                limit: 0
            },
            hooks: {
                beforeRequest: [
                    (options) => {
                        (0, networkLogger_1.logNetworkEvent)('REQUEST_START', {
                            url: options.url,
                            method: options.method,
                            headers: options.headers,
                            body: options.body
                        });
                    }
                ],
                afterResponse: [
                    (response) => {
                        (0, networkLogger_1.logNetworkEvent)('RESPONSE_END', {
                            url: response.url,
                            status: response.statusCode,
                            headers: response.headers,
                            body: response.body
                        });
                        return response;
                    }
                ]
            },
            cookieJar: this.cookieJar
        });
        this.account = account;
        this.isLoggedIn = false;
        this.isLogging = false;
        this.bookingAuthorization = '';
        (0, networkLogger_1.logNetworkEvent)('SESSION_CREATED', {
            username: account.username,
            timestamp: new Date().toISOString()
        });
    }
    mergeHeaders(headers) {
        return {
            ...headers,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36'
        };
    }
    async login() {
        if (this.isLogging)
            return false;
        try {
            this.isLogging = true;
            (0, networkLogger_1.logNetworkEvent)('LOGIN_START', {
                username: this.account.username,
                timestamp: new Date().toISOString()
            });
            const zjuam_login_res = await this.instance.get(ZJUAM_LOGIN_URL).text();
            const zjuam_pubkey_res = await this.instance.get(ZJUAM_PUBKEY_URL).json();
            const password_bytes = Buffer.from(this.account.password, 'ascii');
            const password_int = BigInt('0x' + password_bytes.toString('hex'));
            const e_int = BigInt('0x' + zjuam_pubkey_res.exponent);
            const M_int = BigInt('0x' + zjuam_pubkey_res.modulus);
            const result_int = password_int ** e_int % M_int;
            const encrypt_password = result_int.toString(16).padStart(128, '0');
            const dom = new jsdom_1.JSDOM(zjuam_login_res);
            const execution = dom.window.document.querySelector('input[name="execution"]')?.getAttribute('value');
            const zjuam_login_data = {
                'username': this.account.username,
                'password': encrypt_password,
                '_eventId': 'submit',
                'execution': execution || '',
                'authcode': '',
            };
            const zjuam_login_res_ = await this.instance.post(ZJUAM_LOGIN_URL, {
                form: zjuam_login_data,
                headers: this.mergeHeaders({
                    'Content-Type': 'application/x-www-form-urlencoded'
                })
            });
            const cas_index = zjuam_login_res_.url.indexOf('cas=');
            if (cas_index === -1)
                throw new Error('CAS获取失败');
            const booking_user_data = {
                'cas': zjuam_login_res_.url.substring(cas_index + 4)
            };
            const booking_user_res = await this.instance.post(BOOKING_USER_URL, {
                form: booking_user_data,
                headers: this.mergeHeaders({
                    'Content-Type': 'application/x-www-form-urlencoded'
                })
            }).json();
            if (booking_user_res.code !== 1 || !booking_user_res.member.token) {
                throw new Error('TOKEN获取失败');
            }
            this.bookingAuthorization = 'bearer' + booking_user_res.member.token;
            this.isLoggedIn = true;
            log_1.SessionLogger.info(`登录成功: ${this.account.username}`);
            (0, networkLogger_1.logNetworkEvent)('LOGIN_SUCCESS', {
                username: this.account.username,
                timestamp: new Date().toISOString(),
                hasToken: !!this.bookingAuthorization
            });
            this.isLogging = false;
            return true;
        }
        catch (err) {
            log_1.SessionLogger.error(`登录失败: ${err.message}`, { username: this.account.username, stack: err.stack });
            this.isLogging = false;
            this.isLoggedIn = false;
            (0, networkLogger_1.logNetworkEvent)('LOGIN_FAILED', {
                username: this.account.username,
                timestamp: new Date().toISOString(),
                error: err.message
            });
            return false;
        }
    }
    async testIfLoggedIn() {
        if (!this.isLoggedIn)
            return false;
        try {
            const booking_time_data = {
                'area': "53",
                'room': "147",
                'authorization': this.bookingAuthorization,
            };
            const booking_time_res = await this.instance.post(BOOKING_TIME_URL, {
                json: booking_time_data,
                headers: this.mergeHeaders({
                    'Content-Type': 'application/json',
                    'Authorization': this.bookingAuthorization
                })
            }).json();
            if (booking_time_res.code && booking_time_res.code === 1) {
                return true;
            }
            else {
                this.isLoggedIn = false;
                return false;
            }
        }
        catch (err) {
            log_1.SessionLogger.error(`测试登录状态失败: ${err.message}`, { username: this.account.username, stack: err.stack });
            this.isLoggedIn = false;
            return false;
        }
    }
    async getQuickSelect(date = formattedDate(), booking_type = '2') {
        if (!this.isLoggedIn) {
            throw new Error('未登录');
        }
        try {
            const quick_select_data = {
                'id': booking_type,
                'date': date,
                'authorization': this.bookingAuthorization || '',
                'members': 0
            };
            const quick_select_resp = await this.instance.post(BOOKING_SELECT_TIME_URL, {
                json: quick_select_data,
                headers: this.mergeHeaders({
                    'Content-Type': 'application/json',
                    'Authorization': this.bookingAuthorization
                })
            }).json();
            this.quickSelectData = quick_select_resp.data;
            if (!this.quickSelectData)
                throw new Error('获取quickSelect失败');
            const data = this.quickSelectData;
            const result = [];
            for (const premise in data.premises) {
                const premise_data = data.premises[premise];
                premise_data.children = data.storey.filter((storey) => storey.parentId === premise_data.id);
                premise_data.children.forEach((storey) => {
                    storey.rooms = data.area.filter((area) => area.parentId === storey.id);
                });
                result.push(premise_data);
            }
            return result;
        }
        catch (err) {
            log_1.SessionLogger.error(`获取列表失败: ${err.message}`, { username: this.account.username, stack: err.stack });
            throw new Error('获取列表失败');
        }
    }
    async getSpaceTimeSlots(premise_id, area_id) {
        if (!this.isLoggedIn || !this.bookingAuthorization)
            return [];
        try {
            const booking_time_data = {
                'area': premise_id,
                'room': area_id,
                'authorization': this.bookingAuthorization,
            };
            const booking_time_res = (await this.instance.post(BOOKING_TIME_URL, {
                json: booking_time_data,
                headers: this.mergeHeaders({
                    'Content-Type': 'application/json',
                    'Authorization': this.bookingAuthorization
                })
            }).json()).data;
            const booking_time_slots = [];
            if (booking_time_res.list && booking_time_res.list.length > 0) {
                for (const date_info of booking_time_res.list) {
                    const { date, info: { Fully_Booked, startTimeStamp, endTimeStamp } } = date_info;
                    booking_time_slots.push({
                        date,
                        start_time: startTimeStamp.slice(11, 16),
                        end_time: endTimeStamp.slice(11, 16),
                        isBookable: Fully_Booked === '0'
                    });
                }
            }
            else if (booking_time_res.date && booking_time_res.date.length > 0) {
                for (const date of booking_time_res.date) {
                    booking_time_slots.push({
                        date,
                        start_time: '08:30',
                        end_time: '22:30',
                        isBookable: true
                    });
                }
            }
            return booking_time_slots;
        }
        catch (err) {
            log_1.SessionLogger.error(`获取空间可用时间段失败: ${err.message}`, { username: this.account.username, stack: err.stack });
            return [];
        }
    }
    async getSpaceDetail(room_id, id = '2') {
        if (!this.isLoggedIn || !this.bookingAuthorization)
            throw new Error('会话未登录');
        try {
            const space_detail_data = {
                'areaId': room_id,
                'authorization': this.bookingAuthorization,
                'id': id
            };
            const space_detail_res = (await this.instance.post(BOOKING_DETAIL_URL, {
                json: space_detail_data,
                headers: this.mergeHeaders({
                    'Content-Type': 'application/json',
                    'Authorization': this.bookingAuthorization
                })
            }).json()).data;
            if (!space_detail_res)
                throw new Error('获取空间详情失败');
            log_1.SessionLogger.info(`获取空间详情成功: ${space_detail_res.name}`, { username: this.account.username, room_id });
            return space_detail_res;
        }
        catch (err) {
            log_1.SessionLogger.error(`获取空间详情失败: ${err.message}`, { username: this.account.username, stack: err.stack });
            throw new Error('获取空间详情失败');
        }
    }
    async getSubscription() {
        if (!this.isLoggedIn || !this.bookingAuthorization)
            throw new Error('会话未登录');
        try {
            const subscription_data = {
                'authorization': this.bookingAuthorization,
            };
            const subscription_res = (await this.instance.post(BOOKING_SUBSCRIPTION_URL, {
                json: subscription_data,
                headers: this.mergeHeaders({
                    'Content-Type': 'application/json',
                    'Authorization': this.bookingAuthorization
                })
            }).json()).data;
            if (!subscription_res)
                throw new Error('获取预约信息失败');
            log_1.SessionLogger.info(`获取预约信息成功: ${subscription_res.length} 条`, { username: this.account.username });
            return subscription_res;
        }
        catch (err) {
            log_1.SessionLogger.error(`获取预约信息失败: ${err.message}`, { username: this.account.username, stack: err.stack });
            throw new Error('获取预约信息失败');
        }
    }
    async bookSpace(room_id, day, start_time, end_time, title = '单人研习', title_id = '1') {
        try {
            if (!this.isLoggedIn || !this.bookingAuthorization)
                throw Error('会话未登录');
            (0, networkLogger_1.logNetworkEvent)('SPACE_BOOKING_START', {
                username: this.account.username,
                room_id,
                day,
                start_time,
                end_time,
                title,
                timestamp: new Date().toISOString()
            });
            const date = (0, date_fns_1.format)(new Date(), 'yyyyMMdd');
            const key = (date + date.split('').reverse().join('')).substring(0, 16);
            const iv = 'ZZWBKJ_ZHIHUAWEI';
            const plaintext = JSON.stringify({
                day,
                start_time,
                end_time,
                title,
                content: "1",
                mobile: this.account.mobile,
                room: room_id,
                open: "1",
                file_name: "",
                file_url: "",
                titleId: title_id,
                id: "2"
            });
            const cipher = crypto_1.default.createCipheriv('aes-128-cbc', Buffer.from(key), Buffer.from(iv));
            let encrypted = cipher.update(plaintext, 'utf8', 'base64');
            encrypted += cipher.final('base64');
            const booking_confirm_data = {
                aesjson: encrypted,
                authorization: this.bookingAuthorization
            };
            const booking_confirm_result = await this.instance.post(BOOKING_CONFIRM_URL, {
                json: booking_confirm_data,
                headers: this.mergeHeaders({
                    'Content-Type': 'application/json',
                    'Authorization': this.bookingAuthorization
                })
            }).json();
            log_1.SessionLogger.debug('空间预约API响应', {
                userId: this.account.username,
                response: booking_confirm_result
            });
            if (booking_confirm_result.code === 1) {
                log_1.SessionLogger.info(`空间预约成功: ${booking_confirm_result.msg}`, { username: this.account.username });
                (0, networkLogger_1.logNetworkEvent)('SPACE_BOOKING_SUCCESS', {
                    username: this.account.username,
                    room_id,
                    day,
                    start_time,
                    end_time,
                    message: booking_confirm_result.msg,
                    timestamp: new Date().toISOString()
                });
                return true;
            }
            else {
                (0, networkLogger_1.logNetworkEvent)('SPACE_BOOKING_FAILED', {
                    username: this.account.username,
                    room_id,
                    day,
                    start_time,
                    end_time,
                    error: booking_confirm_result.msg,
                    timestamp: new Date().toISOString()
                });
                throw Error(booking_confirm_result.msg);
            }
        }
        catch (err) {
            log_1.SessionLogger.error(`空间预约失败: ${err.message}`, { username: this.account.username, stack: err.stack });
            (0, networkLogger_1.logNetworkEvent)('SPACE_BOOKING_ERROR', {
                username: this.account.username,
                room_id,
                day,
                start_time,
                end_time,
                error: err.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }
}
exports.LibrarySession = LibrarySession;
