"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.simulateUserLogin = simulateUserLogin;
exports.simulateAPIRequest = simulateAPIRequest;
exports.simulateBookingTask = simulateBookingTask;
exports.simulateSystemMonitoring = simulateSystemMonitoring;
exports.runLoggerDemo = runLoggerDemo;
const log_1 = require("../config/log");
async function simulateUserLogin(userId, password) {
    log_1.ServiceLogger.info('开始用户登录流程', { userId, timestamp: new Date().toISOString() });
    try {
        log_1.SessionLogger.debug('验证用户输入', { userId, hasPassword: !!password });
        if (!userId || !password) {
            log_1.SessionLogger.warn('用户输入验证失败', {
                userId,
                missingFields: [!userId && 'userId', !password && 'password'].filter(Boolean)
            });
            return { success: false, message: '用户名或密码不能为空' };
        }
        log_1.DatabaseLogger.info('查询用户信息', { userId });
        await new Promise(resolve => setTimeout(resolve, 100));
        const isValidPassword = password === 'correct_password';
        if (!isValidPassword) {
            log_1.SessionLogger.warn('密码验证失败', { userId, attemptTime: new Date().toISOString() });
            return { success: false, message: '密码错误' };
        }
        const sessionId = `sess_${Date.now()}`;
        log_1.SessionLogger.info('创建用户会话', {
            userId,
            sessionId,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        });
        log_1.ServiceLogger.info('用户登录成功', {
            userId,
            sessionId,
            loginTime: new Date().toISOString(),
            userAgent: 'Demo-Client/1.0'
        });
        return { success: true, sessionId, message: '登录成功' };
    }
    catch (error) {
        log_1.ServiceLogger.error('用户登录过程中发生错误', {
            userId,
            error: error.message,
            stack: error.stack
        });
        return { success: false, message: '系统错误，请稍后重试' };
    }
}
async function simulateAPIRequest(method, endpoint, userId) {
    const requestId = `req_${Date.now()}`;
    const startTime = Date.now();
    log_1.APILogger.info('收到API请求', {
        requestId,
        method,
        endpoint,
        userId,
        timestamp: new Date().toISOString()
    });
    try {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 200 + 50));
        const duration = Date.now() - startTime;
        const statusCode = Math.random() > 0.1 ? 200 : 500;
        if (statusCode === 200) {
            log_1.APILogger.info('API请求处理成功', {
                requestId,
                method,
                endpoint,
                statusCode,
                duration: `${duration}ms`,
                userId
            });
        }
        else {
            log_1.APILogger.error('API请求处理失败', {
                requestId,
                method,
                endpoint,
                statusCode,
                duration: `${duration}ms`,
                userId,
                error: '内部服务器错误'
            });
        }
        return { statusCode, duration };
    }
    catch (error) {
        const duration = Date.now() - startTime;
        log_1.APILogger.error('API请求异常', {
            requestId,
            method,
            endpoint,
            duration: `${duration}ms`,
            userId,
            error: error.message,
            stack: error.stack
        });
        return { statusCode: 500, duration };
    }
}
async function simulateBookingTask(taskId, userId, roomId) {
    log_1.TaskLogger.info('开始处理预约任务', { taskId, userId, roomId });
    try {
        log_1.DatabaseLogger.debug('检查房间可用性', { taskId, roomId });
        await new Promise(resolve => setTimeout(resolve, 50));
        const isRoomAvailable = Math.random() > 0.3;
        if (!isRoomAvailable) {
            log_1.TaskLogger.warn('房间不可用', { taskId, roomId, reason: '已被预约' });
            return { success: false, message: '房间已被预约' };
        }
        log_1.DatabaseLogger.info('创建预约记录', { taskId, userId, roomId });
        await new Promise(resolve => setTimeout(resolve, 100));
        const bookingId = `booking_${Date.now()}`;
        log_1.TaskLogger.debug('发送预约确认通知', { taskId, bookingId, userId });
        log_1.TaskLogger.info('预约任务处理完成', {
            taskId,
            bookingId,
            userId,
            roomId,
            status: 'confirmed',
            completedAt: new Date().toISOString()
        });
        return { success: true, bookingId, message: '预约成功' };
    }
    catch (error) {
        log_1.TaskLogger.error('预约任务处理失败', {
            taskId,
            userId,
            roomId,
            error: error.message,
            stack: error.stack
        });
        return { success: false, message: '预约失败，请稍后重试' };
    }
}
function simulateSystemMonitoring() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    log_1.ServiceLogger.info('系统监控报告', {
        memory: {
            rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
            heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
            heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
            external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`
        },
        cpu: {
            user: `${cpuUsage.user}μs`,
            system: `${cpuUsage.system}μs`
        },
        uptime: `${Math.round(process.uptime())}s`,
        timestamp: new Date().toISOString()
    });
}
async function runLoggerDemo() {
    console.log('🎭 开始日志系统实际使用演示\n');
    log_1.log.info('=== 用户登录流程演示 ===');
    await simulateUserLogin('user123', 'wrong_password');
    await new Promise(resolve => setTimeout(resolve, 500));
    await simulateUserLogin('user123', 'correct_password');
    await new Promise(resolve => setTimeout(resolve, 1000));
    log_1.log.info('=== API请求处理演示 ===');
    await simulateAPIRequest('GET', '/api/rooms', 'user123');
    await simulateAPIRequest('POST', '/api/booking', 'user123');
    await simulateAPIRequest('DELETE', '/api/booking/123', 'user123');
    await new Promise(resolve => setTimeout(resolve, 1000));
    log_1.log.info('=== 预约任务处理演示 ===');
    await simulateBookingTask('task_001', 'user123', 'room_147');
    await simulateBookingTask('task_002', 'user456', 'room_153');
    await new Promise(resolve => setTimeout(resolve, 1000));
    log_1.log.info('=== 系统监控演示 ===');
    simulateSystemMonitoring();
    await new Promise(resolve => setTimeout(resolve, 1000));
    log_1.log.info('=== 便捷日志函数演示 ===');
    log_1.log.debug('这是调试信息', { component: 'demo', action: 'test' });
    log_1.log.info('这是普通信息', { status: 'running' });
    log_1.log.warn('这是警告信息', { issue: 'performance-slow' });
    log_1.log.error('这是错误信息', { errorCode: 'DEMO_ERROR' });
    console.log('\n✅ 日志系统演示完成!');
    console.log('📁 请查看 logs/ 目录下的日志文件');
}
if (require.main === module) {
    runLoggerDemo().catch(error => {
        console.error('演示过程中发生错误:', error);
        process.exit(1);
    });
}
