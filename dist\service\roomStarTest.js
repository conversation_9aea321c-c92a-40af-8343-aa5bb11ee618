"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runRoomStarTests = runRoomStarTests;
const dbConnection_1 = require("../config/dbConnection");
async function testDatabaseStarFunctions() {
    console.log('\n=== 测试数据库收藏功能 ===');
    const testUser = {
        name: 'testuser',
        email: '<EMAIL>',
        authentication: {
            password: 'testpass'
        },
        services: {
            account: {
                username: 'testuser',
                password: 'testpass',
                mobile: '***********'
            }
        }
    };
    try {
        const createdUser = await dbConnection_1.db.createUser(testUser);
        console.log('✅ 用户创建成功:', createdUser._id);
        const userId = createdUser._id;
        console.log('\n--- 测试添加收藏 ---');
        let result = await dbConnection_1.db.setRoomStar(userId, 'room_001', true);
        console.log('添加 room_001 收藏结果:', result);
        result = await dbConnection_1.db.setRoomStar(userId, 'room_002', true);
        console.log('添加 room_002 收藏结果:', result);
        let user = await dbConnection_1.db.findUser({ _id: userId });
        console.log('当前收藏列表:', user?.services?.stars);
        console.log('\n--- 测试重复添加收藏 ---');
        result = await dbConnection_1.db.setRoomStar(userId, 'room_001', true);
        console.log('重复添加 room_001 收藏结果:', result);
        user = await dbConnection_1.db.findUser({ _id: userId });
        console.log('重复添加后收藏列表:', user?.services?.stars);
        console.log('\n--- 测试取消收藏 ---');
        result = await dbConnection_1.db.setRoomStar(userId, 'room_001', false);
        console.log('取消 room_001 收藏结果:', result);
        user = await dbConnection_1.db.findUser({ _id: userId });
        console.log('取消收藏后列表:', user?.services?.stars);
        console.log('\n--- 测试取消不存在的收藏 ---');
        result = await dbConnection_1.db.setRoomStar(userId, 'room_999', false);
        console.log('取消不存在房间收藏结果:', result);
        user = await dbConnection_1.db.findUser({ _id: userId });
        console.log('最终收藏列表:', user?.services?.stars);
        await dbConnection_1.db.deleteUser(userId);
        console.log('✅ 测试用户已清理');
    }
    catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}
function testAPIResponseFormat() {
    console.log('\n=== 测试API响应格式 ===');
    const mockStarsResponse = {
        success: true,
        stars: ['room_001', 'room_002', 'room_003']
    };
    console.log('getStars API 响应格式:', JSON.stringify(mockStarsResponse, null, 2));
    const mockSetStarResponse = {
        success: true
    };
    console.log('setStar API 响应格式:', JSON.stringify(mockSetStarResponse, null, 2));
    const mockSetStarRequest = {
        room_id: 'room_001',
        starred: true
    };
    console.log('setStar API 请求参数格式:', JSON.stringify(mockSetStarRequest, null, 2));
}
async function testEdgeCases() {
    console.log('\n=== 测试边界情况 ===');
    try {
        console.log('--- 测试不存在的用户 ---');
        const result = await dbConnection_1.db.setRoomStar('nonexistent_user', 'room_001', true);
        console.log('不存在用户的收藏操作结果:', result);
    }
    catch (error) {
        console.log('不存在用户操作预期错误:', error.message);
    }
    console.log('\n--- 测试空字符串房间ID ---');
    console.log('空字符串房间ID应该在API层面被拒绝');
    console.log('\n--- 测试特殊字符房间ID ---');
    console.log('特殊字符房间ID在数据库层面是允许的，但应该在业务层面验证');
}
function testUserInfoCompatibility() {
    console.log('\n=== 测试用户信息接口兼容性 ===');
    const mockUserInfoResponse = {
        success: true,
        user_id: 'user_123',
        name: 'testuser',
        email: '<EMAIL>',
        services: {
            account: {
                username: 'testuser',
                password: 'testpass',
                mobile: '***********'
            },
            tasks: []
        }
    };
    console.log('原有 /user/info API 响应保持不变:');
    console.log(JSON.stringify(mockUserInfoResponse, null, 2));
    console.log('✅ 确认不包含 stars 字段，保持向后兼容');
}
async function runRoomStarTests() {
    console.log('开始房间收藏功能测试...\n');
    try {
        await testDatabaseStarFunctions();
        testAPIResponseFormat();
        await testEdgeCases();
        testUserInfoCompatibility();
        console.log('\n✅ 所有房间收藏功能测试完成！');
        console.log('\n=== 功能总结 ===');
        console.log('✅ 数据库 stars 字段：string[] 类型，存储房间ID');
        console.log('✅ GET /user/getstars：返回 {success: boolean, stars: string[]}');
        console.log('✅ POST /user/star：接收 {room_id: string, starred: boolean}，返回 {success: boolean}');
        console.log('✅ 原有 /user/info 接口保持不变');
        console.log('✅ 避免重复收藏，正确处理取消收藏');
        console.log('✅ 完整的错误处理和日志记录');
    }
    catch (error) {
        console.error('\n❌ 测试过程中出现错误:', error);
    }
}
if (require.main === module) {
    runRoomStarTests();
}
