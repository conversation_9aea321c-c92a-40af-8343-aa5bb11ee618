# Installation
> `npm install --save @types/pouchdb-adapter-idb`

# Summary
This package contains type definitions for pouchdb-adapter-idb (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-idb.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-idb/index.d.ts)
````ts
/// <reference types="pouchdb-core" />

declare namespace PouchDB {
    namespace Core {
        interface DatabaseInfo {
            idb_attachment_format?: "base64" | "binary" | undefined;
        }
    }

    namespace IdbAdapter {
        interface IdbAdapterConfiguration extends Configuration.LocalDatabaseConfiguration {
            /**
             * Configures storage persistence.
             *
             * Only works in Firefox 26+.
             */
            storage?: "persistent" | "temporary" | undefined;
            adapter: "idb";
        }
    }

    interface Static {
        new<Content extends {}>(name: string | null, options: IdbAdapter.IdbAdapterConfiguration): Database<Content>;
    }
}

declare module "pouchdb-adapter-idb" {
    const plugin: PouchDB.Plugin;
    export = plugin;
}

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core)

# Credits
These definitions were written by [Simon Paulger](https://github.com/spaulg), [Brian Geppert](https://github.com/geppy), and [Frederico Galvão](https://github.com/fredgalvao).
