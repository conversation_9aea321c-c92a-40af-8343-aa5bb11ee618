{"name": "@types/pouchdb-find", "version": "7.3.3", "description": "TypeScript definitions for pouchdb-find", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-find", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "trubit", "url": "https://github.com/trubit"}, {"name": "<PERSON>", "githubUsername": "tiangolo", "url": "https://github.com/tiangolo"}, {"name": "<PERSON>", "githubUsername": "kuz<PERSON><PERSON>", "url": "https://github.com/kuzmatech"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-find"}, "scripts": {}, "dependencies": {"@types/pouchdb-core": "*"}, "typesPublisherContentHash": "6be00c4c663529bd9423a6440e741124e07f43e99a41da16b7553454a86dbbd5", "typeScriptVersion": "4.5"}