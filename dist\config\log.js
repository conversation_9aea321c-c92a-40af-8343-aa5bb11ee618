"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.log = exports.loggers = exports.NetworkLogger = exports.TaskLogger = exports.APILogger = exports.DatabaseLogger = exports.SessionLogger = exports.ServiceLogger = exports.MainLogger = void 0;
const winston_1 = __importDefault(require("winston"));
const chalk_1 = __importDefault(require("chalk"));
const colors = {
    error: chalk_1.default.red.bold,
    warn: chalk_1.default.yellow.bold,
    info: chalk_1.default.blue.bold,
    debug: chalk_1.default.green.bold,
    verbose: chalk_1.default.cyan.bold,
    silly: chalk_1.default.magenta.bold
};
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.printf(({ timestamp, level, message, service, stack, ...meta }) => {
    const colorFn = colors[level] || chalk_1.default.white;
    const timeStr = chalk_1.default.gray(`[${timestamp}]`);
    const serviceStr = service ? chalk_1.default.cyan.bold(`[${service}]`) : '';
    const levelStr = colorFn(`${level.toUpperCase()}`);
    const messageStr = typeof message === 'object' ? JSON.stringify(message, null, 2) : message;
    let logLine = `${timeStr} ${serviceStr} ${levelStr} ${messageStr}`;
    if (Object.keys(meta).length > 0) {
        const metaStr = chalk_1.default.gray(JSON.stringify(meta, null, 2));
        logLine += `\n${chalk_1.default.gray('Meta:')} ${metaStr}`;
    }
    if (stack) {
        logLine += `\n${chalk_1.default.red('Stack:')} ${chalk_1.default.red(stack)}`;
    }
    return logLine;
}));
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
const error_log = new winston_1.default.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    format: fileFormat,
    maxsize: 5242880,
    maxFiles: 5
});
const combined = new winston_1.default.transports.File({
    filename: 'logs/combined.log',
    format: fileFormat,
    maxsize: 5242880,
    maxFiles: 5
});
const console_log = new winston_1.default.transports.Console({
    format: consoleFormat,
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug'
});
const baseLoggerConfig = {
    level: process.env.LOG_LEVEL || 'info',
    transports: [
        error_log,
        combined,
        console_log
    ],
    exceptionHandlers: [
        new winston_1.default.transports.File({ filename: 'logs/exceptions.log' })
    ],
    rejectionHandlers: [
        new winston_1.default.transports.File({ filename: 'logs/rejections.log' })
    ]
};
exports.MainLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '主服务' }
});
exports.ServiceLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '图书馆预约服务' }
});
exports.SessionLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '图书馆预约会话' }
});
exports.DatabaseLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '数据库服务' }
});
exports.APILogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: 'API服务' }
});
exports.TaskLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '任务调度' }
});
exports.NetworkLogger = winston_1.default.createLogger({
    level: 'debug',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
    transports: [
        new winston_1.default.transports.File({
            filename: 'logs/network-requests.log',
            format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.json()),
            maxsize: 20971520,
            maxFiles: 3,
            tailable: true
        })
    ],
    defaultMeta: { service: '网络请求日志' }
});
exports.loggers = {
    service: exports.ServiceLogger,
    session: exports.SessionLogger,
    database: exports.DatabaseLogger,
    api: exports.APILogger,
    task: exports.TaskLogger,
    network: exports.NetworkLogger
};
process.on('uncaughtException', (error) => {
    exports.MainLogger.error('未捕获的异常', { error: error.message, stack: error.stack });
});
process.on('unhandledRejection', (reason, promise) => {
    exports.MainLogger.error('未处理的Promise拒绝', { reason, promise });
});
exports.log = {
    error: (message, meta) => exports.ServiceLogger.error(message, meta),
    warn: (message, meta) => exports.ServiceLogger.warn(message, meta),
    info: (message, meta) => exports.ServiceLogger.info(message, meta),
    debug: (message, meta) => exports.ServiceLogger.debug(message, meta),
    verbose: (message, meta) => exports.ServiceLogger.verbose(message, meta)
};
console.log(chalk_1.default.cyan.bold('\n日志系统已初始化'));
console.log(chalk_1.default.gray(`日志文件目录: logs/`));
console.log(chalk_1.default.gray(`日志级别: ${process.env.LOG_LEVEL || 'info'}`));
console.log(chalk_1.default.gray(`运行环境: ${process.env.NODE_ENV || 'development'}`));
console.log(chalk_1.default.cyan('─'.repeat(50)));
