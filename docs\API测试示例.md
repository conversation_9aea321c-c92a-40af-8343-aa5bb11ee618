# API测试示例

## 新增任务管理接口测试

### 1. 获取用户任务列表

```bash
# 获取当前用户的所有任务
curl -X GET "http://localhost:3000/user/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**响应示例:**
```json
{
  "success": true,
  "tasks": [
    {
      "task_id": "task_abc123",
      "active": true,
      "repetive": false,
      "type": "space",
      "target": {
        "id": ["room_147"],
        "date": "2024-01-15",
        "start_time": "14:00",
        "end_time": "16:00"
      },
      "execute": {
        "day": 1,
        "time": "09:00"
      },
      "status": "success",
      "lastExecutionTime": "2024-01-14T09:00:00.000Z",
      "lastExecutionResult": {
        "success": true,
        "message": "任务执行成功",
        "timestamp": "2024-01-14T09:00:00.000Z"
      },
      "createdAt": "2024-01-13T10:00:00.000Z",
      "updatedAt": "2024-01-14T09:00:00.000Z"
    }
  ]
}
```

### 2. 删除特定任务

```bash
# 删除指定的任务
curl -X DELETE "http://localhost:3000/user/deletetask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "task_abc123"
  }'
```

**响应示例:**
```json
{
  "success": true,
  "message": "任务删除成功"
}
```

### 3. 获取任务状态统计

```bash
# 获取任务状态统计信息
curl -X GET "http://localhost:3000/user/taskstatus" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**响应示例:**
```json
{
  "success": true,
  "stats": {
    "total": 5,
    "pending": 2,
    "executing": 1,
    "success": 1,
    "failed": 1,
    "repetitive": 3,
    "single": 2
  }
}
```

## 完善的现有接口测试

### 4. 创建新任务 (增强版 - 支持多目标ID)

```bash
# 创建新的预约任务 - 单目标ID
curl -X POST "http://localhost:3000/user/newtask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "active": true,
    "repetive": false,
    "type": "space",
    "target": {
      "id": ["room_147"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'

# 创建新的预约任务 - 多目标ID (推荐)
curl -X POST "http://localhost:3000/user/newtask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "active": true,
    "repetive": false,
    "type": "space",
    "target": {
      "id": ["room_147", "room_148", "room_149", "room_150"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'
```

**响应示例:**
```json
{
  "success": true,
  "task_id": "task_xyz789"
}
```

### 5. 更新任务 (修复版)

```bash
# 更新现有任务
curl -X POST "http://localhost:3000/user/updatetask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "task_xyz789",
    "active": true,
    "repetive": true,
    "repeat": [false, true, false, true, false, true, false],
    "type": "space",
    "target": {
      "id": ["room_147"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'
```

**响应示例:**
```json
{
  "success": true
}
```

## 完整的工作流程测试

### 场景1: 多目标ID任务执行

```bash
# 1. 创建多目标ID任务
curl -X POST "http://localhost:3000/user/newtask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "active": true,
    "repetive": false,
    "type": "space",
    "target": {
      "id": ["room_147", "room_148", "room_149"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'

# 2. 查看任务状态 (应该是 pending)
curl -X GET "http://localhost:3000/user/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 3. 等待任务执行...

# 4. 再次查看任务状态和执行结果
curl -X GET "http://localhost:3000/user/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**执行结果示例:**
```json
{
  "success": true,
  "tasks": [
    {
      "task_id": "task_multi_001",
      "status": "success",
      "lastExecutionResult": {
        "success": true,
        "message": "预约成功，使用目标ID: room_148",
        "timestamp": "2024-01-14T09:00:00.000Z",
        "attemptedIds": ["room_147", "room_148"],
        "successfulId": "room_148",
        "totalAttempts": 2
      }
    }
  ]
}
```

### 场景2: 单次任务生命周期

```bash
# 1. 创建单次任务
curl -X POST "http://localhost:3000/user/newtask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "active": true,
    "repetive": false,
    "type": "space",
    "target": {
      "id": ["room_147"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'

# 2. 查看任务状态 (应该是 pending)
curl -X GET "http://localhost:3000/user/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 3. 等待任务执行...

# 4. 再次查看任务状态 (应该是 success 或 failed)
curl -X GET "http://localhost:3000/user/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 5. 查看统计信息
curl -X GET "http://localhost:3000/user/taskstatus" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 场景3: 重试策略对比测试

```bash
# 测试循环重试策略 (≤3个目标ID)
curl -X POST "http://localhost:3000/user/newtask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "active": true,
    "repetive": false,
    "type": "space",
    "target": {
      "id": ["room_001", "room_002"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'

# 测试单次尝试策略 (>3个目标ID)
curl -X POST "http://localhost:3000/user/newtask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "active": true,
    "repetive": false,
    "type": "space",
    "target": {
      "id": ["room_001", "room_002", "room_003", "room_004", "room_005"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'

# 对比执行结果
curl -X GET "http://localhost:3000/user/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**预期结果对比:**
- **循环重试**: `attemptedIds` 可能包含重复ID，最多3次尝试
- **单次尝试**: `attemptedIds` 不重复，每个ID最多尝试1次

### 场景4: 重复任务管理

```bash
# 1. 创建重复任务 (每周一、三、五)
curl -X POST "http://localhost:3000/user/newtask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "active": true,
    "repetive": true,
    "repeat": [false, true, false, true, false, true, false],
    "type": "space",
    "target": {
      "id": ["room_147"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'

# 2. 查看任务列表
curl -X GET "http://localhost:3000/user/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 3. 修改任务 (改为每天执行)
curl -X POST "http://localhost:3000/user/updatetask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "TASK_ID_FROM_STEP_1",
    "active": true,
    "repetive": true,
    "repeat": [true, true, true, true, true, true, true],
    "type": "space",
    "target": {
      "id": ["room_147"],
      "date": "2024-01-15",
      "start_time": "14:00",
      "end_time": "16:00"
    },
    "execute": {
      "day": 1,
      "time": "09:00"
    }
  }'

# 4. 删除任务
curl -X DELETE "http://localhost:3000/user/deletetask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "TASK_ID_FROM_STEP_1"
  }'
```

## 错误处理测试

### 1. 删除不存在的任务

```bash
curl -X DELETE "http://localhost:3000/user/deletetask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "nonexistent_task"
  }'
```

**预期响应:**
```json
{
  "success": false,
  "error": "任务不存在"
}
```

### 2. 缺少必要参数

```bash
curl -X DELETE "http://localhost:3000/user/deletetask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'
```

**预期响应:**
```json
{
  "success": false,
  "error": "任务ID不能为空"
}
```

## 状态监控

### 实时监控任务状态变化

```bash
# 创建一个即将执行的任务
TOMORROW=$(date -d "tomorrow" +%Y-%m-%d)
CURRENT_TIME=$(date +%H:%M)

curl -X POST "http://localhost:3000/user/newtask" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"active\": true,
    \"repetive\": false,
    \"type\": \"space\",
    \"target\": {
      \"id\": [\"room_147\"],
      \"date\": \"$TOMORROW\",
      \"start_time\": \"14:00\",
      \"end_time\": \"16:00\"
    },
    \"execute\": {
      \"day\": 1,
      \"time\": \"$CURRENT_TIME\"
    }
  }"

# 持续监控状态变化
while true; do
  echo "=== $(date) ==="
  curl -s -X GET "http://localhost:3000/user/taskstatus" \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" | jq '.stats'
  sleep 10
done
```

## 注意事项

1. **认证**: 所有接口都需要有效的JWT令牌
2. **时间格式**: 日期使用 "yyyy-mm-dd"，时间使用 "hh:mm"
3. **状态变化**: 任务状态会根据执行情况自动更新
4. **数据持久化**: 已执行的单次任务会保留在数据库中
5. **错误处理**: 接口会返回详细的错误信息
