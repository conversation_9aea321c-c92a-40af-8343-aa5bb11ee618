"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sp = exports.SessionPool = void 0;
const librarySession_1 = require("./librarySession");
const dbConnection_1 = require("../config/dbConnection");
const log_1 = require("../config/log");
const taskPool_1 = require("./taskPool");
const taskExecutor_1 = require("./taskExecutor");
class SessionPool {
    constructor(db, keep_alive_delay) {
        this.keepAliveDelay = 1000 * 60 * 10;
        this.sessionTimeoutMs = 60 * 60 * 1000;
        this.sessions = [];
        this.db = db;
        this.keepAliveDelay = keep_alive_delay || this.keepAliveDelay;
        this.taskPool = new taskPool_1.TaskPool(async (userId, task) => {
            return await this.executeTaskInternal(userId, task);
        }, async (userId, task) => {
            await this.updateTaskStatusInDatabase(userId, task);
        }, async (userId) => {
            return await this.startSession(userId);
        });
        this.init();
    }
    async init() {
        try {
            const users = await this.db.findAllUsers({});
            for (const user of users) {
                if (user.services?.account) {
                    this.createSession(user);
                    if (user.services.tasks) {
                        for (const task of user.services.tasks) {
                            this.taskPool.createTask(user._id, task);
                        }
                    }
                }
            }
            log_1.ServiceLogger.info(`会话池初始化完成, 创建了 ${this.sessions.length} 个会话.`);
            await this.startKeepAliveSessions();
        }
        catch (err) {
            log_1.ServiceLogger.error(`初始化会话池失败: ${err}`);
        }
    }
    createSession(user) {
        const exsists = this.getSession(user._id);
        if (exsists)
            return exsists;
        const session = new librarySession_1.LibrarySession(user.services.account);
        const tasks = user.services.tasks || [];
        this.sessions.push({
            id: user._id,
            session,
            tasks
        });
        return session;
    }
    getSession(user_id) {
        const session = this.sessions.find(session => session.id === user_id);
        if (session)
            return session.session;
        else
            return null;
    }
    removeSession(user_id) {
        this.sessions = this.sessions.filter(session => session.id !== user_id);
    }
    async startSession(user_id) {
        const session = this.getSession(user_id);
        if (!session)
            return false;
        try {
            let loginResult = false;
            if (!await session.testIfLoggedIn()) {
                loginResult = await session.login();
            }
            else {
                loginResult = true;
            }
            if (loginResult) {
                try {
                    await this.db.updateSessionLoginTime(user_id);
                    log_1.ServiceLogger.info(`会话登录时间已更新`, { userId: user_id });
                }
                catch (error) {
                    log_1.ServiceLogger.error(`更新会话登录时间失败`, {
                        userId: user_id,
                        error: error.message
                    });
                }
            }
            return loginResult;
        }
        catch (e) {
            log_1.ServiceLogger.error(`启动会话 ${user_id} 失败: ${e}`);
            return false;
        }
    }
    async startKeepAliveSessions() {
        let [success, fail] = [0, 0];
        let current = false;
        for (const sessionEntry of this.sessions) {
            try {
                current = await this.startSession(sessionEntry.id);
            }
            catch (e) {
                log_1.ServiceLogger.error(`启动会话 ${sessionEntry.id} 失败: ${e}`);
                current = false;
            }
            success += current ? 1 : 0;
            fail += current ? 0 : 1;
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        log_1.ServiceLogger.info(`成功启动了 ${success} 个会话, ${fail} 个失败.`);
        return success;
    }
    async updateSession(user_id, account) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        if (!sessionEntry)
            return false;
        sessionEntry.session = new librarySession_1.LibrarySession(account);
        console.log(`Session updated for user: ${user_id}`);
        return true;
    }
    async updateUser(user_id) {
        const user = await this.db.findUser({ _id: user_id });
        if (!user)
            return false;
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        try {
            if (!sessionEntry) {
                if (user.services?.account) {
                    this.createSession(user);
                    if (user.services.tasks) {
                        for (const task of user.services.tasks) {
                            this.taskPool.createTask(user_id, task);
                        }
                    }
                    return true;
                }
            }
            else if (user.services?.account) {
                const oldTasks = sessionEntry.tasks;
                const newTasks = user.services.tasks || [];
                for (const oldTask of oldTasks) {
                    if (oldTask.task_id && !newTasks.find(t => t.task_id === oldTask.task_id)) {
                        this.taskPool.deleteTask(oldTask.task_id);
                    }
                }
                for (const newTask of newTasks) {
                    if (newTask.task_id && !oldTasks.find(t => t.task_id === newTask.task_id)) {
                        this.taskPool.createTask(user_id, newTask);
                    }
                    else if (newTask.task_id) {
                        this.taskPool.updateTask(newTask.task_id, newTask);
                    }
                }
                sessionEntry.tasks = newTasks;
                if (user.services?.account.username !== sessionEntry.session.account.username ||
                    user.services?.account.password !== sessionEntry.session.account.password) {
                    return await this.updateSession(user_id, user.services.account);
                }
            }
            return true;
        }
        catch (e) {
            log_1.ServiceLogger.error(`更新用户 ${user_id} 会话失败: ${e}`);
            return false;
        }
    }
    async fetchQuickSelect(user_id, date, booking_type) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        if (!sessionEntry)
            return null;
        return await sessionEntry.session.getQuickSelect(date, booking_type);
    }
    getAllSessions() {
        return this.sessions.map(sessionEntry => ({
            id: sessionEntry.id,
            isLoggedIn: sessionEntry.session ? true : false,
            taskCount: sessionEntry.tasks.length
        }));
    }
    getSessionCount() {
        return this.sessions.length;
    }
    getTasks(user_id) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        return sessionEntry ? sessionEntry.tasks : [];
    }
    async addTask(user_id, task) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        if (!sessionEntry)
            return false;
        const now = new Date().toISOString();
        task.status = 'pending';
        task.createdAt = now;
        task.updatedAt = now;
        sessionEntry.tasks.push(task);
        const taskCreated = this.taskPool.createTask(user_id, task);
        if (!taskCreated) {
            sessionEntry.tasks.pop();
            log_1.ServiceLogger.error('任务线程创建失败', { userId: user_id, taskId: task.task_id });
            return false;
        }
        try {
            const user = await this.db.findUser({ _id: user_id });
            if (user) {
                const updatedTasks = [...(user.services?.tasks || []), task];
                await this.db.updateUser(user_id, {
                    'services.tasks': updatedTasks
                });
            }
            log_1.ServiceLogger.info(`任务添加成功`, { userId: user_id, taskId: task.task_id, taskType: task.type });
            return true;
        }
        catch (err) {
            this.taskPool.deleteTask(task.task_id || '');
            sessionEntry.tasks.pop();
            log_1.ServiceLogger.error(`任务添加失败: ${err}`, { userId: user_id, taskId: task.task_id });
            return false;
        }
    }
    async removeTask(user_id, taskIndex) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        if (!sessionEntry || taskIndex < 0 || taskIndex >= sessionEntry.tasks.length) {
            return false;
        }
        const taskToRemove = sessionEntry.tasks[taskIndex];
        const taskId = taskToRemove.task_id;
        if (taskId) {
            this.taskPool.deleteTask(taskId);
        }
        sessionEntry.tasks.splice(taskIndex, 1);
        try {
            await this.db.updateUser(user_id, {
                'services.tasks': sessionEntry.tasks
            });
            log_1.ServiceLogger.info(`任务移除成功`, { userId: user_id, taskId, taskIndex });
            return true;
        }
        catch (err) {
            log_1.ServiceLogger.error(`任务移除失败: ${err}`, { userId: user_id, taskId, taskIndex });
            return false;
        }
    }
    async removeTaskById(user_id, task_id) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        if (!sessionEntry)
            return false;
        const taskIndex = sessionEntry.tasks.findIndex(task => task.task_id === task_id);
        if (taskIndex === -1) {
            log_1.ServiceLogger.warn('尝试移除不存在的任务', { userId: user_id, taskId: task_id });
            return false;
        }
        return await this.removeTask(user_id, taskIndex);
    }
    async updateTaskStatusInDatabase(user_id, task) {
        try {
            await this.db.updateTask(user_id, task);
            const sessionEntry = this.sessions.find(session => session.id === user_id);
            if (sessionEntry) {
                const taskIndex = sessionEntry.tasks.findIndex(t => t.task_id === task.task_id);
                if (taskIndex !== -1) {
                    sessionEntry.tasks[taskIndex] = { ...task };
                }
            }
        }
        catch (error) {
            log_1.ServiceLogger.error('更新任务状态到数据库失败', {
                userId: user_id,
                taskId: task.task_id,
                error: error.message
            });
            throw error;
        }
    }
    async executeTaskInternal(user_id, task) {
        const session = this.getSession(user_id);
        if (!session) {
            log_1.ServiceLogger.error('会话不存在', { userId: user_id, taskId: task.task_id });
            return false;
        }
        try {
            const is_logged = await this.refreshSession(user_id);
            if (!is_logged) {
                log_1.ServiceLogger.error('会话登录失败', { userId: user_id, taskId: task.task_id });
                return false;
            }
            const executor = new taskExecutor_1.TaskExecutor(session, user_id);
            const result = await executor.executeTask(task);
            log_1.ServiceLogger.info('任务执行完成', {
                userId: user_id,
                taskId: task.task_id,
                success: result.success,
                message: result.message,
                attemptedIds: result.attemptedIds,
                successfulId: result.successfulId,
                totalAttempts: result.totalAttempts
            });
            const executionTime = new Date().toISOString();
            task.lastExecutionResult = {
                success: result.success,
                message: result.message,
                timestamp: executionTime,
                attemptedIds: result.attemptedIds,
                successfulId: result.successfulId,
                totalAttempts: result.totalAttempts
            };
            return result.success;
        }
        catch (err) {
            log_1.SessionLogger.error(`任务执行失败: ${err.message}`, {
                userId: user_id,
                taskId: task.task_id,
                stack: err.stack
            });
            return false;
        }
    }
    async executeTask(user_id, task) {
        const session = this.getSession(user_id);
        if (!session)
            throw Error('会话不存在');
        try {
            switch (task.type) {
                case 'space':
                    const book_space = task.space;
                    log_1.ServiceLogger.info('开始执行空间预约', { userId: user_id, ...task.space });
                    return await session.bookSpace(book_space.room_id, book_space.day, book_space.start_time, book_space.end_time, book_space.title, book_space.title_id);
                case 'seat':
                    console.log(`Executing seat booking task for user ${user_id}`);
                    return true;
                default:
                    throw Error('未知的预约类型');
            }
        }
        catch (err) {
            log_1.SessionLogger.error(`预约失败: ${err.message}`, { userId: user_id, stack: err.stack });
            return false;
        }
    }
    async getSpaceDetail(user_id, room_id, id = '2') {
        const session = this.getSession(user_id);
        if (!session)
            throw Error('会话不存在');
        return await session.getSpaceDetail(room_id, id);
    }
    async getSubscription(user_id) {
        const session = this.getSession(user_id);
        if (!session)
            throw Error('会话不存在');
        return await session.getSubscription();
    }
    async getSessionStatus(user_id) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        if (!sessionEntry) {
            return { exists: false, isLoggedIn: false };
        }
        try {
            const isLoggedIn = await sessionEntry.session.testIfLoggedIn();
            return {
                exists: true,
                isLoggedIn,
            };
        }
        catch (err) {
            log_1.SessionLogger.error(`读取会话状态失败: ${err}`, { userId: user_id });
            return {
                exists: true,
                isLoggedIn: false,
            };
        }
    }
    async checkAndRefreshSessionIfNeeded(user_id, pass_user) {
        try {
            let user = null;
            if (pass_user) {
                user = pass_user;
            }
            else {
                user = await this.db.findUser({ _id: user_id });
            }
            if (!user || !user.services?.sessionLoginTime) {
                log_1.ServiceLogger.info(`用户无会话登录时间记录，执行登录`, { userId: user_id });
                return await this.startSession(user_id);
            }
            const lastLoginTime = new Date(user.services.sessionLoginTime);
            const now = new Date();
            const timeDiff = now.getTime() - lastLoginTime.getTime();
            if (timeDiff > this.sessionTimeoutMs) {
                log_1.ServiceLogger.info(`会话超时，执行重新登录`, {
                    userId: user_id,
                    lastLoginTime: lastLoginTime.toISOString(),
                    timeDiffMinutes: Math.round(timeDiff / (1000 * 60))
                });
                return await this.startSession(user_id);
            }
            const session = this.getSession(user_id);
            if (!session) {
                log_1.ServiceLogger.info(`会话不存在，执行登录`, { userId: user_id });
                return await this.startSession(user_id);
            }
            try {
                const isLoggedIn = await session.testIfLoggedIn();
                if (!isLoggedIn) {
                    log_1.ServiceLogger.info(`会话已失效，执行重新登录`, { userId: user_id });
                    return await this.startSession(user_id);
                }
                return true;
            }
            catch (error) {
                log_1.ServiceLogger.warn(`检查会话状态失败，执行重新登录`, {
                    userId: user_id,
                    error: error.message
                });
                return await this.startSession(user_id);
            }
        }
        catch (error) {
            log_1.ServiceLogger.error(`检查会话状态失败`, {
                userId: user_id,
                error: error.message
            });
            return false;
        }
    }
    async refreshSession(user_id) {
        const session = this.getSession(user_id);
        if (!session)
            return false;
        try {
            const isLoggedIn = await session.testIfLoggedIn();
            if (!isLoggedIn) {
                log_1.ServiceLogger.info(`会话未登录或已过期，正在尝试重新登录`, { userId: user_id });
                const loginResult = await session.login();
                if (loginResult) {
                    try {
                        await this.db.updateSessionLoginTime(user_id);
                        log_1.ServiceLogger.info(`会话登录时间已更新`, { userId: user_id });
                    }
                    catch (error) {
                        log_1.ServiceLogger.error(`更新会话登录时间失败`, {
                            userId: user_id,
                            error: error.message
                        });
                    }
                }
                return loginResult;
            }
            return true;
        }
        catch (err) {
            log_1.ServiceLogger.error(`刷新会话登录状态失败`, { userId: user_id, error: err.message });
            return false;
        }
    }
    async getSessionDetail(user_id) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        if (!sessionEntry) {
            return { exists: false, isLoggedIn: false, taskCount: 0 };
        }
        try {
            const isLoggedIn = await sessionEntry.session.testIfLoggedIn();
            return {
                exists: true,
                isLoggedIn,
                taskCount: sessionEntry.tasks.length,
                lastRefresh: new Date()
            };
        }
        catch (err) {
            console.error(`Failed to get session status for user ${user_id}: ${err}`);
            return {
                exists: true,
                isLoggedIn: false,
                taskCount: sessionEntry.tasks.length,
                lastRefresh: new Date()
            };
        }
    }
    async clearExpiredSessions() {
        let removedCount = 0;
        const validSessions = [];
        for (const sessionEntry of this.sessions) {
            try {
                const user = await this.db.findUser({ _id: sessionEntry.id });
                if (user && user.services?.account) {
                    validSessions.push(sessionEntry);
                }
                else {
                    console.log(`Removing session for non-existent or incomplete user: ${sessionEntry.id}`);
                    removedCount++;
                }
            }
            catch (err) {
                console.error(`Error checking user ${sessionEntry.id}: ${err}`);
                removedCount++;
            }
        }
        this.sessions = validSessions;
        console.log(`Cleared ${removedCount} expired sessions`);
        return removedCount;
    }
    getPoolStats() {
        const totalSessions = this.sessions.length;
        const totalTasks = this.sessions.reduce((sum, session) => sum + session.tasks.length, 0);
        const averageTasksPerSession = totalSessions > 0 ? totalTasks / totalSessions : 0;
        return {
            totalSessions,
            totalTasks,
            averageTasksPerSession: Math.round(averageTasksPerSession * 100) / 100
        };
    }
    getSystemStatus() {
        const taskPoolStats = this.taskPool.getStats();
        return {
            sessionTimeout: {
                timeoutMs: this.sessionTimeoutMs,
                timeoutHours: this.sessionTimeoutMs / (1000 * 60 * 60)
            },
            taskPool: taskPoolStats
        };
    }
    async shutdown() {
        log_1.ServiceLogger.info(`正在关闭会话池，当前有 ${this.sessions.length} 个会话`);
        this.taskPool.shutdown();
        this.sessions = [];
        log_1.ServiceLogger.info('会话池关闭完成');
    }
    async reinitialize() {
        log_1.ServiceLogger.info('重新初始化会话池...');
        this.taskPool.shutdown();
        this.sessions = [];
        this.taskPool = new taskPool_1.TaskPool(async (userId, task) => {
            return await this.executeTaskInternal(userId, task);
        }, async (userId, task) => {
            await this.updateTaskStatusInDatabase(userId, task);
        }, async (userId) => {
            return await this.startSession(userId);
        });
        await this.init();
        await this.startKeepAliveSessions();
        log_1.ServiceLogger.info('会话池重新初始化完成');
    }
    getTaskPoolStats() {
        return this.taskPool.getStats();
    }
    getAllTasksInfo() {
        return this.taskPool.getAllTasksInfo();
    }
    cleanupTaskPool() {
        return this.taskPool.cleanup();
    }
    async triggerKeepAlive() {
        log_1.ServiceLogger.info('手动触发会话保活（已废弃功能）');
    }
    async getDetailedStatus() {
        const sessionDetails = [];
        for (const sessionEntry of this.sessions) {
            try {
                const isLoggedIn = await sessionEntry.session.testIfLoggedIn();
                sessionDetails.push({
                    id: sessionEntry.id,
                    isLoggedIn,
                    taskCount: sessionEntry.tasks.length,
                    lastActive: sessionEntry.lastActive
                });
            }
            catch (err) {
                sessionDetails.push({
                    id: sessionEntry.id,
                    isLoggedIn: false,
                    taskCount: sessionEntry.tasks.length,
                    lastActive: sessionEntry.lastActive
                });
            }
        }
        return {
            poolStats: this.getPoolStats(),
            systemStatus: this.getSystemStatus(),
            sessionDetails
        };
    }
    updateUserActivity(user_id) {
        const sessionEntry = this.sessions.find(session => session.id === user_id);
        if (sessionEntry) {
            sessionEntry.lastActive = new Date();
            log_1.ServiceLogger.debug(`更新用户 ${user_id} 活动时间`);
        }
    }
}
exports.SessionPool = SessionPool;
exports.sp = new SessionPool(dbConnection_1.db);
