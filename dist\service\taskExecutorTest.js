"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runTaskExecutorTests = runTaskExecutorTests;
const taskExecutor_1 = require("./taskExecutor");
const librarySession_1 = require("./librarySession");
class MockLibrarySession extends librarySession_1.LibrarySession {
    constructor(failureRate = 0.7, failingIds = []) {
        super({ username: 'test', password: 'test', mobile: '12345678901' });
        this.failureRate = failureRate;
        this.failingIds = new Set(failingIds);
    }
    async bookSpace(room_id, day, start_time, end_time) {
        console.log(`尝试预约空间: ${room_id}, 日期: ${day}, 时间: ${start_time}-${end_time}`);
        await new Promise(resolve => setTimeout(resolve, 100));
        if (this.failingIds.has(room_id)) {
            console.log(`预约失败: ${room_id} (在失败列表中)`);
            return false;
        }
        const success = Math.random() > this.failureRate;
        console.log(`预约${success ? '成功' : '失败'}: ${room_id}`);
        return success;
    }
}
async function testCyclicRetryStrategy() {
    console.log('\n=== 测试循环重试策略 (目标ID ≤ 3个) ===');
    const mockSession = new MockLibrarySession(0.8);
    const executor = new taskExecutor_1.TaskExecutor(mockSession, 'user001');
    const task = {
        active: true,
        task_id: 'test_cyclic_001',
        repetive: false,
        type: 'space',
        target: {
            id: ['room_001', 'room_002'],
            date: '2024-01-15',
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 0,
            time: '09:00'
        }
    };
    console.log('目标ID:', task.target.id);
    console.log('预期行为: 循环重试直到达到3次尝试');
    const result = await executor.executeTask(task);
    console.log('执行结果:', {
        success: result.success,
        message: result.message,
        attemptedIds: result.attemptedIds,
        successfulId: result.successfulId,
        totalAttempts: result.totalAttempts
    });
}
async function testSingleAttemptStrategy() {
    console.log('\n=== 测试单次尝试策略 (目标ID > 3个) ===');
    const mockSession = new MockLibrarySession(0, ['room_001', 'room_002', 'room_003']);
    const executor = new taskExecutor_1.TaskExecutor(mockSession, 'user002');
    const task = {
        active: true,
        task_id: 'test_single_001',
        repetive: false,
        type: 'space',
        target: {
            id: ['room_001', 'room_002', 'room_003', 'room_004', 'room_005'],
            date: '2024-01-15',
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 0,
            time: '09:00'
        }
    };
    console.log('目标ID:', task.target.id);
    console.log('预期行为: 每个ID尝试一次，第4个应该成功');
    const result = await executor.executeTask(task);
    console.log('执行结果:', {
        success: result.success,
        message: result.message,
        attemptedIds: result.attemptedIds,
        successfulId: result.successfulId,
        totalAttempts: result.totalAttempts
    });
}
async function testAllFailureScenario() {
    console.log('\n=== 测试所有ID都失败的情况 ===');
    const mockSession = new MockLibrarySession(1.0);
    const executor = new taskExecutor_1.TaskExecutor(mockSession, 'user003');
    const task = {
        active: true,
        task_id: 'test_failure_001',
        repetive: false,
        type: 'space',
        target: {
            id: ['room_001', 'room_002', 'room_003'],
            date: '2024-01-15',
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 0,
            time: '09:00'
        }
    };
    console.log('目标ID:', task.target.id);
    console.log('预期行为: 循环重试3次，全部失败');
    const result = await executor.executeTask(task);
    console.log('执行结果:', {
        success: result.success,
        message: result.message,
        attemptedIds: result.attemptedIds,
        successfulId: result.successfulId,
        totalAttempts: result.totalAttempts
    });
}
async function testFirstSuccessScenario() {
    console.log('\n=== 测试第一次就成功的情况 ===');
    const mockSession = new MockLibrarySession(0.0);
    const executor = new taskExecutor_1.TaskExecutor(mockSession, 'user004');
    const task = {
        active: true,
        task_id: 'test_success_001',
        repetive: false,
        type: 'space',
        target: {
            id: ['room_001', 'room_002', 'room_003', 'room_004'],
            date: '2024-01-15',
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 0,
            time: '09:00'
        }
    };
    console.log('目标ID:', task.target.id);
    console.log('预期行为: 第一个ID就成功，不再尝试其他ID');
    const result = await executor.executeTask(task);
    console.log('执行结果:', {
        success: result.success,
        message: result.message,
        attemptedIds: result.attemptedIds,
        successfulId: result.successfulId,
        totalAttempts: result.totalAttempts
    });
}
async function testSeatBookingType() {
    console.log('\n=== 测试座位预约类型 ===');
    const mockSession = new MockLibrarySession(0.0);
    const executor = new taskExecutor_1.TaskExecutor(mockSession, 'user005');
    const task = {
        active: true,
        task_id: 'test_seat_001',
        repetive: false,
        type: 'seat',
        target: {
            id: ['seat_001', 'seat_002'],
            date: '2024-01-15',
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 0,
            time: '09:00'
        }
    };
    console.log('目标ID:', task.target.id);
    console.log('预期行为: 座位预约功能（当前为模拟实现）');
    const result = await executor.executeTask(task);
    console.log('执行结果:', {
        success: result.success,
        message: result.message,
        attemptedIds: result.attemptedIds,
        successfulId: result.successfulId,
        totalAttempts: result.totalAttempts
    });
}
async function runTaskExecutorTests() {
    console.log('开始任务执行器测试...\n');
    try {
        await testCyclicRetryStrategy();
        await testSingleAttemptStrategy();
        await testAllFailureScenario();
        await testFirstSuccessScenario();
        await testSeatBookingType();
        console.log('\n✅ 所有任务执行器测试完成！');
    }
    catch (error) {
        console.error('\n❌ 测试过程中出现错误:', error);
    }
}
if (require.main === module) {
    runTaskExecutorTests();
}
