"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDetailedHealth = exports.getMetrics = exports.performanceMonitoring = void 0;
const log_1 = require("../config/log");
const env_1 = __importDefault(require("../config/env"));
class MonitoringService {
    constructor() {
        this.responseTimes = [];
        this.maxResponseTimeHistory = 100;
        this.startTime = new Date();
        this.lastCpuUsage = process.cpuUsage();
        this.metrics = {
            requestCount: 0,
            errorCount: 0,
            averageResponseTime: 0,
            lastRequestTime: new Date(),
            uptime: 0,
            memoryUsage: process.memoryUsage(),
            cpuUsage: process.cpuUsage()
        };
        setInterval(() => {
            this.updateSystemMetrics();
        }, 30000);
    }
    updateSystemMetrics() {
        this.metrics.uptime = Date.now() - this.startTime.getTime();
        this.metrics.memoryUsage = process.memoryUsage();
        this.metrics.cpuUsage = process.cpuUsage(this.lastCpuUsage);
        this.lastCpuUsage = process.cpuUsage();
    }
    recordRequest(responseTime, isError = false) {
        this.metrics.requestCount++;
        this.metrics.lastRequestTime = new Date();
        if (isError) {
            this.metrics.errorCount++;
        }
        this.responseTimes.push(responseTime);
        if (this.responseTimes.length > this.maxResponseTimeHistory) {
            this.responseTimes.shift();
        }
        this.metrics.averageResponseTime =
            this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length;
    }
    getMetrics() {
        const errorRate = this.metrics.requestCount > 0
            ? (this.metrics.errorCount / this.metrics.requestCount) * 100
            : 0;
        const uptimeMinutes = this.metrics.uptime / (1000 * 60);
        const requestsPerMinute = uptimeMinutes > 0
            ? this.metrics.requestCount / uptimeMinutes
            : 0;
        const maxResponseTime = this.responseTimes.length > 0
            ? Math.max(...this.responseTimes)
            : 0;
        const minResponseTime = this.responseTimes.length > 0
            ? Math.min(...this.responseTimes)
            : 0;
        return {
            ...this.metrics,
            errorRate,
            requestsPerMinute,
            maxResponseTime,
            minResponseTime
        };
    }
    checkHealth() {
        const checks = {};
        let overallStatus = 'healthy';
        const memUsage = this.metrics.memoryUsage;
        const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
        if (memUsagePercent > 90) {
            checks.memory = { status: 'critical', message: 'Memory usage is critical', value: `${memUsagePercent.toFixed(2)}%` };
            overallStatus = 'critical';
        }
        else if (memUsagePercent > 75) {
            checks.memory = { status: 'warning', message: 'Memory usage is high', value: `${memUsagePercent.toFixed(2)}%` };
            if (overallStatus === 'healthy')
                overallStatus = 'warning';
        }
        else {
            checks.memory = { status: 'healthy', message: 'Memory usage is normal', value: `${memUsagePercent.toFixed(2)}%` };
        }
        const errorRate = this.getMetrics().errorRate;
        if (errorRate > 10) {
            checks.errorRate = { status: 'critical', message: 'Error rate is critical', value: `${errorRate.toFixed(2)}%` };
            overallStatus = 'critical';
        }
        else if (errorRate > 5) {
            checks.errorRate = { status: 'warning', message: 'Error rate is high', value: `${errorRate.toFixed(2)}%` };
            if (overallStatus === 'healthy')
                overallStatus = 'warning';
        }
        else {
            checks.errorRate = { status: 'healthy', message: 'Error rate is normal', value: `${errorRate.toFixed(2)}%` };
        }
        const avgResponseTime = this.metrics.averageResponseTime;
        if (avgResponseTime > 5000) {
            checks.responseTime = { status: 'critical', message: 'Response time is critical', value: `${avgResponseTime.toFixed(2)}ms` };
            overallStatus = 'critical';
        }
        else if (avgResponseTime > 2000) {
            checks.responseTime = { status: 'warning', message: 'Response time is high', value: `${avgResponseTime.toFixed(2)}ms` };
            if (overallStatus === 'healthy')
                overallStatus = 'warning';
        }
        else {
            checks.responseTime = { status: 'healthy', message: 'Response time is normal', value: `${avgResponseTime.toFixed(2)}ms` };
        }
        const uptimeHours = this.metrics.uptime / (1000 * 60 * 60);
        checks.uptime = {
            status: 'healthy',
            message: 'Service is running',
            value: `${uptimeHours.toFixed(2)} hours`
        };
        return { status: overallStatus, checks };
    }
}
const monitoringService = new MonitoringService();
const performanceMonitoring = (req, res, next) => {
    if (!env_1.default.PERFORMANCE_MONITORING) {
        return next();
    }
    const startTime = Date.now();
    res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        const isError = res.statusCode >= 400;
        monitoringService.recordRequest(responseTime, isError);
        if (responseTime > 1000) {
            log_1.MainLogger.warn('Slow request detected', {
                method: req.method,
                url: req.url,
                responseTime: `${responseTime}ms`,
                statusCode: res.statusCode
            });
        }
        if (isError) {
            log_1.MainLogger.error('Error request', {
                method: req.method,
                url: req.url,
                responseTime: `${responseTime}ms`,
                statusCode: res.statusCode,
                userAgent: req.get('User-Agent')
            });
        }
    });
    next();
};
exports.performanceMonitoring = performanceMonitoring;
const getMetrics = (_req, res) => {
    if (!env_1.default.PERFORMANCE_MONITORING) {
        res.status(404).json({ error: 'Performance monitoring disabled' });
        return;
    }
    const metrics = monitoringService.getMetrics();
    res.json({
        timestamp: new Date().toISOString(),
        metrics,
        environment: env_1.default.NODE_ENV
    });
};
exports.getMetrics = getMetrics;
const getDetailedHealth = (_req, res) => {
    const health = monitoringService.checkHealth();
    const metrics = monitoringService.getMetrics();
    const response = {
        status: health.status,
        timestamp: new Date().toISOString(),
        uptime: metrics.uptime,
        environment: env_1.default.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0',
        checks: health.checks,
        metrics: {
            requests: {
                total: metrics.requestCount,
                errors: metrics.errorCount,
                errorRate: `${((metrics.errorCount / metrics.requestCount) * 100 || 0).toFixed(2)}%`,
                requestsPerMinute: metrics.requestsPerMinute.toFixed(2)
            },
            performance: {
                averageResponseTime: `${metrics.averageResponseTime.toFixed(2)}ms`,
                maxResponseTime: `${metrics.maxResponseTime}ms`,
                minResponseTime: `${metrics.minResponseTime}ms`
            },
            system: {
                memory: {
                    used: `${(metrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
                    total: `${(metrics.memoryUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`,
                    usage: `${((metrics.memoryUsage.heapUsed / metrics.memoryUsage.heapTotal) * 100).toFixed(2)}%`
                },
                cpu: {
                    user: metrics.cpuUsage.user,
                    system: metrics.cpuUsage.system
                }
            }
        }
    };
    const statusCode = health.status === 'healthy' ? 200 :
        health.status === 'warning' ? 200 : 503;
    res.status(statusCode).json(response);
};
exports.getDetailedHealth = getDetailedHealth;
exports.default = monitoringService;
