# 美化日志系统使用说明

## 🎨 概述

我们已经将日志系统进行了全面美化，现在支持：
- 🌈 **彩色输出** - 不同日志级别使用不同颜色
- 📋 **图标标识** - 每个日志级别都有对应的emoji图标
- 🏷️ **服务标签** - 清晰标识不同服务的日志
- 📊 **结构化输出** - 美观的JSON格式化
- 📁 **文件分类** - 自动分类保存到不同文件

## 🚀 快速开始

### 1. 基本使用

```typescript
import { ServiceLogger, log } from './src/config/log'

// 使用特定服务的日志器
ServiceLogger.info('服务启动成功', { port: 3000, env: 'development' })

// 使用便捷函数
log.info('这是一条信息日志')
log.warn('这是一条警告日志')
log.error('这是一条错误日志')
```

### 2. 不同服务的日志器

```typescript
import { 
    ServiceLogger,    // 🏛️ 图书馆预约服务
    SessionLogger,    // 🔐 图书馆预约会话
    DatabaseLogger,   // 💾 数据库服务
    APILogger,        // 🌐 API服务
    TaskLogger        // ⚙️ 任务调度
} from './src/config/log'

// 会话相关日志
SessionLogger.info('用户登录成功', { userId: '12345', loginTime: new Date() })

// 数据库相关日志
DatabaseLogger.info('数据库连接成功', { host: 'localhost', database: 'library' })

// API相关日志
APILogger.info('API请求处理', { method: 'POST', endpoint: '/api/booking', responseTime: '150ms' })

// 任务相关日志
TaskLogger.info('定时任务执行', { taskName: 'cleanup', duration: '2.3s' })
```

## 📊 日志级别和颜色

| 级别 | 图标 | 颜色 | 用途 |
|------|------|------|------|
| `error` | ❌ | 红色 | 错误信息，需要立即关注 |
| `warn` | ⚠️ | 黄色 | 警告信息，可能的问题 |
| `info` | ℹ️ | 蓝色 | 一般信息，正常流程 |
| `debug` | 🐛 | 绿色 | 调试信息，开发时使用 |
| `verbose` | 📝 | 青色 | 详细信息，更多细节 |
| `silly` | 🔍 | 紫色 | 最详细的信息 |

## 🎯 使用示例

### 错误日志（带堆栈跟踪）

```typescript
try {
    // 一些可能出错的代码
    throw new Error('数据库连接失败')
} catch (error) {
    ServiceLogger.error('操作失败', {
        error: error.message,
        stack: error.stack,
        context: 'user-registration'
    })
}
```

### 结构化日志

```typescript
APILogger.info('用户预约请求', {
    user: {
        id: 'user123',
        name: '张三',
        email: '<EMAIL>'
    },
    booking: {
        roomId: 'room147',
        startTime: '2025-01-15 14:00:00',
        duration: 120
    },
    metadata: {
        userAgent: 'Mozilla/5.0...',
        ip: '*************',
        timestamp: new Date().toISOString()
    }
})
```

### 性能监控日志

```typescript
const startTime = Date.now()

// 执行一些操作...

const duration = Date.now() - startTime
APILogger.info('API性能监控', {
    endpoint: '/api/rooms/search',
    method: 'GET',
    duration: `${duration}ms`,
    memoryUsage: process.memoryUsage(),
    statusCode: 200
})
```

## 📁 文件输出

日志会自动保存到以下文件：

- `logs/combined.log` - 所有级别的日志
- `logs/error.log` - 仅错误级别的日志
- `logs/exceptions.log` - 未捕获的异常
- `logs/rejections.log` - 未处理的Promise拒绝

### 文件配置

- **最大文件大小**: 5MB
- **保留文件数**: 5个
- **格式**: JSON（便于日志分析工具处理）

## ⚙️ 环境配置

### 环境变量

```bash
# 设置日志级别
LOG_LEVEL=debug

# 设置运行环境
NODE_ENV=development
```

### 不同环境的行为

- **开发环境** (`NODE_ENV=development`)
  - 控制台显示 `debug` 级别及以上
  - 显示详细的堆栈跟踪
  - 彩色输出

- **生产环境** (`NODE_ENV=production`)
  - 控制台仅显示 `info` 级别及以上
  - 简化输出格式
  - 重点关注性能

## 🧪 测试日志系统

运行测试文件查看效果：

```bash
# 使用 ts-node 运行测试
npx ts-node test_logger.ts

# 或者编译后运行
npm run build
node dist/test_logger.js
```

## 🔧 自定义配置

### 修改颜色和图标

在 `src/config/log.ts` 中修改：

```typescript
// 自定义颜色
const colors = {
    error: chalk.red.bold,
    warn: chalk.yellow.bold,
    info: chalk.blue.bold,
    // ...
}

// 自定义图标
const icons = {
    error: '🚨',
    warn: '⚠️',
    info: '💡',
    // ...
}
```

### 添加新的日志器

```typescript
// 添加新的专用日志器
export const PaymentLogger = winston.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '💳 支付服务' }
})
```

### 自定义格式化器

```typescript
const customFormat = winston.format.printf(({ timestamp, level, message, service }) => {
    // 自定义格式化逻辑
    return `${timestamp} [${service}] ${level}: ${message}`
})
```

## 📈 最佳实践

### 1. 选择合适的日志级别

```typescript
// ✅ 好的做法
ServiceLogger.info('用户登录成功', { userId: '12345' })
ServiceLogger.warn('API响应时间较慢', { responseTime: '2.5s', threshold: '1s' })
ServiceLogger.error('数据库连接失败', { error: error.message, retryCount: 3 })

// ❌ 避免的做法
ServiceLogger.info('进入函数 getUserById')  // 过于详细，应该用 debug
ServiceLogger.error('用户输入无效')          // 不是系统错误，应该用 warn
```

### 2. 提供有用的上下文

```typescript
// ✅ 好的做法
SessionLogger.info('会话创建成功', {
    userId: 'user123',
    sessionId: 'sess_abc123',
    loginMethod: 'password',
    userAgent: req.headers['user-agent'],
    ip: req.ip
})

// ❌ 避免的做法
SessionLogger.info('会话创建成功')  // 缺少上下文信息
```

### 3. 结构化日志数据

```typescript
// ✅ 好的做法
APILogger.info('预约请求处理', {
    request: {
        method: 'POST',
        endpoint: '/api/booking',
        userId: 'user123'
    },
    response: {
        statusCode: 200,
        duration: '150ms'
    },
    business: {
        roomId: 'room147',
        bookingId: 'booking456'
    }
})
```

## 🚨 故障排除

### 1. 颜色不显示

如果控制台不显示颜色，检查：
- 终端是否支持ANSI颜色
- 是否在CI/CD环境中（可能需要强制启用颜色）

### 2. 日志文件权限问题

确保应用有权限写入 `logs/` 目录：

```bash
mkdir -p logs
chmod 755 logs
```

### 3. 性能问题

如果日志输出影响性能：
- 调整日志级别到 `warn` 或 `error`
- 考虑使用异步日志传输
- 减少结构化数据的复杂度

## 📚 相关文件

- `src/config/log.ts` - 主要日志配置（使用chalk）
- `src/config/log_simple.ts` - 简化版配置（不依赖chalk）
- `test_logger.ts` - 日志系统测试文件
- `logs/` - 日志文件目录

现在您的日志系统已经完全美化，享受更好的开发体验吧！ 🎉
