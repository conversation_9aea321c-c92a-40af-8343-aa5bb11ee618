"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = __importDefault(require("http"));
const https_1 = __importDefault(require("https"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const compression_1 = __importDefault(require("compression"));
const cors_1 = __importDefault(require("cors"));
const env_1 = __importDefault(require("./config/env"));
const log_1 = require("./config/log");
const router_1 = __importDefault(require("./router"));
const consoleAdmin_1 = require("./admin/consoleAdmin");
const monitoring_1 = require("./middleware/monitoring");
const app = (0, express_1.default)();
const corsOptions = {
    origin: env_1.default.NODE_ENV === 'production'
        ? env_1.default.CORS_ORIGINS
        : ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};
app.use((0, cors_1.default)(corsOptions));
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: env_1.default.REQUEST_BODY_LIMIT }));
app.use(express_1.default.urlencoded({ extended: true, limit: env_1.default.REQUEST_BODY_LIMIT }));
app.use(monitoring_1.performanceMonitoring);
app.get('/health', (_req, res) => {
    if (!env_1.default.HEALTH_CHECK_ENABLED) {
        res.status(404).json({ error: 'Health check disabled' });
        return;
    }
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: env_1.default.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0',
        ssl: !!(env_1.default.SSL_CERT && env_1.default.SSL_KEY)
    });
});
app.get('/health/detailed', monitoring_1.getDetailedHealth);
app.get('/metrics', monitoring_1.getMetrics);
try {
    app.use('/', (0, router_1.default)());
}
catch (error) {
    log_1.MainLogger.error('路由初始化失败', { error: error.message, stack: error.stack });
}
app.use(express_1.default.static(path_1.default.join(__dirname, '../public'), {
    maxAge: env_1.default.STATIC_CACHE_MAX_AGE,
    etag: true,
    lastModified: true
}));
app.get('*', (req, res) => {
    if (res.headersSent) {
        return;
    }
    if (req.path.startsWith('/api') ||
        req.path.startsWith('/auth') ||
        req.path.startsWith('/user') ||
        req.path.startsWith('/task') ||
        req.path.startsWith('/session') ||
        req.path.startsWith('/health') ||
        req.path.startsWith('/metrics') ||
        req.path.startsWith('/crypto') ||
        req.path.startsWith('/assets') ||
        req.path.includes('.')) {
        if (!res.headersSent) {
            res.status(404).json({ error: 'API endpoint not found' });
        }
        return;
    }
    res.sendFile(path_1.default.join(__dirname, '../public/index.html'), (err) => {
        if (err) {
            log_1.MainLogger.error('发送前端页面失败', { error: err.message, path: req.path });
            if (!res.headersSent) {
                res.status(500).json({ error: 'Internal server error' });
            }
        }
    });
});
let server;
if (env_1.default.SSL_CERT && env_1.default.SSL_KEY) {
    try {
        const httpsOptions = {
            cert: fs_1.default.readFileSync(env_1.default.SSL_CERT),
            key: fs_1.default.readFileSync(env_1.default.SSL_KEY)
        };
        server = https_1.default.createServer(httpsOptions, app);
        log_1.MainLogger.info('HTTPS服务器配置成功');
    }
    catch (error) {
        log_1.MainLogger.error('HTTPS配置失败，使用HTTP服务器', { error: error.message });
        server = http_1.default.createServer(app);
    }
}
else {
    server = http_1.default.createServer(app);
}
server.listen(env_1.default.PORT, env_1.default.HOST, () => {
    const protocol = server instanceof https_1.default.Server ? 'https' : 'http';
    log_1.MainLogger.info('网关服务启动成功', {
        port: env_1.default.PORT,
        host: env_1.default.HOST,
        protocol,
        environment: env_1.default.NODE_ENV
    });
    if (env_1.default.CONSOLE_ADMIN_ENABLED) {
        setTimeout(() => {
            log_1.MainLogger.info('启动控制台管理界面...');
            consoleAdmin_1.consoleAdmin.start();
        }, 2000);
    }
});
process.on('SIGINT', () => {
    log_1.MainLogger.info('接收到 SIGINT 信号，正在关闭服务...');
    if (env_1.default.CONSOLE_ADMIN_ENABLED) {
        consoleAdmin_1.consoleAdmin.stop();
    }
    server.close(() => {
        log_1.MainLogger.info('服务已关闭');
        process.exit(0);
    });
});
process.on('SIGTERM', () => {
    log_1.MainLogger.info('接收到 SIGTERM 信号，正在关闭服务...');
    if (env_1.default.CONSOLE_ADMIN_ENABLED) {
        consoleAdmin_1.consoleAdmin.stop();
    }
    server.close(() => {
        log_1.MainLogger.info('服务已关闭');
        process.exit(0);
    });
});
