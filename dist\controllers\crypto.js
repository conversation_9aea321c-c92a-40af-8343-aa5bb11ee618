"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPublicKeyEndpoint = void 0;
const helpers_1 = require("../helpers");
const getPublicKeyEndpoint = async (req, res, next) => {
    try {
        const publicKey = (0, helpers_1.getPublicKeyForFrontend)();
        res.status(200).json({
            success: true,
            publicKey: publicKey
        }).end();
        next();
    }
    catch (err) {
        next(err);
    }
};
exports.getPublicKeyEndpoint = getPublicKeyEndpoint;
