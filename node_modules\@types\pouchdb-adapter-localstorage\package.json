{"name": "@types/pouchdb-adapter-localstorage", "version": "6.1.6", "description": "TypeScript definitions for pouchdb-adapter-localstorage", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-localstorage", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "spaulg", "url": "https://github.com/spaulg"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-adapter-localstorage"}, "scripts": {}, "dependencies": {"@types/pouchdb-core": "*"}, "typesPublisherContentHash": "1b2811c1f6d20720cc1e1e1f4a55577f5954ee0b99f94aee4ad5273ed623004f", "typeScriptVersion": "4.5"}