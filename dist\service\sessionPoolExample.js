"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sessionPoolManager = exports.SessionPoolManager = void 0;
exports.exampleUsage = exampleUsage;
const sessionPool_1 = require("./sessionPool");
const dbConnection_1 = require("../config/dbConnection");
const log_1 = require("../config/log");
class SessionPoolManager {
    constructor() {
        this.sessionPool = new sessionPool_1.SessionPool(dbConnection_1.db, 15 * 60 * 1000);
    }
    async start() {
        log_1.ServiceLogger.info('启动会话池管理器');
        const status = await this.sessionPool.getDetailedStatus();
        log_1.ServiceLogger.info('会话池状态', status);
    }
    async stop() {
        log_1.ServiceLogger.info('停止会话池管理器');
        await this.sessionPool.shutdown();
    }
    async addBookingTask(userId, task) {
        const success = await this.sessionPool.addTask(userId, task);
        if (success) {
            this.sessionPool.updateUserActivity(userId);
            log_1.ServiceLogger.info(`为用户 ${userId} 添加任务成功: ${task.type}`);
        }
        else {
            log_1.ServiceLogger.error(`为用户 ${userId} 添加任务失败: ${task.type}`);
        }
        return success;
    }
    getUserTasks(userId) {
        return this.sessionPool.getTasks(userId);
    }
    async getStats() {
        return await this.sessionPool.getDetailedStatus();
    }
    async manualOperations() {
        await this.sessionPool.triggerKeepAlive();
        const cleanedCount = this.sessionPool.cleanupTaskPool();
        log_1.ServiceLogger.info(`清理了 ${cleanedCount} 个已销毁的任务线程`);
    }
    reconfigureKeepAlive(config) {
        log_1.ServiceLogger.info('会话保活配置功能已废弃，现在使用按需检查机制', config);
    }
    getSystemStatus() {
        return this.sessionPool.getSystemStatus();
    }
    getTaskPoolStats() {
        return this.sessionPool.getTaskPoolStats();
    }
}
exports.SessionPoolManager = SessionPoolManager;
async function exampleUsage() {
    const manager = new SessionPoolManager();
    try {
        await manager.start();
        const task = {
            active: true,
            task_id: 'task_example_001',
            repetive: false,
            type: 'space',
            target: {
                id: ['room_147'],
                date: '2024-01-15',
                start_time: '14:00',
                end_time: '16:00'
            },
            execute: {
                day: 1,
                time: '09:00'
            }
        };
        await manager.addBookingTask('user123', task);
        const stats = await manager.getStats();
        console.log('会话池统计:', stats);
        await manager.manualOperations();
        setTimeout(async () => {
            await manager.stop();
        }, 60000);
    }
    catch (error) {
        log_1.ServiceLogger.error('会话池管理器运行出错:', error);
        await manager.stop();
    }
}
exports.sessionPoolManager = new SessionPoolManager();
process.on('SIGINT', async () => {
    log_1.ServiceLogger.info('接收到 SIGINT 信号，正在优雅关闭...');
    await exports.sessionPoolManager.stop();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    log_1.ServiceLogger.info('接收到 SIGTERM 信号，正在优雅关闭...');
    await exports.sessionPoolManager.stop();
    process.exit(0);
});
