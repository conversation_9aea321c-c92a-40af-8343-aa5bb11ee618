{"name": "@types/pouchdb-replication", "version": "6.4.7", "description": "TypeScript definitions for pouchdb-replication", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-replication", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "trubit", "url": "https://github.com/trubit"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "tiangolo", "url": "https://github.com/tiangolo"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-replication"}, "scripts": {}, "dependencies": {"@types/pouchdb-core": "*", "@types/pouchdb-find": "*"}, "typesPublisherContentHash": "0f00f0719c574114b38d06f5c839c3d151d8a97850119de71b386f6c8b02de25", "typeScriptVersion": "4.5"}