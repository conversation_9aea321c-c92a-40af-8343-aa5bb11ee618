{"name": "@types/pouchdb", "version": "6.4.2", "description": "TypeScript definitions for pouchdb", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "AGBrown", "url": "https://github.com/AGBrown"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb"}, "scripts": {}, "dependencies": {"@types/pouchdb-adapter-cordova-sqlite": "*", "@types/pouchdb-adapter-fruitdown": "*", "@types/pouchdb-adapter-http": "*", "@types/pouchdb-adapter-idb": "*", "@types/pouchdb-adapter-leveldb": "*", "@types/pouchdb-adapter-localstorage": "*", "@types/pouchdb-adapter-memory": "*", "@types/pouchdb-adapter-node-websql": "*", "@types/pouchdb-adapter-websql": "*", "@types/pouchdb-browser": "*", "@types/pouchdb-core": "*", "@types/pouchdb-http": "*", "@types/pouchdb-mapreduce": "*", "@types/pouchdb-node": "*", "@types/pouchdb-replication": "*"}, "typesPublisherContentHash": "f5e3120d28a9445c306c8d049dbf7102d6a0906bb272faac318666413c56ed84", "typeScriptVersion": "4.5"}