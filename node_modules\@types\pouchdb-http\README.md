# Installation
> `npm install --save @types/pouchdb-http`

# Summary
This package contains type definitions for pouchdb-http (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-http.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-http/index.d.ts)
````ts
/// <reference types="pouchdb-core" />
/// <reference types="pouchdb-adapter-http" />

declare const PouchDb: PouchDB.Static;
export = PouchDb;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-adapter-http](https://npmjs.com/package/@types/pouchdb-adapter-http), [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core)

# Credits
These definitions were written by [<PERSON>](https://github.com/spaulg), [<PERSON>](https://github.com/geppy), and [<PERSON><PERSON>](https://github.com/fredgalvao).
