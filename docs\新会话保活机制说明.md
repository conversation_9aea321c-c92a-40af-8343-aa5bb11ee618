# 新会话保活机制说明

## 概述

本次更新完全重写了会话保活逻辑，从定时轮询改为按需检查的方式，提高了系统效率并减少了不必要的资源消耗。

## 核心变更

### 1. 移除定时轮询机制 ✅

**旧机制**：
- 定时轮询检查所有会话状态
- 根据任务数量决定是否保活
- 消耗大量系统资源

**新机制**：
- 按需检查，仅在API访问时触发
- 不再依赖任务存在与否
- 大幅减少系统资源消耗

### 2. 数据库存储会话登录时间 ✅

**新增字段**：
```typescript
interface User {
    services?: {
        sessionLoginTime?: string; // 会话登录时间 (ISO字符串)
        // ... 其他字段
    }
}
```

**数据库方法**：
```typescript
async updateSessionLoginTime(id: string): Promise<number>
```

### 3. 1小时超时检查机制 ✅

**检查逻辑**：
- 用户通过API访问时自动检查
- 超过1小时自动执行重新登录
- 更新数据库中的登录时间

**实现方法**：
```typescript
async checkAndRefreshSessionIfNeeded(userId: string): Promise<boolean>
```

### 4. 保留手动登录API ✅

**兼容性**：
- 原有手动登录API仍然有效
- 手动登录时自动更新登录时间
- 保持向后兼容

### 5. 保留任务preLogin机制 ✅

**任务执行**：
- 任务执行前仍会检查会话状态
- 保持任务系统的可靠性
- 不影响现有任务功能

## 技术实现

### 1. API访问时的会话检查

在认证中间件中集成会话检查：

```typescript
export const auth = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    // ... 原有认证逻辑
    
    // 检查并刷新会话（如果需要）
    if (user.services?.account) {
        try {
            await sp.checkAndRefreshSessionIfNeeded(id);
        } catch (error) {
            // 会话检查失败不影响API访问，只记录日志
            console.warn(`会话检查失败，用户: ${id}, 错误: ${error.message}`);
        }
    }
    
    // ... 继续处理
}
```

### 2. 智能会话检查逻辑

```typescript
async checkAndRefreshSessionIfNeeded(user_id: string): Promise<boolean> {
    // 1. 获取用户的会话登录时间
    const user = await this.db.findUser({ _id: user_id });
    const lastLoginTime = user.services?.sessionLoginTime;
    
    // 2. 检查是否超过1小时
    if (!lastLoginTime || (Date.now() - new Date(lastLoginTime).getTime()) > 3600000) {
        // 3. 超时则执行重新登录
        return await this.startSession(user_id);
    }
    
    // 4. 未超时则检查会话有效性
    return await this.validateExistingSession(user_id);
}
```

### 3. 登录时间自动更新

```typescript
async startSession(user_id: string): Promise<boolean> {
    // ... 执行登录逻辑
    
    // 登录成功后更新数据库中的会话登录时间
    if (loginResult) {
        await this.db.updateSessionLoginTime(user_id);
    }
    
    return loginResult;
}
```

## 系统优势

### 1. 性能提升
- **减少轮询开销**：不再定时检查所有会话
- **按需执行**：仅在必要时进行会话检查
- **资源节约**：大幅减少CPU和网络资源消耗

### 2. 精确控制
- **1小时精确超时**：不再依赖轮询间隔
- **实时响应**：API访问时立即检查会话状态
- **智能判断**：根据实际使用情况决定是否刷新

### 3. 数据持久化
- **状态保存**：会话登录时间持久化到数据库
- **重启恢复**：系统重启后仍能正确判断会话状态
- **审计追踪**：完整的会话时间记录

### 4. 向后兼容
- **API兼容**：所有原有API接口保持不变
- **功能保留**：手动登录和preLogin机制完全保留
- **平滑升级**：无需修改客户端代码

## 配置说明

### 会话超时时间
```typescript
private sessionTimeoutMs: number = 60 * 60 * 1000; // 1小时
```

### 系统状态查询
```typescript
const status = sessionPool.getSystemStatus();
// 返回:
{
    "sessionTimeout": {
        "timeoutMs": 3600000,
        "timeoutHours": 1
    },
    "taskPool": { ... }
}
```

## 使用场景

### 1. 正常API访问
```typescript
// 用户调用任何API时
GET /user/tasks
Authorization: Bearer JWT_TOKEN

// 系统自动检查会话状态
// 如果超过1小时，自动重新登录
// 用户无感知，API正常返回
```

### 2. 手动登录
```typescript
// 用户手动触发登录
POST /user/startsession
Authorization: Bearer JWT_TOKEN

// 执行登录并更新登录时间
// 重置1小时计时器
```

### 3. 任务执行
```typescript
// 任务执行时自动检查会话
// 如果需要则执行preLogin
// 确保任务执行的可靠性
```

## 监控和调试

### 日志记录
- **会话检查**：记录每次会话状态检查
- **超时处理**：记录会话超时和重新登录
- **时间更新**：记录登录时间更新操作

### 状态查询
```typescript
// 获取系统状态
const status = await fetch('/admin/status');

// 查看会话超时配置
console.log(status.sessionTimeout);
```

### 错误处理
- **会话检查失败**：不影响API访问，记录警告日志
- **登录失败**：返回相应错误，用户可重试
- **数据库错误**：记录错误日志，保持系统稳定

## 迁移说明

### 自动迁移
- **现有用户**：首次API访问时自动设置登录时间
- **现有会话**：根据当前状态判断是否需要重新登录
- **数据兼容**：新字段为可选，不影响现有数据

### 废弃功能
- **定时轮询**：相关配置和方法已废弃
- **保活轮询**：不再执行定时保活检查
- **任务依赖**：不再根据任务数量决定保活

## 最佳实践

### 1. 客户端处理
- **错误重试**：API调用失败时适当重试
- **状态监控**：监控会话相关的错误日志
- **用户体验**：会话刷新对用户透明

### 2. 服务端监控
- **性能监控**：观察会话检查的性能影响
- **错误率监控**：监控会话相关的错误率
- **资源使用**：对比新旧机制的资源消耗

### 3. 运维管理
- **日志分析**：定期分析会话相关日志
- **性能调优**：根据使用情况调整超时时间
- **故障排查**：利用详细日志快速定位问题

## 注意事项

1. **网络延迟**：会话检查可能增加API响应时间
2. **并发访问**：多个API同时访问时的会话检查处理
3. **数据库负载**：频繁的登录时间更新操作
4. **时区处理**：确保时间计算的准确性
5. **异常恢复**：会话检查失败时的降级处理

新的会话保活机制更加高效、精确和可靠，为系统的长期稳定运行提供了坚实的基础！
