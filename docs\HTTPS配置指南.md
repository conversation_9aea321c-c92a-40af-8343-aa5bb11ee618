# HTTPS配置指南

## 概述

本指南详细说明如何为图书馆预约系统配置HTTPS通信，包括开发环境和生产环境的配置方法。

## 开发环境配置

### 1. 生成自签名证书

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x scripts/generate-ssl.sh

# 运行证书生成脚本
./scripts/generate-ssl.sh
```

#### Windows
```cmd
# 运行Windows版本的证书生成脚本
scripts\generate-ssl.bat
```

### 2. 配置环境变量

创建 `.env` 文件或设置环境变量：

```bash
# SSL证书配置
SSL_CERT=./ssl/server.crt
SSL_KEY=./ssl/server.key

# 其他配置
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
```

### 3. 启动HTTPS服务

```bash
# 设置环境变量并启动
export SSL_CERT=./ssl/server.crt
export SSL_KEY=./ssl/server.key
npm run dev
```

服务器将在 `https://localhost:3000` 启动。

### 4. 浏览器配置

由于使用自签名证书，浏览器会显示安全警告：

1. **Chrome/Edge**: 点击"高级" → "继续前往localhost（不安全）"
2. **Firefox**: 点击"高级" → "接受风险并继续"
3. **Safari**: 点击"显示详细信息" → "访问此网站"

## 生产环境配置

### 1. 获取正式SSL证书

#### 使用Let's Encrypt（推荐）

```bash
# 安装Certbot
sudo apt-get update
sudo apt-get install certbot

# 获取证书（需要域名）
sudo certbot certonly --standalone -d yourdomain.com

# 证书位置
# 证书: /etc/letsencrypt/live/yourdomain.com/fullchain.pem
# 私钥: /etc/letsencrypt/live/yourdomain.com/privkey.pem
```

#### 使用商业SSL证书

1. 购买SSL证书
2. 生成CSR（证书签名请求）
3. 提交CSR给证书颁发机构
4. 下载并安装证书

### 2. 配置生产环境变量

```bash
# 生产环境配置
NODE_ENV=production
PORT=443
HOST=0.0.0.0
SSL_CERT=/etc/letsencrypt/live/yourdomain.com/fullchain.pem
SSL_KEY=/etc/letsencrypt/live/yourdomain.com/privkey.pem
FRONTEND_URL=https://yourdomain.com
```

### 3. 配置反向代理（推荐）

#### Nginx配置示例

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Docker环境配置

### 1. Dockerfile中的SSL配置

```dockerfile
# 复制SSL证书（如果包含在镜像中）
COPY ssl/ /app/ssl/

# 设置环境变量
ENV SSL_CERT=/app/ssl/server.crt
ENV SSL_KEY=/app/ssl/server.key
```

### 2. Docker Compose配置

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "443:3000"
    environment:
      - NODE_ENV=production
      - SSL_CERT=/app/ssl/server.crt
      - SSL_KEY=/app/ssl/server.key
    volumes:
      - ./ssl:/app/ssl:ro
```

## 安全最佳实践

### 1. SSL/TLS配置

- 使用TLS 1.2或更高版本
- 禁用不安全的加密套件
- 启用HSTS（HTTP Strict Transport Security）
- 配置正确的SSL证书链

### 2. 应用层安全

```javascript
// 在Express中添加安全头
app.use((req, res, next) => {
    // HSTS
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    
    // 防止点击劫持
    res.setHeader('X-Frame-Options', 'DENY');
    
    // XSS保护
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // 内容类型嗅探保护
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    next();
});
```

### 3. 证书管理

- 定期更新SSL证书
- 监控证书过期时间
- 使用自动化工具管理证书更新

## 故障排除

### 常见问题

1. **证书路径错误**
   ```bash
   # 检查证书文件是否存在
   ls -la ssl/
   
   # 检查文件权限
   chmod 600 ssl/server.key
   chmod 644 ssl/server.crt
   ```

2. **端口占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :443
   
   # 杀死占用进程
   sudo kill -9 <PID>
   ```

3. **防火墙配置**
   ```bash
   # 开放HTTPS端口
   sudo ufw allow 443
   
   # 检查防火墙状态
   sudo ufw status
   ```

### 调试命令

```bash
# 测试SSL证书
openssl s_client -connect localhost:443 -servername localhost

# 检查证书信息
openssl x509 -in ssl/server.crt -text -noout

# 验证私钥和证书匹配
openssl rsa -in ssl/server.key -modulus -noout | openssl md5
openssl x509 -in ssl/server.crt -modulus -noout | openssl md5
```

## 监控和维护

### 1. 证书监控

```bash
# 检查证书过期时间
openssl x509 -in ssl/server.crt -noout -dates

# 自动化证书更新（Let's Encrypt）
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 日志监控

- 监控SSL握手失败
- 检查证书验证错误
- 跟踪HTTPS连接统计

通过以上配置，您的图书馆预约系统将支持安全的HTTPS通信。
