# Installation
> `npm install --save @types/pouchdb-adapter-localstorage`

# Summary
This package contains type definitions for pouchdb-adapter-localstorage (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-localstorage.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-localstorage/index.d.ts)
````ts
/// <reference types="pouchdb-core" />

declare namespace PouchDB {
    namespace LocalStorageAdapter {
        interface LocalStorageAdapterConfiguration extends Configuration.LocalDatabaseConfiguration {
            adapter: "localstorage";
        }
    }

    interface Static {
        new<Content extends {}>(
            name: string | null,
            options: LocalStorageAdapter.LocalStorageAdapterConfiguration,
        ): Database<Content>;
    }
}

declare module "pouchdb-adapter-localstorage" {
    const plugin: PouchDB.Plugin;
    export = plugin;
}

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core)

# Credits
These definitions were written by [Simon Paulger](https://github.com/spaulg), [Brian Geppert](https://github.com/geppy), and [Frederico Galvão](https://github.com/fredgalvao).
