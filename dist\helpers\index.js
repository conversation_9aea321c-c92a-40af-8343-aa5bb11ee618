"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateUserId = exports.generateTaskId = exports.verifyToken = exports.generateToken = exports.verifyPassword = exports.decryptPassword = exports.authentication = exports.getPublicKeyForFrontend = exports.getPublicKey = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const process_1 = __importDefault(require("process"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const node_rsa_1 = __importDefault(require("node-rsa"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const log_1 = require("../config/log");
class RSAKeyManager {
    constructor() {
        this.keyPair = null;
        this.keyDir = path_1.default.join(process_1.default.cwd(), 'keys');
        this.privateKeyPath = path_1.default.join(this.keyDir, 'private.pem');
        this.publicKeyPath = path_1.default.join(this.keyDir, 'public.pem');
        this.ensureKeyDir();
        this.loadOrGenerateKeys();
    }
    static getInstance() {
        if (!RSAKeyManager.instance) {
            RSAKeyManager.instance = new RSAKeyManager();
        }
        return RSAKeyManager.instance;
    }
    ensureKeyDir() {
        if (!fs_1.default.existsSync(this.keyDir)) {
            fs_1.default.mkdirSync(this.keyDir, { recursive: true });
        }
    }
    loadOrGenerateKeys() {
        try {
            if (fs_1.default.existsSync(this.privateKeyPath) && fs_1.default.existsSync(this.publicKeyPath)) {
                const privateKey = fs_1.default.readFileSync(this.privateKeyPath, 'utf8');
                this.keyPair = new node_rsa_1.default(privateKey);
                log_1.MainLogger.info('RSA密钥对加载成功');
            }
            else {
                this.generateNewKeys();
            }
        }
        catch (error) {
            log_1.MainLogger.error('加载RSA密钥失败，生成新密钥:', error);
            this.generateNewKeys();
        }
    }
    generateNewKeys() {
        try {
            this.keyPair = new node_rsa_1.default({ b: 2048 });
            const privateKey = this.keyPair.exportKey('private');
            fs_1.default.writeFileSync(this.privateKeyPath, privateKey);
            const publicKey = this.keyPair.exportKey('public');
            fs_1.default.writeFileSync(this.publicKeyPath, publicKey);
            log_1.MainLogger.info('新的RSA密钥对生成并保存成功');
        }
        catch (error) {
            log_1.MainLogger.error('生成RSA密钥对失败:', error);
            throw error;
        }
    }
    getPublicKey() {
        if (!this.keyPair) {
            throw new Error('RSA密钥对未初始化');
        }
        return this.keyPair.exportKey('public');
    }
    getPublicKeyForFrontend() {
        if (!this.keyPair) {
            throw new Error('RSA密钥对未初始化');
        }
        return this.keyPair.exportKey('pkcs8-public-pem');
    }
    decrypt(encryptedData) {
        if (!this.keyPair) {
            throw new Error('RSA密钥对未初始化');
        }
        try {
            return this.keyPair.decrypt(encryptedData, 'utf8');
        }
        catch (error) {
            throw new Error('RSA解密失败: ' + error.message);
        }
    }
    encrypt(data) {
        if (!this.keyPair) {
            throw new Error('RSA密钥对未初始化');
        }
        try {
            return this.keyPair.encrypt(data, 'base64');
        }
        catch (error) {
            throw new Error('RSA加密失败: ' + error.message);
        }
    }
}
const rsaManager = RSAKeyManager.getInstance();
const getPublicKey = () => {
    return rsaManager.getPublicKey();
};
exports.getPublicKey = getPublicKey;
const getPublicKeyForFrontend = () => {
    return rsaManager.getPublicKeyForFrontend();
};
exports.getPublicKeyForFrontend = getPublicKeyForFrontend;
const authentication = (encryptedPassword) => {
    try {
        const plainPassword = (0, exports.decryptPassword)(encryptedPassword);
        return bcrypt_1.default.hashSync(plainPassword, 10);
    }
    catch (error) {
        throw new Error('密码解密失败: ' + error.message);
    }
};
exports.authentication = authentication;
const decryptPassword = (encryptedPassword) => {
    return rsaManager.decrypt(encryptedPassword);
};
exports.decryptPassword = decryptPassword;
const verifyPassword = (encryptedPassword, hash) => {
    try {
        const plainPassword = rsaManager.decrypt(encryptedPassword);
        return bcrypt_1.default.compareSync(plainPassword, hash);
    }
    catch (error) {
        console.error('密码验证失败:', error);
        return false;
    }
};
exports.verifyPassword = verifyPassword;
const generateToken = (user) => {
    const secret = process_1.default.env.ZJU_BOOKING_SECRET || 'zju-booking-secret';
    return jsonwebtoken_1.default.sign({
        id: user._id,
    }, secret);
};
exports.generateToken = generateToken;
const verifyToken = (token) => {
    const secret = process_1.default.env.ZJU_BOOKING_SECRET || 'zju-booking-secret';
    try {
        const decoded = jsonwebtoken_1.default.verify(token, secret);
        if (typeof decoded == 'object' && decoded.id) {
            return decoded.id;
        }
        return null;
    }
    catch (error) {
        return null;
    }
};
exports.verifyToken = verifyToken;
const generateTaskId = () => {
    return `task_${Math.random().toString(36).slice(2, 12)}`;
};
exports.generateTaskId = generateTaskId;
const generateUserId = () => {
    return `user_${Math.random().toString(36).slice(2, 12)}`;
};
exports.generateUserId = generateUserId;
