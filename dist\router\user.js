"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const user_1 = require("../controllers/user");
const authentication_1 = require("../controllers/authentication");
const error_1 = require("../config/error");
exports.default = (router) => {
    router.get('/user/info', authentication_1.auth, user_1.info, error_1.raiseError);
    router.post('/user/setaccount', authentication_1.auth, user_1.setAccount, error_1.raiseError);
    router.get('/user/getstatus', authentication_1.auth, user_1.getStatus, error_1.raiseError);
    router.post('/user/startsession', authentication_1.auth, user_1.login, error_1.raiseError);
    router.post('/user/book', authentication_1.auth, user_1.book, error_1.raiseError);
    router.post('/user/newtask', authentication_1.auth, user_1.newTask, error_1.raiseError);
    router.post('/user/updatetask', authentication_1.auth, user_1.updateTask, error_1.raiseError);
    router.post('/user/quickselect', authentication_1.auth, user_1.quickSelect, error_1.raiseError);
    router.post('/user/spacedetail', authentication_1.auth, user_1.spaceDetail, error_1.raiseError);
    router.get('/user/getsubscription', authentication_1.auth, user_1.getSubscription, error_1.raiseError);
    router.get('/user/tasks', authentication_1.auth, user_1.getTasks, error_1.raiseError);
    router.delete('/user/deletetasks', authentication_1.auth, user_1.deleteTasks, error_1.raiseError);
    router.get('/user/taskstatus', authentication_1.auth, user_1.getTaskStatus, error_1.raiseError);
    router.get('/user/getstars', authentication_1.auth, user_1.getStars, error_1.raiseError);
    router.post('/user/star', authentication_1.auth, user_1.setStar, error_1.raiseError);
};
