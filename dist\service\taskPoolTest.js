"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runTaskPoolTests = runTaskPoolTests;
const taskPool_1 = require("./taskPool");
const log_1 = require("../config/log");
async function mockExecuteTask(userId, task) {
    log_1.ServiceLogger.info(`模拟执行任务`, {
        userId,
        taskId: task.task_id,
        taskType: task.type,
        target: task.target
    });
    await new Promise(resolve => setTimeout(resolve, 100));
    return Math.random() > 0.1;
}
function testTimeUtils() {
    console.log('\n=== 测试时间工具函数 ===');
    const timeMinutes = taskPool_1.TimeUtils.parseTimeToMinutes('14:30');
    console.log(`14:30 转换为分钟数: ${timeMinutes}`);
    const timeString = taskPool_1.TimeUtils.minutesToTimeString(870);
    console.log(`870分钟转换为时间: ${timeString}`);
    const date = taskPool_1.TimeUtils.parseDate('2024-01-15');
    console.log(`解析日期 2024-01-15: ${date.toDateString()}`);
    const dateString = taskPool_1.TimeUtils.dateToString(new Date(2024, 0, 15));
    console.log(`日期转换为字符串: ${dateString}`);
    const dayOfWeek = taskPool_1.TimeUtils.getDayOfWeek(new Date(2024, 0, 15));
    console.log(`2024-01-15 是星期: ${dayOfWeek}`);
}
function testSingleTaskTime() {
    console.log('\n=== 测试单次任务时间计算 ===');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = taskPool_1.TimeUtils.dateToString(tomorrow);
    const task = {
        active: true,
        task_id: 'test_single_001',
        repetive: false,
        type: 'space',
        target: {
            id: ['room_147'],
            date: tomorrowStr,
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 1,
            time: '09:00'
        }
    };
    const nextExecution = taskPool_1.TimeUtils.calculateNextExecutionTime(task);
    if (nextExecution) {
        console.log(`单次任务下次执行时间: ${new Date(nextExecution).toLocaleString()}`);
    }
    else {
        console.log('单次任务无需执行');
    }
}
function testRepetitiveTaskTime() {
    console.log('\n=== 测试重复任务时间计算 ===');
    const task = {
        active: true,
        task_id: 'test_repeat_001',
        repetive: true,
        repeat: [false, true, false, true, false, true, false],
        type: 'space',
        target: {
            id: ['room_147'],
            date: '2024-01-15',
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 1,
            time: '09:00'
        }
    };
    const nextExecution = taskPool_1.TimeUtils.calculateNextExecutionTime(task);
    if (nextExecution) {
        console.log(`重复任务下次执行时间: ${new Date(nextExecution).toLocaleString()}`);
    }
    else {
        console.log('重复任务无需执行');
    }
}
async function testTaskPoolBasics() {
    console.log('\n=== 测试任务池基本功能 ===');
    const taskPool = new taskPool_1.TaskPool(mockExecuteTask, async (userId, task) => {
        console.log(`模拟更新任务状态: ${task.task_id} -> ${task.status}`);
    }, async (userId) => {
        console.log(`模拟预登录用户: ${userId}`);
        return true;
    });
    const task1 = {
        active: true,
        task_id: 'test_pool_001',
        repetive: false,
        type: 'space',
        target: {
            id: ['room_147'],
            date: '2024-01-15',
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 0,
            time: '09:00'
        }
    };
    const task2 = {
        active: true,
        task_id: 'test_pool_002',
        repetive: true,
        repeat: [false, true, false, false, false, false, false],
        type: 'seat',
        target: {
            id: ['seat_001'],
            date: '2024-01-15',
            start_time: '10:00',
            end_time: '12:00'
        },
        execute: {
            day: 1,
            time: '08:00'
        }
    };
    console.log('创建任务1:', taskPool.createTask('user001', task1));
    console.log('创建任务2:', taskPool.createTask('user002', task2));
    const foundTask = taskPool.queryTask('test_pool_001');
    console.log('查询任务1:', foundTask ? '找到' : '未找到');
    const userTasks = taskPool.queryUserTasks('user001');
    console.log(`用户001的任务数量: ${userTasks.length}`);
    const stats = taskPool.getStats();
    console.log('任务池统计:', stats);
    const allTasks = taskPool.getAllTasksInfo();
    console.log('所有任务信息:', allTasks);
    console.log('等待5秒...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    console.log('删除任务1:', taskPool.deleteTask('test_pool_001'));
    console.log('删除用户002的所有任务:', taskPool.deleteUserTasks('user002'));
    const finalStats = taskPool.getStats();
    console.log('最终统计:', finalStats);
    taskPool.shutdown();
    console.log('任务池已关闭');
}
async function runTaskPoolTests() {
    console.log('开始任务池系统测试...\n');
    try {
        testTimeUtils();
        testSingleTaskTime();
        testRepetitiveTaskTime();
        await testTaskPoolBasics();
        console.log('\n✅ 所有测试完成！');
    }
    catch (error) {
        console.error('\n❌ 测试过程中出现错误:', error);
    }
}
if (require.main === module) {
    runTaskPoolTests();
}
