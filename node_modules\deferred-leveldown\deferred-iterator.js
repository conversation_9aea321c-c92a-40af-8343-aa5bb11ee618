var AbstractIterator = require('abstract-leveldown').AbstractIterator
var inherits = require('inherits')

function DeferredIterator (db, options) {
  AbstractIterator.call(this, db)

  this._options = options
  this._iterator = null
  this._operations = []
}

inherits(DeferredIterator, AbstractIterator)

DeferredIterator.prototype.setDb = function (db) {
  var it = this._iterator = db.iterator(this._options)
  this._operations.forEach(function (op) {
    it[op.method].apply(it, op.args)
  })
}

DeferredIterator.prototype._operation = function (method, args) {
  if (this._iterator) return this._iterator[method].apply(this._iterator, args)
  this._operations.push({ method: method, args: args })
}

'next end'.split(' ').forEach(function (m) {
  DeferredIterator.prototype['_' + m] = function () {
    this._operation(m, arguments)
  }
})

// Must defer seek() rather than _seek() because it requires db._serializeKey to be available
DeferredIterator.prototype.seek = function () {
  this._operation('seek', arguments)
}

module.exports = DeferredIterator
