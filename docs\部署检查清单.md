# 图书馆预约系统部署检查清单

## 🎯 部署前检查

### ✅ 代码准备
- [x] TypeScript编译成功 (`npm run build`)
- [x] 所有依赖安装完成 (`npm install`)
- [x] 环境变量配置文件准备 (`.env.production`)
- [x] 前端页面集成到 `/public` 目录
- [x] RSA密钥对生成 (`keys/` 目录)
- [x] SSL证书准备 (`ssl/` 目录，可选)

### ✅ 功能验证
- [x] 基础健康检查端点 (`/health`)
- [x] 详细健康检查端点 (`/health/detailed`)
- [x] 性能监控端点 (`/metrics`)
- [x] 静态文件服务正常
- [x] API路由正常工作
- [x] 数据库连接正常
- [x] 日志系统正常
- [x] 网络请求日志记录正常

### ✅ 安全配置
- [x] JWT密钥配置
- [x] RSA加密配置
- [x] CORS配置
- [x] 敏感信息过滤
- [x] 环境变量管理

## 🚀 部署方式选择

### 方式一：Docker部署（推荐）

#### 准备工作
```bash
# 1. 确保Docker和Docker Compose已安装
docker --version
docker-compose --version

# 2. 配置生产环境变量
cp .env.example .env.production
# 编辑 .env.production 文件

# 3. 构建和启动
docker-compose -f docker-compose.prod.yml up -d
```

#### 验证部署
```bash
# 检查容器状态
docker-compose -f docker-compose.prod.yml ps

# 检查日志
docker-compose -f docker-compose.prod.yml logs -f app

# 测试健康检查
curl http://localhost:3000/health
```

### 方式二：传统部署

#### 准备工作
```bash
# 1. 安装Node.js 18+
node --version

# 2. 安装PM2
npm install -g pm2

# 3. 构建应用
npm run build

# 4. 启动服务
pm2 start dist/index.js --name "lib-api"
```

## 🔧 环境配置

### 必需的环境变量
```bash
# 基础配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# JWT密钥（必须设置）
JWT_SECRET=your-super-secure-jwt-secret-key

# 前端URL
FRONTEND_URL=https://yourdomain.com

# SSL配置（可选）
SSL_CERT=/path/to/ssl/server.crt
SSL_KEY=/path/to/ssl/server.key
```

### 可选的环境变量
```bash
# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs

# 安全配置
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# 监控配置
HEALTH_CHECK_ENABLED=true
PERFORMANCE_MONITORING=true
```

## 🌐 网络配置

### 端口开放
- **HTTP**: 80 (可选，用于重定向到HTTPS)
- **HTTPS**: 443 (推荐)
- **应用端口**: 3000 (内部使用)

### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow ssh

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

## 🔐 SSL/HTTPS配置

### 开发环境（自签名证书）
```bash
# Linux/macOS
./scripts/generate-ssl.sh

# Windows
scripts\generate-ssl.bat
```

### 生产环境（Let's Encrypt）
```bash
# 安装Certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 配置环境变量
SSL_CERT=/etc/letsencrypt/live/yourdomain.com/fullchain.pem
SSL_KEY=/etc/letsencrypt/live/yourdomain.com/privkey.pem
```

## 📊 监控和维护

### 健康检查
```bash
# 基础健康检查
curl http://localhost:3000/health

# 详细健康检查
curl http://localhost:3000/health/detailed

# 性能指标
curl http://localhost:3000/metrics
```

### 日志监控
```bash
# 查看应用日志
tail -f logs/service.log

# 查看错误日志
tail -f logs/error.log

# 查看网络请求日志
tail -f logs/network-requests.log
```

### 自动监控脚本
```bash
# 给脚本执行权限
chmod +x scripts/monitor.sh

# 运行监控检查
./scripts/monitor.sh

# 设置定时监控（每5分钟）
echo "*/5 * * * * /path/to/lib-api/scripts/monitor.sh --cron" | crontab -
```

## 🔄 更新和维护

### 应用更新
```bash
# Docker方式
git pull origin main
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# 传统方式
git pull origin main
npm run build
pm2 restart lib-api
```

### 数据备份
```bash
# 创建备份
tar -czf backup_$(date +%Y%m%d_%H%M%S).tar.gz data/ logs/ .env

# 定时备份（每天凌晨2点）
echo "0 2 * * * /path/to/backup-script.sh" | crontab -
```

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :3000
   
   # 检查日志
   tail -f logs/error.log
   ```

2. **SSL证书问题**
   ```bash
   # 验证证书
   openssl x509 -in ssl/server.crt -text -noout
   
   # 检查证书和私钥匹配
   openssl rsa -in ssl/server.key -modulus -noout | openssl md5
   openssl x509 -in ssl/server.crt -modulus -noout | openssl md5
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 添加swap
   sudo fallocate -l 1G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### 紧急恢复
```bash
# 快速重启服务
docker-compose -f docker-compose.prod.yml restart

# 或者
pm2 restart lib-api

# 从备份恢复
tar -xzf backup_YYYYMMDD_HHMMSS.tar.gz
```

## ✅ 部署完成验证

### 最终检查清单
- [ ] 服务正常启动
- [ ] 健康检查返回正常状态
- [ ] 前端页面可以访问
- [ ] API接口正常响应
- [ ] SSL证书配置正确（如果启用）
- [ ] 日志正常记录
- [ ] 监控指标正常
- [ ] 备份策略已配置
- [ ] 监控告警已设置

### 性能基准
- **响应时间**: < 500ms (正常), < 1000ms (可接受)
- **内存使用**: < 512MB (正常), < 1GB (可接受)
- **错误率**: < 1% (正常), < 5% (可接受)
- **可用性**: > 99.9%

恭喜！您的图书馆预约系统已成功部署并可以投入使用。
