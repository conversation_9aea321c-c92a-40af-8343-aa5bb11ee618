# 任务池系统说明

## 概述

新的任务池系统完全重写了原有的任务轮询逻辑，实现了更精确、更高效的任务管理机制。

## 主要特性

### 1. 任务池类 (TaskPool)
- 管理所有用户的任务线程
- 提供创建、查询、更新、删除任务线程的接口
- 自动清理已销毁的线程引用

### 2. 任务线程类 (TaskThread)
- 每个任务都是独立的线程
- 根据执行时间自动管理循环
- 确保精确时间执行预约任务
- 自动销毁非重复任务，保留重复任务

### 3. 时间工具类 (TimeUtils)
- 精确的时间计算功能
- 支持单次任务和重复任务的时间调度
- 处理复杂的时间逻辑

## 任务类型参数说明

### 时间相关参数
- **日期格式**: `"yyyy-mm-dd"` (如: "2024-01-15")
- **时间格式**: `"hh:mm"` (如: "14:30")
- **repeat**: 长度恒为7的布尔数组，表示预约每个星期几
  - `[周日, 周一, 周二, 周三, 周四, 周五, 周六]`
  - 例如: `[false, true, false, true, false, true, false]` 表示周一、周三、周五

### 执行参数 (execute)
- **day**: `0 | 1`
  - `0`: 在目标预约日期当天执行
  - `1`: 在目标预约日期前一天执行
- **time**: 执行时间 (格式: "hh:mm")

## 任务执行逻辑

### 单次任务 (repetive: false)
1. 根据 `target.date` 确定预约日期
2. 根据 `execute.day` 计算执行日期
3. 在执行日期的 `execute.time` 时间执行任务
4. 执行完成后自动销毁线程

### 重复任务 (repetive: true)
1. 根据 `repeat` 数组确定重复的星期几
2. 计算下一个符合条件的日期
3. 根据 `execute.day` 和 `execute.time` 计算执行时间
4. 执行完成后继续调度下一次执行

## API 接口

### TaskPool 类

#### 创建任务线程
```typescript
createTask(userId: string, task: BookingTask): boolean
```

#### 查询任务线程
```typescript
queryTask(taskId: string): TaskThread | null
queryUserTasks(userId: string): TaskThread[]
```

#### 更新任务线程
```typescript
updateTask(taskId: string, newTask: BookingTask): boolean
```

#### 删除任务线程
```typescript
deleteTask(taskId: string): boolean
deleteUserTasks(userId: string): number
```

#### 获取统计信息
```typescript
getStats(): {
    totalThreads: number;
    activeThreads: number;
    userTaskCounts: { [userId: string]: number };
    taskTypes: { [type: string]: number };
}
```

### SessionPool 集成

SessionPool 已经集成了新的任务池系统，保持了原有的接口兼容性：

#### 任务管理
```typescript
// 添加任务 (自动创建任务线程)
addTask(user_id: string, task: BookingTask): Promise<boolean>

// 删除任务 (自动销毁任务线程)
removeTask(user_id: string, taskIndex: number): Promise<boolean>
removeTaskById(user_id: string, task_id: string): Promise<boolean>

// 更新用户 (自动同步任务线程)
updateUser(user_id: string): Promise<boolean>
```

#### 系统状态
```typescript
// 获取系统状态
getSystemStatus(): {
    keepAlive: { isActive: boolean; config: any; nextKeepAlive?: string };
    taskPool: { totalThreads: number; activeThreads: number; ... };
}

// 获取任务池统计
getTaskPoolStats(): { ... }

// 获取所有任务信息
getAllTasksInfo(): Array<{ taskId: string; userId: string; ... }>
```

## 使用示例

### 创建单次任务
```typescript
const singleTask: BookingTask = {
    active: true,
    task_id: 'task_001',
    repetive: false,
    type: 'space',
    target: {
        id: ['room_147'],
        date: '2024-01-15',      // 预约日期
        start_time: '14:00',
        end_time: '16:00'
    },
    execute: {
        day: 1,                  // 前一天执行
        time: '09:00'           // 上午9点执行
    }
};

await sessionPool.addTask('user123', singleTask);
```

### 创建重复任务
```typescript
const repeatTask: BookingTask = {
    active: true,
    task_id: 'task_002',
    repetive: true,
    repeat: [false, true, false, true, false, true, false], // 周一、周三、周五
    type: 'space',
    target: {
        id: ['room_147'],
        date: '2024-01-15',      // 在重复任务中可能不使用
        start_time: '14:00',
        end_time: '16:00'
    },
    execute: {
        day: 1,                  // 前一天执行
        time: '09:00'           // 上午9点执行
    }
};

await sessionPool.addTask('user123', repeatTask);
```

## 优势

1. **精确时间控制**: 每个任务都有独立的定时器，确保精确执行
2. **自动生命周期管理**: 单次任务自动销毁，重复任务自动调度
3. **高效资源利用**: 只为需要执行的任务创建线程
4. **完整的监控**: 提供详细的统计信息和状态监控
5. **向后兼容**: 保持原有 SessionPool 接口不变

## 注意事项

1. 任务必须有唯一的 `task_id`
2. 时间格式必须严格遵循 "yyyy-mm-dd" 和 "hh:mm"
3. `repeat` 数组长度必须为7
4. 系统会自动处理时区和夏令时问题
5. 已销毁的线程会自动从任务池中清理
