# SessionPool 轮询系统说明

## 概述

为 SessionPool 类添加了完整的轮询系统，实现了自动化的会话管理和任务执行功能。系统包括三个主要的轮询组件：

1. **状态检查轮询** - 定期检查会话登录状态
2. **任务执行轮询** - 定期执行用户的预约任务  
3. **会话保活轮询** - 保持重要会话的活跃状态

## 主要功能

### 1. 轮询系统管理

#### 启动轮询系统
```typescript
sessionPool.startPollingSystem()
```

#### 停止轮询系统
```typescript
sessionPool.stopPollingSystem()
```

#### 更新轮询配置
```typescript
sessionPool.updatePollingConfig({
    statusCheckInterval: 3 * 60 * 1000,    // 3分钟
    taskExecutionInterval: 20 * 60 * 1000, // 20分钟
    sessionKeepAliveInterval: 8 * 60 * 1000, // 8分钟
    maxRetries: 5,
    retryDelay: 3000
})
```

### 2. 状态检查轮询

**功能：**
- 定期检查所有会话的登录状态
- 自动重新登录失效的会话
- 清理过期和无效的会话

**默认间隔：** 5分钟

**执行内容：**
- 调用 `refreshAllSessions()` 刷新所有会话状态
- 调用 `clearExpiredSessions()` 清理过期会话
- 记录状态检查结果

### 3. 任务执行轮询

**功能：**
- 定期执行用户的预约任务
- 支持多种任务类型（座位预约、空间预约等）
- 自动更新用户活动时间

**默认间隔：** 30分钟

**执行内容：**
- 筛选有待执行任务的用户
- 逐个执行用户任务
- 记录执行结果
- 更新用户最后活动时间

### 4. 会话保活轮询

**功能：**
- 保持重要会话的活跃状态
- 基于用户活动时间和任务数量判断是否需要保活
- 防止重要会话因长时间不活动而失效

**默认间隔：** 10分钟

**保活策略：**
- 有待执行任务的会话
- 最近活动时间在保活延迟时间内的会话

### 5. 手动触发功能

#### 手动状态检查
```typescript
await sessionPool.triggerStatusCheck()
```

#### 手动任务执行
```typescript
await sessionPool.triggerTaskExecution()
```

#### 手动会话保活
```typescript
await sessionPool.triggerKeepAlive()
```

### 6. 状态监控

#### 获取轮询状态
```typescript
const status = sessionPool.getPollingStatus()
console.log(status)
// 输出：
// {
//   isActive: true,
//   config: { statusCheckInterval: 300000, ... },
//   nextStatusCheck: Date,
//   nextTaskExecution: Date,
//   nextKeepAlive: Date
// }
```

#### 获取详细状态报告
```typescript
const report = await sessionPool.getDetailedStatus()
console.log(report)
// 输出：
// {
//   poolStats: { totalSessions: 5, totalTasks: 12, ... },
//   pollingStatus: { isActive: true, ... },
//   sessionDetails: [
//     { id: 'user1', isLoggedIn: true, taskCount: 3, ... }
//   ]
// }
```

## 配置参数

### 默认轮询配置
```typescript
pollingConfig = {
    statusCheckInterval: 5 * 60 * 1000,     // 5分钟检查状态
    taskExecutionInterval: 30 * 60 * 1000,  // 30分钟执行任务
    sessionKeepAliveInterval: 10 * 60 * 1000, // 10分钟保活检查
    maxRetries: 3,                          // 最大重试次数
    retryDelay: 5000                        // 重试延迟5秒
}
```

### 会话保活配置
```typescript
keepAliveDelay: 10 * 60 * 1000  // 10分钟保活延迟
```

## 使用示例

### 基本使用
```typescript
import { sp } from './sessionPool'

// 会话池会自动启动轮询系统
// 添加任务
await sp.addTask('user123', {
    type: 'space',
    target: 'room_147', 
    time: '2024-01-15 14:00'
})

// 更新用户活动时间
sp.updateUserActivity('user123')

// 获取状态
const status = await sp.getDetailedStatus()
```

### 高级使用
```typescript
import { SessionPoolManager } from './sessionPoolExample'

const manager = new SessionPoolManager()
await manager.start()

// 自定义配置
manager.reconfigurePolling({
    statusCheckInterval: 2 * 60 * 1000,  // 2分钟
    taskExecutionInterval: 15 * 60 * 1000 // 15分钟
})

// 手动操作
await manager.manualOperations()
```

## 日志记录

系统使用 `ServiceLogger` 记录所有重要操作：

- **INFO级别：** 轮询系统启动/停止、任务执行结果、状态检查结果
- **WARN级别：** 重复启动轮询系统等警告
- **ERROR级别：** 轮询执行错误、任务执行失败等
- **DEBUG级别：** 用户活动时间更新等详细信息

## 错误处理

- 所有轮询操作都包含 try-catch 错误处理
- 轮询出错不会影响其他轮询的正常运行
- 支持重试机制（配置 maxRetries 和 retryDelay）
- 优雅关闭时会停止所有轮询定时器

## 性能考虑

- 轮询操作之间添加适当延迟，避免请求过于频繁
- 支持动态配置轮询间隔
- 自动清理过期会话，避免内存泄漏
- 批量操作支持，提高效率

## 扩展性

系统设计具有良好的扩展性：

- 可以轻松添加新的轮询类型
- 支持自定义任务执行策略
- 配置参数可动态调整
- 支持插件式的任务处理器

这个轮询系统为图书馆预约系统提供了强大的自动化能力，确保会话的稳定性和任务的及时执行。
