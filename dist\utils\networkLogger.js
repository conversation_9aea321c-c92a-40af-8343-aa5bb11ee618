"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logRequest = logRequest;
exports.logResponse = logResponse;
exports.logError = logError;
exports.addNetworkLoggingInterceptors = addNetworkLoggingInterceptors;
exports.logNetworkEvent = logNetworkEvent;
const log_1 = require("../config/log");
function generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function safeStringify(obj) {
    if (obj === null || obj === undefined) {
        return obj;
    }
    if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean') {
        return obj;
    }
    try {
        const seen = new WeakSet();
        return JSON.parse(JSON.stringify(obj, (key, val) => {
            if (val != null && typeof val === 'object') {
                if (seen.has(val)) {
                    return '[Circular Reference]';
                }
                seen.add(val);
            }
            return val;
        }));
    }
    catch (error) {
        return '[Serialization Error]';
    }
}
function filterSensitiveData(data) {
    if (!data || typeof data !== 'object') {
        return data;
    }
    const sensitiveKeys = ['password', 'token', 'authorization', 'cookie', 'session'];
    const filtered = { ...data };
    for (const key in filtered) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
            filtered[key] = '[FILTERED]';
        }
    }
    return filtered;
}
function logRequest(config) {
    const requestId = config.requestId || generateRequestId();
    const startTime = Date.now();
    config.requestId = requestId;
    config.startTime = startTime;
    const logData = {
        timestamp: new Date().toISOString(),
        type: 'REQUEST',
        method: (config.method || 'GET').toUpperCase(),
        url: config.url || '',
        headers: filterSensitiveData(safeStringify(config.headers)),
        data: config.data ? safeStringify(config.data) : undefined,
        params: config.params ? safeStringify(config.params) : undefined,
        timeout: config.timeout,
        requestId
    };
    log_1.NetworkLogger.debug('HTTP Request', logData);
}
function logResponse(response) {
    const config = response.config;
    const requestId = config.requestId || 'unknown';
    const startTime = config.startTime || Date.now();
    const duration = Date.now() - startTime;
    const logData = {
        timestamp: new Date().toISOString(),
        type: 'RESPONSE',
        status: response.status,
        statusText: response.statusText,
        headers: safeStringify(response.headers),
        data: response.data ? safeStringify(response.data) : undefined,
        duration,
        requestId,
        url: config.url || '',
        method: (config.method || 'GET').toUpperCase()
    };
    log_1.NetworkLogger.debug('HTTP Response', logData);
}
function logError(error) {
    const config = error.config;
    const requestId = config?.requestId || 'unknown';
    const startTime = config?.startTime || Date.now();
    const duration = Date.now() - startTime;
    const logData = {
        timestamp: new Date().toISOString(),
        type: 'ERROR',
        message: error.message,
        code: error.code,
        status: error.response?.status,
        headers: error.response?.headers ? safeStringify(error.response.headers) : undefined,
        data: error.response?.data ? safeStringify(error.response.data) : undefined,
        duration,
        requestId,
        url: config?.url,
        method: config?.method?.toUpperCase(),
        stack: error.stack
    };
    log_1.NetworkLogger.error('HTTP Error', logData);
}
function addNetworkLoggingInterceptors(axiosInstance) {
    axiosInstance.interceptors.request.use((config) => {
        logRequest(config);
        return config;
    }, (error) => {
        log_1.NetworkLogger.error('Request Interceptor Error', {
            timestamp: new Date().toISOString(),
            error: error.message,
            stack: error.stack
        });
        return Promise.reject(error);
    });
    axiosInstance.interceptors.response.use((response) => {
        logResponse(response);
        return response;
    }, (error) => {
        logError(error);
        return Promise.reject(error);
    });
}
function logNetworkEvent(event, data) {
    log_1.NetworkLogger.info('Network Event', {
        timestamp: new Date().toISOString(),
        event,
        data: safeStringify(data)
    });
}
