# Installation
> `npm install --save @types/pouchdb-adapter-fruitdown`

# Summary
This package contains type definitions for pouchdb-adapter-fruitdown (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-fruitdown.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-fruitdown/index.d.ts)
````ts
/// <reference types="pouchdb-core" />

declare namespace PouchDB {
    namespace FruitDOWNAdapter {
        interface FruitDOWNAdapterConfiguration extends Configuration.LocalDatabaseConfiguration {
            adapter: "fruitdown";
        }
    }

    interface Static {
        new<Content extends {}>(
            name: string | null,
            options: FruitDOWNAdapter.FruitDOWNAdapterConfiguration,
        ): Database<Content>;
    }
}

declare module "pouchdb-adapter-fruitdown" {
    const plugin: PouchDB.Plugin;
    export = plugin;
}

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core)

# Credits
These definitions were written by [Simon Paulger](https://github.com/spaulg), [Brian Geppert](https://github.com/geppy), and [Frederico Galvão](https://github.com/fredgalvao).
