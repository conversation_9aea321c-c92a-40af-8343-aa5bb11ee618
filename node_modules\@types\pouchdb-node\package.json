{"name": "@types/pouchdb-node", "version": "6.1.7", "description": "TypeScript definitions for pouchdb-node", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-node", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "spaulg", "url": "https://github.com/spaulg"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-node"}, "scripts": {}, "dependencies": {"@types/pouchdb-adapter-http": "*", "@types/pouchdb-adapter-leveldb": "*", "@types/pouchdb-core": "*", "@types/pouchdb-mapreduce": "*", "@types/pouchdb-replication": "*"}, "typesPublisherContentHash": "295e79f39a5fc1c015206c42de0fc2c49428bb38b0c644d000c2199b1b849077", "typeScriptVersion": "4.5"}