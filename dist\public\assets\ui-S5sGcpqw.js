var ua=t=>{throw TypeError(t)};var Ss=(t,e,n)=>e.has(t)||ua("Cannot "+n);var Tt=(t,e,n)=>(Ss(t,e,"read from private field"),n?n.call(t):e.get(t)),ca=(t,e,n)=>e.has(t)?ua("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n);import{c as P,r as x,s as Sn,w as z,g as Za,o as er,a as ht,u,F as Ht,d as O,C as tr,m as R,b as xs,h as Ue,e as E,f as D,i as $,j as A,k as nr,t as te,n as G,l as or,p as Ps,q as ne,v as To,x as lt,y as cn,z as hn,A as ee,B as J,D as ar,E as rr,G as _s,H as Ms,I as Qe,J as ks,K as sr,L as Ze,M as H,N as ie,T as ir,O as Bs,P as vn,Q as Wt,R as Ke,S as Ct,U as ce,V as X,W as oe,X as Ts,Y as he,Z as lr,_ as xn,$ as ur,a0 as cr,a1 as Is,a2 as Rs}from"./vendor-B4L16uJa.js";function gt(t,e){return t-e*Math.floor(t/e)}const dr=1721426;function nt(t,e,n,o){e=Kt(t,e);let a=e-1,r=-2;return n<=2?r=0:Je(e)&&(r=-1),dr-1+365*a+Math.floor(a/4)-Math.floor(a/100)+Math.floor(a/400)+Math.floor((367*n-362)/12+r+o)}function Je(t){return t%4===0&&(t%100!==0||t%400===0)}function Kt(t,e){return t==="BC"?1-e:e}function Pn(t){let e="AD";return t<=0&&(e="BC",t=1-t),[e,t]}const Fs={standard:[31,28,31,30,31,30,31,31,30,31,30,31],leapyear:[31,29,31,30,31,30,31,31,30,31,30,31]};class we{fromJulianDay(e){let n=e,o=n-dr,a=Math.floor(o/146097),r=gt(o,146097),s=Math.floor(r/36524),i=gt(r,36524),l=Math.floor(i/1461),c=gt(i,1461),d=Math.floor(c/365),f=a*400+s*100+l*4+d+(s!==4&&d!==4?1:0),[p,h]=Pn(f),y=n-nt(p,h,1,1),w=2;n<nt(p,h,3,1)?w=0:Je(h)&&(w=1);let m=Math.floor(((y+w)*12+373)/367),v=n-nt(p,h,m,1)+1;return new Q(p,h,m,v)}toJulianDay(e){return nt(e.era,e.year,e.month,e.day)}getDaysInMonth(e){return Fs[Je(e.year)?"leapyear":"standard"][e.month-1]}getMonthsInYear(e){return 12}getDaysInYear(e){return Je(e.year)?366:365}getYearsInEra(e){return 9999}getEras(){return["BC","AD"]}isInverseEra(e){return e.era==="BC"}balanceDate(e){e.year<=0&&(e.era=e.era==="BC"?"AD":"BC",e.year=1-e.year)}constructor(){this.identifier="gregory"}}const Ls={"001":1,AD:1,AE:6,AF:6,AI:1,AL:1,AM:1,AN:1,AR:1,AT:1,AU:1,AX:1,AZ:1,BA:1,BE:1,BG:1,BH:6,BM:1,BN:1,BY:1,CH:1,CL:1,CM:1,CN:1,CR:1,CY:1,CZ:1,DE:1,DJ:6,DK:1,DZ:6,EC:1,EE:1,EG:6,ES:1,FI:1,FJ:1,FO:1,FR:1,GB:1,GE:1,GF:1,GP:1,GR:1,HR:1,HU:1,IE:1,IQ:6,IR:6,IS:1,IT:1,JO:6,KG:1,KW:6,KZ:1,LB:1,LI:1,LK:1,LT:1,LU:1,LV:1,LY:6,MC:1,MD:1,ME:1,MK:1,MN:1,MQ:1,MV:5,MY:1,NL:1,NO:1,NZ:1,OM:6,PL:1,QA:6,RE:1,RO:1,RS:1,RU:1,SD:6,SE:1,SI:1,SK:1,SM:1,SY:6,TJ:1,TM:1,TR:1,UA:1,UY:1,UZ:1,VA:1,VN:1,XK:1};function ot(t,e){return e=se(e,t.calendar),t.era===e.era&&t.year===e.year&&t.month===e.month&&t.day===e.day}function fr(t,e){return e=se(e,t.calendar),t=vo(t),e=vo(e),t.era===e.era&&t.year===e.year&&t.month===e.month}function Jn(t,e){return Io(t.calendar,e.calendar)&&ot(t,e)}function da(t,e){return Io(t.calendar,e.calendar)&&fr(t,e)}function Io(t,e){var n,o,a,r;return(r=(a=(n=t.isEqual)===null||n===void 0?void 0:n.call(t,e))!==null&&a!==void 0?a:(o=e.isEqual)===null||o===void 0?void 0:o.call(e,t))!==null&&r!==void 0?r:t.identifier===e.identifier}function Vs(t,e){return ot(t,hr(e))}function pr(t,e,n){let o=t.calendar.toJulianDay(t),a=Ws(e),r=Math.ceil(o+1-a)%7;return r<0&&(r+=7),r}function Ns(t){return Ve(Date.now(),t)}function hr(t){return zs(Ns(t))}function vr(t,e){return t.calendar.toJulianDay(t)-e.calendar.toJulianDay(e)}function Us(t,e){return fa(t)-fa(e)}function fa(t){return t.hour*36e5+t.minute*6e4+t.second*1e3+t.millisecond}let Gn=null;function Yt(){return Gn==null&&(Gn=new Intl.DateTimeFormat().resolvedOptions().timeZone),Gn}function vo(t){return t.subtract({days:t.day-1})}function pa(t){return t.add({days:t.calendar.getDaysInMonth(t)-t.day})}const ha=new Map;function Hs(t){if(Intl.Locale){let n=ha.get(t);return n||(n=new Intl.Locale(t).maximize().region,n&&ha.set(t,n)),n}let e=t.split("-")[1];return e==="u"?void 0:e}function Ws(t){let e=Hs(t);return e&&Ls[e]||0}function Dt(t){t=se(t,new we);let e=Kt(t.era,t.year);return mr(e,t.month,t.day,t.hour,t.minute,t.second,t.millisecond)}function mr(t,e,n,o,a,r,s){let i=new Date;return i.setUTCHours(o,a,r,s),i.setUTCFullYear(t,e-1,n),i.getTime()}function mo(t,e){if(e==="UTC")return 0;if(t>0&&e===Yt())return new Date(t).getTimezoneOffset()*-6e4;let{year:n,month:o,day:a,hour:r,minute:s,second:i}=yr(t,e);return mr(n,o,a,r,s,i,0)-Math.floor(t/1e3)*1e3}const va=new Map;function yr(t,e){let n=va.get(e);n||(n=new Intl.DateTimeFormat("en-US",{timeZone:e,hour12:!1,era:"short",year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}),va.set(e,n));let o=n.formatToParts(new Date(t)),a={};for(let r of o)r.type!=="literal"&&(a[r.type]=r.value);return{year:a.era==="BC"||a.era==="B"?-a.year+1:+a.year,month:+a.month,day:+a.day,hour:a.hour==="24"?0:+a.hour,minute:+a.minute,second:+a.second}}const ma=864e5;function Ks(t,e,n,o){return(n===o?[n]:[n,o]).filter(r=>Ys(t,e,r))}function Ys(t,e,n){let o=yr(n,e);return t.year===o.year&&t.month===o.month&&t.day===o.day&&t.hour===o.hour&&t.minute===o.minute&&t.second===o.second}function Le(t,e,n="compatible"){let o=Et(t);if(e==="UTC")return Dt(o);if(e===Yt()&&n==="compatible"){o=se(o,new we);let l=new Date,c=Kt(o.era,o.year);return l.setFullYear(c,o.month-1,o.day),l.setHours(o.hour,o.minute,o.second,o.millisecond),l.getTime()}let a=Dt(o),r=mo(a-ma,e),s=mo(a+ma,e),i=Ks(o,e,a-r,a-s);if(i.length===1)return i[0];if(i.length>1)switch(n){case"compatible":case"earlier":return i[0];case"later":return i[i.length-1];case"reject":throw new RangeError("Multiple possible absolute times found")}switch(n){case"earlier":return Math.min(a-r,a-s);case"compatible":case"later":return Math.max(a-r,a-s);case"reject":throw new RangeError("No such absolute time found")}}function gr(t,e,n="compatible"){return new Date(Le(t,e,n))}function Ve(t,e){let n=mo(t,e),o=new Date(t+n),a=o.getUTCFullYear(),r=o.getUTCMonth()+1,s=o.getUTCDate(),i=o.getUTCHours(),l=o.getUTCMinutes(),c=o.getUTCSeconds(),d=o.getUTCMilliseconds();return new Ot(a<1?"BC":"AD",a<1?-a+1:a,r,s,e,n,i,l,c,d)}function zs(t){return new Q(t.calendar,t.era,t.year,t.month,t.day)}function Et(t,e){let n=0,o=0,a=0,r=0;if("timeZone"in t)({hour:n,minute:o,second:a,millisecond:r}=t);else if("hour"in t&&!e)return t;return e&&({hour:n,minute:o,second:a,millisecond:r}=e),new At(t.calendar,t.era,t.year,t.month,t.day,n,o,a,r)}function se(t,e){if(Io(t.calendar,e))return t;let n=e.fromJulianDay(t.calendar.toJulianDay(t)),o=t.copy();return o.calendar=e,o.era=n.era,o.year=n.year,o.month=n.month,o.day=n.day,at(o),o}function js(t,e,n){if(t instanceof Ot)return t.timeZone===e?t:Js(t,e);let o=Le(t,e,n);return Ve(o,e)}function qs(t){let e=Dt(t)-t.offset;return new Date(e)}function Js(t,e){let n=Dt(t)-t.offset;return se(Ve(n,e),t.calendar)}const It=36e5;function _n(t,e){let n=t.copy(),o="hour"in n?Zs(n,e):0;yo(n,e.years||0),n.calendar.balanceYearMonth&&n.calendar.balanceYearMonth(n,t),n.month+=e.months||0,go(n),br(n),n.day+=(e.weeks||0)*7,n.day+=e.days||0,n.day+=o,Gs(n),n.calendar.balanceDate&&n.calendar.balanceDate(n),n.year<1&&(n.year=1,n.month=1,n.day=1);let a=n.calendar.getYearsInEra(n);if(n.year>a){var r,s;let l=(r=(s=n.calendar).isInverseEra)===null||r===void 0?void 0:r.call(s,n);n.year=a,n.month=l?1:n.calendar.getMonthsInYear(n),n.day=l?1:n.calendar.getDaysInMonth(n)}n.month<1&&(n.month=1,n.day=1);let i=n.calendar.getMonthsInYear(n);return n.month>i&&(n.month=i,n.day=n.calendar.getDaysInMonth(n)),n.day=Math.max(1,Math.min(n.calendar.getDaysInMonth(n),n.day)),n}function yo(t,e){var n,o;!((n=(o=t.calendar).isInverseEra)===null||n===void 0)&&n.call(o,t)&&(e=-e),t.year+=e}function go(t){for(;t.month<1;)yo(t,-1),t.month+=t.calendar.getMonthsInYear(t);let e=0;for(;t.month>(e=t.calendar.getMonthsInYear(t));)t.month-=e,yo(t,1)}function Gs(t){for(;t.day<1;)t.month--,go(t),t.day+=t.calendar.getDaysInMonth(t);for(;t.day>t.calendar.getDaysInMonth(t);)t.day-=t.calendar.getDaysInMonth(t),t.month++,go(t)}function br(t){t.month=Math.max(1,Math.min(t.calendar.getMonthsInYear(t),t.month)),t.day=Math.max(1,Math.min(t.calendar.getDaysInMonth(t),t.day))}function at(t){t.calendar.constrainDate&&t.calendar.constrainDate(t),t.year=Math.max(1,Math.min(t.calendar.getYearsInEra(t),t.year)),br(t)}function wr(t){let e={};for(let n in t)typeof t[n]=="number"&&(e[n]=-t[n]);return e}function $r(t,e){return _n(t,wr(e))}function Ro(t,e){let n=t.copy();return e.era!=null&&(n.era=e.era),e.year!=null&&(n.year=e.year),e.month!=null&&(n.month=e.month),e.day!=null&&(n.day=e.day),at(n),n}function mn(t,e){let n=t.copy();return e.hour!=null&&(n.hour=e.hour),e.minute!=null&&(n.minute=e.minute),e.second!=null&&(n.second=e.second),e.millisecond!=null&&(n.millisecond=e.millisecond),Qs(n),n}function Xs(t){t.second+=Math.floor(t.millisecond/1e3),t.millisecond=tn(t.millisecond,1e3),t.minute+=Math.floor(t.second/60),t.second=tn(t.second,60),t.hour+=Math.floor(t.minute/60),t.minute=tn(t.minute,60);let e=Math.floor(t.hour/24);return t.hour=tn(t.hour,24),e}function Qs(t){t.millisecond=Math.max(0,Math.min(t.millisecond,1e3)),t.second=Math.max(0,Math.min(t.second,59)),t.minute=Math.max(0,Math.min(t.minute,59)),t.hour=Math.max(0,Math.min(t.hour,23))}function tn(t,e){let n=t%e;return n<0&&(n+=e),n}function Zs(t,e){return t.hour+=e.hours||0,t.minute+=e.minutes||0,t.second+=e.seconds||0,t.millisecond+=e.milliseconds||0,Xs(t)}function Fo(t,e,n,o){let a=t.copy();switch(e){case"era":{let i=t.calendar.getEras(),l=i.indexOf(t.era);if(l<0)throw new Error("Invalid era: "+t.era);l=Ne(l,n,0,i.length-1,o==null?void 0:o.round),a.era=i[l],at(a);break}case"year":var r,s;!((r=(s=a.calendar).isInverseEra)===null||r===void 0)&&r.call(s,a)&&(n=-n),a.year=Ne(t.year,n,-1/0,9999,o==null?void 0:o.round),a.year===-1/0&&(a.year=1),a.calendar.balanceYearMonth&&a.calendar.balanceYearMonth(a,t);break;case"month":a.month=Ne(t.month,n,1,t.calendar.getMonthsInYear(t),o==null?void 0:o.round);break;case"day":a.day=Ne(t.day,n,1,t.calendar.getDaysInMonth(t),o==null?void 0:o.round);break;default:throw new Error("Unsupported field "+e)}return t.calendar.balanceDate&&t.calendar.balanceDate(a),at(a),a}function Cr(t,e,n,o){let a=t.copy();switch(e){case"hour":{let r=t.hour,s=0,i=23;if((o==null?void 0:o.hourCycle)===12){let l=r>=12;s=l?12:0,i=l?23:11}a.hour=Ne(r,n,s,i,o==null?void 0:o.round);break}case"minute":a.minute=Ne(t.minute,n,0,59,o==null?void 0:o.round);break;case"second":a.second=Ne(t.second,n,0,59,o==null?void 0:o.round);break;case"millisecond":a.millisecond=Ne(t.millisecond,n,0,999,o==null?void 0:o.round);break;default:throw new Error("Unsupported field "+e)}return a}function Ne(t,e,n,o,a=!1){if(a){t+=Math.sign(e),t<n&&(t=o);let r=Math.abs(e);e>0?t=Math.ceil(t/r)*r:t=Math.floor(t/r)*r,t>o&&(t=n)}else t+=e,t<n?t=o-(n-t-1):t>o&&(t=n+(t-o-1));return t}function Dr(t,e){let n;if(e.years!=null&&e.years!==0||e.months!=null&&e.months!==0||e.weeks!=null&&e.weeks!==0||e.days!=null&&e.days!==0){let a=_n(Et(t),{years:e.years,months:e.months,weeks:e.weeks,days:e.days});n=Le(a,t.timeZone)}else n=Dt(t)-t.offset;n+=e.milliseconds||0,n+=(e.seconds||0)*1e3,n+=(e.minutes||0)*6e4,n+=(e.hours||0)*36e5;let o=Ve(n,t.timeZone);return se(o,t.calendar)}function ei(t,e){return Dr(t,wr(e))}function ti(t,e,n,o){switch(e){case"hour":{let a=0,r=23;if((o==null?void 0:o.hourCycle)===12){let y=t.hour>=12;a=y?12:0,r=y?23:11}let s=Et(t),i=se(mn(s,{hour:a}),new we),l=[Le(i,t.timeZone,"earlier"),Le(i,t.timeZone,"later")].filter(y=>Ve(y,t.timeZone).day===i.day)[0],c=se(mn(s,{hour:r}),new we),d=[Le(c,t.timeZone,"earlier"),Le(c,t.timeZone,"later")].filter(y=>Ve(y,t.timeZone).day===c.day).pop(),f=Dt(t)-t.offset,p=Math.floor(f/It),h=f%It;return f=Ne(p,n,Math.floor(l/It),Math.floor(d/It),o==null?void 0:o.round)*It+h,se(Ve(f,t.timeZone),t.calendar)}case"minute":case"second":case"millisecond":return Cr(t,e,n,o);case"era":case"year":case"month":case"day":{let a=Fo(Et(t),e,n,o),r=Le(a,t.timeZone);return se(Ve(r,t.timeZone),t.calendar)}default:throw new Error("Unsupported field "+e)}}function ni(t,e,n){let o=Et(t),a=mn(Ro(o,e),e);if(a.compare(o)===0)return t;let r=Le(a,t.timeZone,n);return se(Ve(r,t.timeZone),t.calendar)}const oi=/^([+-]\d{6}|\d{4})-(\d{2})-(\d{2})$/;function Pd(t){let e=t.match(oi);if(!e)throw new Error("Invalid ISO 8601 date string: "+t);let n=new Q(Xn(e[1],0,9999),Xn(e[2],1,12),1);return n.day=Xn(e[3],1,n.calendar.getDaysInMonth(n)),n}function Xn(t,e,n){let o=Number(t);if(o<e||o>n)throw new RangeError(`Value out of range: ${e} <= ${o} <= ${n}`);return o}function ai(t){return`${String(t.hour).padStart(2,"0")}:${String(t.minute).padStart(2,"0")}:${String(t.second).padStart(2,"0")}${t.millisecond?String(t.millisecond/1e3).slice(1):""}`}function Er(t){let e=se(t,new we),n;return e.era==="BC"?n=e.year===1?"0000":"-"+String(Math.abs(1-e.year)).padStart(6,"00"):n=String(e.year).padStart(4,"0"),`${n}-${String(e.month).padStart(2,"0")}-${String(e.day).padStart(2,"0")}`}function Ar(t){return`${Er(t)}T${ai(t)}`}function ri(t){let e=Math.sign(t)<0?"-":"+";t=Math.abs(t);let n=Math.floor(t/36e5),o=t%36e5/6e4;return`${e}${String(n).padStart(2,"0")}:${String(o).padStart(2,"0")}`}function si(t){return`${Ar(t)}${ri(t.offset)}[${t.timeZone}]`}function ii(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Lo(t,e,n){ii(t,e),e.set(t,n)}function Vo(t){let e=typeof t[0]=="object"?t.shift():new we,n;if(typeof t[0]=="string")n=t.shift();else{let s=e.getEras();n=s[s.length-1]}let o=t.shift(),a=t.shift(),r=t.shift();return[e,n,o,a,r]}var li=new WeakMap;class Q{copy(){return this.era?new Q(this.calendar,this.era,this.year,this.month,this.day):new Q(this.calendar,this.year,this.month,this.day)}add(e){return _n(this,e)}subtract(e){return $r(this,e)}set(e){return Ro(this,e)}cycle(e,n,o){return Fo(this,e,n,o)}toDate(e){return gr(this,e)}toString(){return Er(this)}compare(e){return vr(this,e)}constructor(...e){Lo(this,li,{writable:!0,value:void 0});let[n,o,a,r,s]=Vo(e);this.calendar=n,this.era=o,this.year=a,this.month=r,this.day=s,at(this)}}var ui=new WeakMap;class At{copy(){return this.era?new At(this.calendar,this.era,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond):new At(this.calendar,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond)}add(e){return _n(this,e)}subtract(e){return $r(this,e)}set(e){return Ro(mn(this,e),e)}cycle(e,n,o){switch(e){case"era":case"year":case"month":case"day":return Fo(this,e,n,o);default:return Cr(this,e,n,o)}}toDate(e,n){return gr(this,e,n)}toString(){return Ar(this)}compare(e){let n=vr(this,e);return n===0?Us(this,Et(e)):n}constructor(...e){Lo(this,ui,{writable:!0,value:void 0});let[n,o,a,r,s]=Vo(e);this.calendar=n,this.era=o,this.year=a,this.month=r,this.day=s,this.hour=e.shift()||0,this.minute=e.shift()||0,this.second=e.shift()||0,this.millisecond=e.shift()||0,at(this)}}var ci=new WeakMap;class Ot{copy(){return this.era?new Ot(this.calendar,this.era,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond):new Ot(this.calendar,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond)}add(e){return Dr(this,e)}subtract(e){return ei(this,e)}set(e,n){return ni(this,e,n)}cycle(e,n,o){return ti(this,e,n,o)}toDate(){return qs(this)}toString(){return si(this)}toAbsoluteString(){return this.toDate().toISOString()}compare(e){return this.toDate().getTime()-js(e,this.timeZone).toDate().getTime()}constructor(...e){Lo(this,ci,{writable:!0,value:void 0});let[n,o,a,r,s]=Vo(e),i=e.shift(),l=e.shift();this.calendar=n,this.era=o,this.year=a,this.month=r,this.day=s,this.timeZone=i,this.offset=l,this.hour=e.shift()||0,this.minute=e.shift()||0,this.second=e.shift()||0,this.millisecond=e.shift()||0,at(this)}}const bt=[[1868,9,8],[1912,7,30],[1926,12,25],[1989,1,8],[2019,5,1]],di=[[1912,7,29],[1926,12,24],[1989,1,7],[2019,4,30]],dn=[1867,1911,1925,1988,2018],qe=["meiji","taisho","showa","heisei","reiwa"];function ya(t){const e=bt.findIndex(([n,o,a])=>t.year<n||t.year===n&&t.month<o||t.year===n&&t.month===o&&t.day<a);return e===-1?bt.length-1:e===0?0:e-1}function Qn(t){let e=dn[qe.indexOf(t.era)];if(!e)throw new Error("Unknown era: "+t.era);return new Q(t.year+e,t.month,t.day)}class fi extends we{fromJulianDay(e){let n=super.fromJulianDay(e),o=ya(n);return new Q(this,qe[o],n.year-dn[o],n.month,n.day)}toJulianDay(e){return super.toJulianDay(Qn(e))}balanceDate(e){let n=Qn(e),o=ya(n);qe[o]!==e.era&&(e.era=qe[o],e.year=n.year-dn[o]),this.constrainDate(e)}constrainDate(e){let n=qe.indexOf(e.era),o=di[n];if(o!=null){let[a,r,s]=o,i=a-dn[n];e.year=Math.max(1,Math.min(i,e.year)),e.year===i&&(e.month=Math.min(r,e.month),e.month===r&&(e.day=Math.min(s,e.day)))}if(e.year===1&&n>=0){let[,a,r]=bt[n];e.month=Math.max(a,e.month),e.month===a&&(e.day=Math.max(r,e.day))}}getEras(){return qe}getYearsInEra(e){let n=qe.indexOf(e.era),o=bt[n],a=bt[n+1];if(a==null)return 9999-o[0]+1;let r=a[0]-o[0];return(e.month<a[1]||e.month===a[1]&&e.day<a[2])&&r++,r}getDaysInMonth(e){return super.getDaysInMonth(Qn(e))}getMinimumMonthInYear(e){let n=ga(e);return n?n[1]:1}getMinimumDayInMonth(e){let n=ga(e);return n&&e.month===n[1]?n[2]:1}constructor(...e){super(...e),this.identifier="japanese"}}function ga(t){if(t.year===1){let e=qe.indexOf(t.era);return bt[e]}}const Or=-543;class pi extends we{fromJulianDay(e){let n=super.fromJulianDay(e),o=Kt(n.era,n.year);return new Q(this,o-Or,n.month,n.day)}toJulianDay(e){return super.toJulianDay(ba(e))}getEras(){return["BE"]}getDaysInMonth(e){return super.getDaysInMonth(ba(e))}balanceDate(){}constructor(...e){super(...e),this.identifier="buddhist"}}function ba(t){let[e,n]=Pn(t.year+Or);return new Q(e,n,t.month,t.day)}const yn=1911;function Sr(t){return t.era==="minguo"?t.year+yn:1-t.year+yn}function wa(t){let e=t-yn;return e>0?["minguo",e]:["before_minguo",1-e]}class hi extends we{fromJulianDay(e){let n=super.fromJulianDay(e),o=Kt(n.era,n.year),[a,r]=wa(o);return new Q(this,a,r,n.month,n.day)}toJulianDay(e){return super.toJulianDay($a(e))}getEras(){return["before_minguo","minguo"]}balanceDate(e){let[n,o]=wa(Sr(e));e.era=n,e.year=o}isInverseEra(e){return e.era==="before_minguo"}getDaysInMonth(e){return super.getDaysInMonth($a(e))}getYearsInEra(e){return e.era==="before_minguo"?9999:9999-yn}constructor(...e){super(...e),this.identifier="roc"}}function $a(t){let[e,n]=Pn(Sr(t));return new Q(e,n,t.month,t.day)}const Ca=1948320,Da=[0,31,62,93,124,155,186,216,246,276,306,336];class vi{fromJulianDay(e){let n=e-Ca,o=1+Math.floor((33*n+3)/12053),a=365*(o-1)+Math.floor((8*o+21)/33),r=n-a,s=r<216?Math.floor(r/31):Math.floor((r-6)/30),i=r-Da[s]+1;return new Q(this,o,s+1,i)}toJulianDay(e){let n=Ca-1+365*(e.year-1)+Math.floor((8*e.year+21)/33);return n+=Da[e.month-1],n+=e.day,n}getMonthsInYear(){return 12}getDaysInMonth(e){return e.month<=6?31:e.month<=11||gt(25*e.year+11,33)<8?30:29}getEras(){return["AP"]}getYearsInEra(){return 9377}constructor(){this.identifier="persian"}}const Zn=78,Ea=80;class mi extends we{fromJulianDay(e){let n=super.fromJulianDay(e),o=n.year-Zn,a=e-nt(n.era,n.year,1,1),r;a<Ea?(o--,r=Je(n.year-1)?31:30,a+=r+155+90+10):(r=Je(n.year)?31:30,a-=Ea);let s,i;if(a<r)s=1,i=a+1;else{let l=a-r;l<155?(s=Math.floor(l/31)+2,i=l%31+1):(l-=155,s=Math.floor(l/30)+7,i=l%30+1)}return new Q(this,o,s,i)}toJulianDay(e){let n=e.year+Zn,[o,a]=Pn(n),r,s;return Je(a)?(r=31,s=nt(o,a,3,21)):(r=30,s=nt(o,a,3,22)),e.month===1?s+e.day-1:(s+=r+Math.min(e.month-2,5)*31,e.month>=8&&(s+=(e.month-7)*30),s+=e.day-1,s)}getDaysInMonth(e){return e.month===1&&Je(e.year+Zn)||e.month>=2&&e.month<=6?31:30}getYearsInEra(){return 9919}getEras(){return["saka"]}balanceDate(){}constructor(...e){super(...e),this.identifier="indian"}}const gn=1948440,Aa=1948439,pe=1300,vt=1600,yi=460322;function bn(t,e,n,o){return o+Math.ceil(29.5*(n-1))+(e-1)*354+Math.floor((3+11*e)/30)+t-1}function xr(t,e,n){let o=Math.floor((30*(n-e)+10646)/10631),a=Math.min(12,Math.ceil((n-(29+bn(e,o,1,1)))/29.5)+1),r=n-bn(e,o,a,1)+1;return new Q(t,o,a,r)}function Oa(t){return(14+11*t)%30<11}class No{fromJulianDay(e){return xr(this,gn,e)}toJulianDay(e){return bn(gn,e.year,e.month,e.day)}getDaysInMonth(e){let n=29+e.month%2;return e.month===12&&Oa(e.year)&&n++,n}getMonthsInYear(){return 12}getDaysInYear(e){return Oa(e.year)?355:354}getYearsInEra(){return 9665}getEras(){return["AH"]}constructor(){this.identifier="islamic-civil"}}class gi extends No{fromJulianDay(e){return xr(this,Aa,e)}toJulianDay(e){return bn(Aa,e.year,e.month,e.day)}constructor(...e){super(...e),this.identifier="islamic-tbla"}}const bi="qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=";let bo,wt;function fn(t){return yi+wt[t-pe]}function Rt(t,e){let n=t-pe,o=1<<11-(e-1);return(bo[n]&o)===0?29:30}function Sa(t,e){let n=fn(t);for(let o=1;o<e;o++)n+=Rt(t,o);return n}function xa(t){return wt[t+1-pe]-wt[t-pe]}class wi extends No{fromJulianDay(e){let n=e-gn,o=fn(pe),a=fn(vt);if(n<o||n>a)return super.fromJulianDay(e);{let r=pe-1,s=1,i=1;for(;i>0;){r++,i=n-fn(r)+1;let l=xa(r);if(i===l){s=12;break}else if(i<l){let c=Rt(r,s);for(s=1;i>c;)i-=c,s++,c=Rt(r,s);break}}return new Q(this,r,s,n-Sa(r,s)+1)}}toJulianDay(e){return e.year<pe||e.year>vt?super.toJulianDay(e):gn+Sa(e.year,e.month)+(e.day-1)}getDaysInMonth(e){return e.year<pe||e.year>vt?super.getDaysInMonth(e):Rt(e.year,e.month)}getDaysInYear(e){return e.year<pe||e.year>vt?super.getDaysInYear(e):xa(e.year)}constructor(){if(super(),this.identifier="islamic-umalqura",bo||(bo=new Uint16Array(Uint8Array.from(atob(bi),e=>e.charCodeAt(0)).buffer)),!wt){wt=new Uint32Array(vt-pe+1);let e=0;for(let n=pe;n<=vt;n++){wt[n-pe]=e;for(let o=1;o<=12;o++)e+=Rt(n,o)}}}}const Pa=347997,Pr=1080,_r=24*Pr,$i=29,Ci=12*Pr+793,Di=$i*_r+Ci;function tt(t){return gt(t*7+1,19)<7}function pn(t){let e=Math.floor((235*t-234)/19),n=12084+13753*e,o=e*29+Math.floor(n/25920);return gt(3*(o+1),7)<3&&(o+=1),o}function Ei(t){let e=pn(t-1),n=pn(t);return pn(t+1)-n===356?2:n-e===382?1:0}function Ft(t){return pn(t)+Ei(t)}function Mr(t){return Ft(t+1)-Ft(t)}function Ai(t){let e=Mr(t);switch(e>380&&(e-=30),e){case 353:return 0;case 354:return 1;case 355:return 2}}function nn(t,e){if(e>=6&&!tt(t)&&e++,e===4||e===7||e===9||e===11||e===13)return 29;let n=Ai(t);return e===2?n===2?30:29:e===3?n===0?29:30:e===6?tt(t)?30:0:30}class Oi{fromJulianDay(e){let n=e-Pa,o=n*_r/Di,a=Math.floor((19*o+234)/235)+1,r=Ft(a),s=Math.floor(n-r);for(;s<1;)a--,r=Ft(a),s=Math.floor(n-r);let i=1,l=0;for(;l<s;)l+=nn(a,i),i++;i--,l-=nn(a,i);let c=s-l;return new Q(this,a,i,c)}toJulianDay(e){let n=Ft(e.year);for(let o=1;o<e.month;o++)n+=nn(e.year,o);return n+e.day+Pa}getDaysInMonth(e){return nn(e.year,e.month)}getMonthsInYear(e){return tt(e.year)?13:12}getDaysInYear(e){return Mr(e.year)}getYearsInEra(){return 9999}getEras(){return["AM"]}balanceYearMonth(e,n){n.year!==e.year&&(tt(n.year)&&!tt(e.year)&&n.month>6?e.month--:!tt(n.year)&&tt(e.year)&&n.month>6&&e.month++)}constructor(){this.identifier="hebrew"}}const wo=1723856,_a=1824665,$o=5500;function wn(t,e,n,o){return t+365*e+Math.floor(e/4)+30*(n-1)+o-1}function Uo(t,e){let n=Math.floor(4*(e-t)/1461),o=1+Math.floor((e-wn(t,n,1,1))/30),a=e+1-wn(t,n,o,1);return[n,o,a]}function kr(t){return Math.floor(t%4/3)}function Br(t,e){return e%13!==0?30:kr(t)+5}class Ho{fromJulianDay(e){let[n,o,a]=Uo(wo,e),r="AM";return n<=0&&(r="AA",n+=$o),new Q(this,r,n,o,a)}toJulianDay(e){let n=e.year;return e.era==="AA"&&(n-=$o),wn(wo,n,e.month,e.day)}getDaysInMonth(e){return Br(e.year,e.month)}getMonthsInYear(){return 13}getDaysInYear(e){return 365+kr(e.year)}getYearsInEra(e){return e.era==="AA"?9999:9991}getEras(){return["AA","AM"]}constructor(){this.identifier="ethiopic"}}class Si extends Ho{fromJulianDay(e){let[n,o,a]=Uo(wo,e);return n+=$o,new Q(this,"AA",n,o,a)}getEras(){return["AA"]}getYearsInEra(){return 9999}constructor(...e){super(...e),this.identifier="ethioaa"}}class xi extends Ho{fromJulianDay(e){let[n,o,a]=Uo(_a,e),r="CE";return n<=0&&(r="BCE",n=1-n),new Q(this,r,n,o,a)}toJulianDay(e){let n=e.year;return e.era==="BCE"&&(n=1-n),wn(_a,n,e.month,e.day)}getDaysInMonth(e){let n=e.year;return e.era==="BCE"&&(n=1-n),Br(n,e.month)}isInverseEra(e){return e.era==="BCE"}balanceDate(e){e.year<=0&&(e.era=e.era==="BCE"?"CE":"BCE",e.year=1-e.year)}getEras(){return["BCE","CE"]}getYearsInEra(e){return e.era==="BCE"?9999:9715}constructor(...e){super(...e),this.identifier="coptic"}}function Pi(t){switch(t){case"buddhist":return new pi;case"ethiopic":return new Ho;case"ethioaa":return new Si;case"coptic":return new xi;case"hebrew":return new Oi;case"indian":return new mi;case"islamic-civil":return new No;case"islamic-tbla":return new gi;case"islamic-umalqura":return new wi;case"japanese":return new fi;case"persian":return new vi;case"roc":return new hi;case"gregory":default:return new we}}let eo=new Map;class Fe{format(e){return this.formatter.format(e)}formatToParts(e){return this.formatter.formatToParts(e)}formatRange(e,n){if(typeof this.formatter.formatRange=="function")return this.formatter.formatRange(e,n);if(n<e)throw new RangeError("End date must be >= start date");return`${this.formatter.format(e)} – ${this.formatter.format(n)}`}formatRangeToParts(e,n){if(typeof this.formatter.formatRangeToParts=="function")return this.formatter.formatRangeToParts(e,n);if(n<e)throw new RangeError("End date must be >= start date");let o=this.formatter.formatToParts(e),a=this.formatter.formatToParts(n);return[...o.map(r=>({...r,source:"startRange"})),{type:"literal",value:" – ",source:"shared"},...a.map(r=>({...r,source:"endRange"}))]}resolvedOptions(){let e=this.formatter.resolvedOptions();return ki()&&(this.resolvedHourCycle||(this.resolvedHourCycle=Bi(e.locale,this.options)),e.hourCycle=this.resolvedHourCycle,e.hour12=this.resolvedHourCycle==="h11"||this.resolvedHourCycle==="h12"),e.calendar==="ethiopic-amete-alem"&&(e.calendar="ethioaa"),e}constructor(e,n={}){this.formatter=Tr(e,n),this.options=n}}const _i={true:{ja:"h11"},false:{}};function Tr(t,e={}){if(typeof e.hour12=="boolean"&&Mi()){e={...e};let a=_i[String(e.hour12)][t.split("-")[0]],r=e.hour12?"h12":"h23";e.hourCycle=a??r,delete e.hour12}let n=t+(e?Object.entries(e).sort((a,r)=>a[0]<r[0]?-1:1).join():"");if(eo.has(n))return eo.get(n);let o=new Intl.DateTimeFormat(t,e);return eo.set(n,o),o}let to=null;function Mi(){return to==null&&(to=new Intl.DateTimeFormat("en-US",{hour:"numeric",hour12:!1}).format(new Date(2020,2,3,0))==="24"),to}let no=null;function ki(){return no==null&&(no=new Intl.DateTimeFormat("fr",{hour:"numeric",hour12:!1}).resolvedOptions().hourCycle==="h12"),no}function Bi(t,e){if(!e.timeStyle&&!e.hour)return;t=t.replace(/(-u-)?-nu-[a-zA-Z0-9]+/,""),t+=(t.includes("-u-")?"":"-u")+"-nu-latn";let n=Tr(t,{...e,timeZone:void 0}),o=parseInt(n.formatToParts(new Date(2020,2,3,0)).find(r=>r.type==="hour").value,10),a=parseInt(n.formatToParts(new Date(2020,2,3,23)).find(r=>r.type==="hour").value,10);if(o===0&&a===23)return"h23";if(o===24&&a===23)return"h24";if(o===0&&a===11)return"h11";if(o===12&&a===11)return"h12";throw new Error("Unexpected hour cycle result")}const Ti=["top","right","bottom","left"],Ge=Math.min,ue=Math.max,$n=Math.round,on=Math.floor,Oe=t=>({x:t,y:t}),Ii={left:"right",right:"left",bottom:"top",top:"bottom"},Ri={start:"end",end:"start"};function Co(t,e,n){return ue(t,Ge(e,n))}function He(t,e){return typeof t=="function"?t(e):t}function We(t){return t.split("-")[0]}function Pt(t){return t.split("-")[1]}function Wo(t){return t==="x"?"y":"x"}function Ko(t){return t==="y"?"height":"width"}function Ae(t){return["top","bottom"].includes(We(t))?"y":"x"}function Yo(t){return Wo(Ae(t))}function Fi(t,e,n){n===void 0&&(n=!1);const o=Pt(t),a=Yo(t),r=Ko(a);let s=a==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return e.reference[r]>e.floating[r]&&(s=Cn(s)),[s,Cn(s)]}function Li(t){const e=Cn(t);return[Do(t),e,Do(e)]}function Do(t){return t.replace(/start|end/g,e=>Ri[e])}function Vi(t,e,n){const o=["left","right"],a=["right","left"],r=["top","bottom"],s=["bottom","top"];switch(t){case"top":case"bottom":return n?e?a:o:e?o:a;case"left":case"right":return e?r:s;default:return[]}}function Ni(t,e,n,o){const a=Pt(t);let r=Vi(We(t),n==="start",o);return a&&(r=r.map(s=>s+"-"+a),e&&(r=r.concat(r.map(Do)))),r}function Cn(t){return t.replace(/left|right|bottom|top/g,e=>Ii[e])}function Ui(t){return{top:0,right:0,bottom:0,left:0,...t}}function Ir(t){return typeof t!="number"?Ui(t):{top:t,right:t,bottom:t,left:t}}function Dn(t){const{x:e,y:n,width:o,height:a}=t;return{width:o,height:a,top:n,left:e,right:e+o,bottom:n+a,x:e,y:n}}function Ma(t,e,n){let{reference:o,floating:a}=t;const r=Ae(e),s=Yo(e),i=Ko(s),l=We(e),c=r==="y",d=o.x+o.width/2-a.width/2,f=o.y+o.height/2-a.height/2,p=o[i]/2-a[i]/2;let h;switch(l){case"top":h={x:d,y:o.y-a.height};break;case"bottom":h={x:d,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:f};break;case"left":h={x:o.x-a.width,y:f};break;default:h={x:o.x,y:o.y}}switch(Pt(e)){case"start":h[s]-=p*(n&&c?-1:1);break;case"end":h[s]+=p*(n&&c?-1:1);break}return h}const Hi=async(t,e,n)=>{const{placement:o="bottom",strategy:a="absolute",middleware:r=[],platform:s}=n,i=r.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(e));let c=await s.getElementRects({reference:t,floating:e,strategy:a}),{x:d,y:f}=Ma(c,o,l),p=o,h={},y=0;for(let w=0;w<i.length;w++){const{name:m,fn:v}=i[w],{x:b,y:g,data:C,reset:_}=await v({x:d,y:f,initialPlacement:o,placement:p,strategy:a,middlewareData:h,rects:c,platform:s,elements:{reference:t,floating:e}});d=b??d,f=g??f,h={...h,[m]:{...h[m],...C}},_&&y<=50&&(y++,typeof _=="object"&&(_.placement&&(p=_.placement),_.rects&&(c=_.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:a}):_.rects),{x:d,y:f}=Ma(c,p,l)),w=-1)}return{x:d,y:f,placement:p,strategy:a,middlewareData:h}};async function Lt(t,e){var n;e===void 0&&(e={});const{x:o,y:a,platform:r,rects:s,elements:i,strategy:l}=t,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=He(e,t),y=Ir(h),m=i[p?f==="floating"?"reference":"floating":f],v=Dn(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(m)))==null||n?m:m.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(i.floating)),boundary:c,rootBoundary:d,strategy:l})),b=f==="floating"?{x:o,y:a,width:s.floating.width,height:s.floating.height}:s.reference,g=await(r.getOffsetParent==null?void 0:r.getOffsetParent(i.floating)),C=await(r.isElement==null?void 0:r.isElement(g))?await(r.getScale==null?void 0:r.getScale(g))||{x:1,y:1}:{x:1,y:1},_=Dn(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:b,offsetParent:g,strategy:l}):b);return{top:(v.top-_.top+y.top)/C.y,bottom:(_.bottom-v.bottom+y.bottom)/C.y,left:(v.left-_.left+y.left)/C.x,right:(_.right-v.right+y.right)/C.x}}const Wi=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:o,placement:a,rects:r,platform:s,elements:i,middlewareData:l}=e,{element:c,padding:d=0}=He(t,e)||{};if(c==null)return{};const f=Ir(d),p={x:n,y:o},h=Yo(a),y=Ko(h),w=await s.getDimensions(c),m=h==="y",v=m?"top":"left",b=m?"bottom":"right",g=m?"clientHeight":"clientWidth",C=r.reference[y]+r.reference[h]-p[h]-r.floating[y],_=p[h]-r.reference[h],M=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c));let B=M?M[g]:0;(!B||!await(s.isElement==null?void 0:s.isElement(M)))&&(B=i.floating[g]||r.floating[y]);const F=C/2-_/2,S=B/2-w[y]/2-1,T=Ge(f[v],S),k=Ge(f[b],S),V=T,N=B-w[y]-k,K=B/2-w[y]/2+F,U=Co(V,K,N),Z=!l.arrow&&Pt(a)!=null&&K!==U&&r.reference[y]/2-(K<V?T:k)-w[y]/2<0,j=Z?K<V?K-V:K-N:0;return{[h]:p[h]+j,data:{[h]:U,centerOffset:K-U-j,...Z&&{alignmentOffset:j}},reset:Z}}}),Ki=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:a,middlewareData:r,rects:s,initialPlacement:i,platform:l,elements:c}=e,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:w=!0,...m}=He(t,e);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const v=We(a),b=Ae(i),g=We(i)===i,C=await(l.isRTL==null?void 0:l.isRTL(c.floating)),_=p||(g||!w?[Cn(i)]:Li(i)),M=y!=="none";!p&&M&&_.push(...Ni(i,w,y,C));const B=[i,..._],F=await Lt(e,m),S=[];let T=((o=r.flip)==null?void 0:o.overflows)||[];if(d&&S.push(F[v]),f){const K=Fi(a,s,C);S.push(F[K[0]],F[K[1]])}if(T=[...T,{placement:a,overflows:S}],!S.every(K=>K<=0)){var k,V;const K=(((k=r.flip)==null?void 0:k.index)||0)+1,U=B[K];if(U&&(!(f==="alignment"?b!==Ae(U):!1)||T.every(ae=>ae.overflows[0]>0&&Ae(ae.placement)===b)))return{data:{index:K,overflows:T},reset:{placement:U}};let Z=(V=T.filter(j=>j.overflows[0]<=0).sort((j,ae)=>j.overflows[1]-ae.overflows[1])[0])==null?void 0:V.placement;if(!Z)switch(h){case"bestFit":{var N;const j=(N=T.filter(ae=>{if(M){const le=Ae(ae.placement);return le===b||le==="y"}return!0}).map(ae=>[ae.placement,ae.overflows.filter(le=>le>0).reduce((le,Be)=>le+Be,0)]).sort((ae,le)=>ae[1]-le[1])[0])==null?void 0:N[0];j&&(Z=j);break}case"initialPlacement":Z=i;break}if(a!==Z)return{reset:{placement:Z}}}return{}}}};function ka(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Ba(t){return Ti.some(e=>t[e]>=0)}const Yi=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:o="referenceHidden",...a}=He(t,e);switch(o){case"referenceHidden":{const r=await Lt(e,{...a,elementContext:"reference"}),s=ka(r,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Ba(s)}}}case"escaped":{const r=await Lt(e,{...a,altBoundary:!0}),s=ka(r,n.floating);return{data:{escapedOffsets:s,escaped:Ba(s)}}}default:return{}}}}};async function zi(t,e){const{placement:n,platform:o,elements:a}=t,r=await(o.isRTL==null?void 0:o.isRTL(a.floating)),s=We(n),i=Pt(n),l=Ae(n)==="y",c=["left","top"].includes(s)?-1:1,d=r&&l?-1:1,f=He(e,t);let{mainAxis:p,crossAxis:h,alignmentAxis:y}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return i&&typeof y=="number"&&(h=i==="end"?y*-1:y),l?{x:h*d,y:p*c}:{x:p*c,y:h*d}}const ji=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:a,y:r,placement:s,middlewareData:i}=e,l=await zi(e,t);return s===((n=i.offset)==null?void 0:n.placement)&&(o=i.arrow)!=null&&o.alignmentOffset?{}:{x:a+l.x,y:r+l.y,data:{...l,placement:s}}}}},qi=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:a}=e,{mainAxis:r=!0,crossAxis:s=!1,limiter:i={fn:m=>{let{x:v,y:b}=m;return{x:v,y:b}}},...l}=He(t,e),c={x:n,y:o},d=await Lt(e,l),f=Ae(We(a)),p=Wo(f);let h=c[p],y=c[f];if(r){const m=p==="y"?"top":"left",v=p==="y"?"bottom":"right",b=h+d[m],g=h-d[v];h=Co(b,h,g)}if(s){const m=f==="y"?"top":"left",v=f==="y"?"bottom":"right",b=y+d[m],g=y-d[v];y=Co(b,y,g)}const w=i.fn({...e,[p]:h,[f]:y});return{...w,data:{x:w.x-n,y:w.y-o,enabled:{[p]:r,[f]:s}}}}}},Ji=function(t){return t===void 0&&(t={}),{options:t,fn(e){const{x:n,y:o,placement:a,rects:r,middlewareData:s}=e,{offset:i=0,mainAxis:l=!0,crossAxis:c=!0}=He(t,e),d={x:n,y:o},f=Ae(a),p=Wo(f);let h=d[p],y=d[f];const w=He(i,e),m=typeof w=="number"?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(l){const g=p==="y"?"height":"width",C=r.reference[p]-r.floating[g]+m.mainAxis,_=r.reference[p]+r.reference[g]-m.mainAxis;h<C?h=C:h>_&&(h=_)}if(c){var v,b;const g=p==="y"?"width":"height",C=["top","left"].includes(We(a)),_=r.reference[f]-r.floating[g]+(C&&((v=s.offset)==null?void 0:v[f])||0)+(C?0:m.crossAxis),M=r.reference[f]+r.reference[g]+(C?0:((b=s.offset)==null?void 0:b[f])||0)-(C?m.crossAxis:0);y<_?y=_:y>M&&(y=M)}return{[p]:h,[f]:y}}}},Gi=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var n,o;const{placement:a,rects:r,platform:s,elements:i}=e,{apply:l=()=>{},...c}=He(t,e),d=await Lt(e,c),f=We(a),p=Pt(a),h=Ae(a)==="y",{width:y,height:w}=r.floating;let m,v;f==="top"||f==="bottom"?(m=f,v=p===(await(s.isRTL==null?void 0:s.isRTL(i.floating))?"start":"end")?"left":"right"):(v=f,m=p==="end"?"top":"bottom");const b=w-d.top-d.bottom,g=y-d.left-d.right,C=Ge(w-d[m],b),_=Ge(y-d[v],g),M=!e.middlewareData.shift;let B=C,F=_;if((n=e.middlewareData.shift)!=null&&n.enabled.x&&(F=g),(o=e.middlewareData.shift)!=null&&o.enabled.y&&(B=b),M&&!p){const T=ue(d.left,0),k=ue(d.right,0),V=ue(d.top,0),N=ue(d.bottom,0);h?F=y-2*(T!==0||k!==0?T+k:ue(d.left,d.right)):B=w-2*(V!==0||N!==0?V+N:ue(d.top,d.bottom))}await l({...e,availableWidth:F,availableHeight:B});const S=await s.getDimensions(i.floating);return y!==S.width||w!==S.height?{reset:{rects:!0}}:{}}}};function Mn(){return typeof window<"u"}function ut(t){return zo(t)?(t.nodeName||"").toLowerCase():"#document"}function de(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function Pe(t){var e;return(e=(zo(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function zo(t){return Mn()?t instanceof Node||t instanceof de(t).Node:!1}function $e(t){return Mn()?t instanceof Element||t instanceof de(t).Element:!1}function Se(t){return Mn()?t instanceof HTMLElement||t instanceof de(t).HTMLElement:!1}function Ta(t){return!Mn()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof de(t).ShadowRoot}function zt(t){const{overflow:e,overflowX:n,overflowY:o,display:a}=Ce(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(a)}function Xi(t){return["table","td","th"].includes(ut(t))}function kn(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function jo(t){const e=qo(),n=$e(t)?Ce(t):t;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function Qi(t){let e=Xe(t);for(;Se(e)&&!St(e);){if(jo(e))return e;if(kn(e))return null;e=Xe(e)}return null}function qo(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function St(t){return["html","body","#document"].includes(ut(t))}function Ce(t){return de(t).getComputedStyle(t)}function Bn(t){return $e(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function Xe(t){if(ut(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Ta(t)&&t.host||Pe(t);return Ta(e)?e.host:e}function Rr(t){const e=Xe(t);return St(e)?t.ownerDocument?t.ownerDocument.body:t.body:Se(e)&&zt(e)?e:Rr(e)}function Vt(t,e,n){var o;e===void 0&&(e=[]),n===void 0&&(n=!0);const a=Rr(t),r=a===((o=t.ownerDocument)==null?void 0:o.body),s=de(a);if(r){const i=Eo(s);return e.concat(s,s.visualViewport||[],zt(a)?a:[],i&&n?Vt(i):[])}return e.concat(a,Vt(a,[],n))}function Eo(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Fr(t){const e=Ce(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const a=Se(t),r=a?t.offsetWidth:n,s=a?t.offsetHeight:o,i=$n(n)!==r||$n(o)!==s;return i&&(n=r,o=s),{width:n,height:o,$:i}}function Jo(t){return $e(t)?t:t.contextElement}function $t(t){const e=Jo(t);if(!Se(e))return Oe(1);const n=e.getBoundingClientRect(),{width:o,height:a,$:r}=Fr(e);let s=(r?$n(n.width):n.width)/o,i=(r?$n(n.height):n.height)/a;return(!s||!Number.isFinite(s))&&(s=1),(!i||!Number.isFinite(i))&&(i=1),{x:s,y:i}}const Zi=Oe(0);function Lr(t){const e=de(t);return!qo()||!e.visualViewport?Zi:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function el(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==de(t)?!1:e}function rt(t,e,n,o){e===void 0&&(e=!1),n===void 0&&(n=!1);const a=t.getBoundingClientRect(),r=Jo(t);let s=Oe(1);e&&(o?$e(o)&&(s=$t(o)):s=$t(t));const i=el(r,n,o)?Lr(r):Oe(0);let l=(a.left+i.x)/s.x,c=(a.top+i.y)/s.y,d=a.width/s.x,f=a.height/s.y;if(r){const p=de(r),h=o&&$e(o)?de(o):o;let y=p,w=Eo(y);for(;w&&o&&h!==y;){const m=$t(w),v=w.getBoundingClientRect(),b=Ce(w),g=v.left+(w.clientLeft+parseFloat(b.paddingLeft))*m.x,C=v.top+(w.clientTop+parseFloat(b.paddingTop))*m.y;l*=m.x,c*=m.y,d*=m.x,f*=m.y,l+=g,c+=C,y=de(w),w=Eo(y)}}return Dn({width:d,height:f,x:l,y:c})}function Go(t,e){const n=Bn(t).scrollLeft;return e?e.left+n:rt(Pe(t)).left+n}function Vr(t,e,n){n===void 0&&(n=!1);const o=t.getBoundingClientRect(),a=o.left+e.scrollLeft-(n?0:Go(t,o)),r=o.top+e.scrollTop;return{x:a,y:r}}function tl(t){let{elements:e,rect:n,offsetParent:o,strategy:a}=t;const r=a==="fixed",s=Pe(o),i=e?kn(e.floating):!1;if(o===s||i&&r)return n;let l={scrollLeft:0,scrollTop:0},c=Oe(1);const d=Oe(0),f=Se(o);if((f||!f&&!r)&&((ut(o)!=="body"||zt(s))&&(l=Bn(o)),Se(o))){const h=rt(o);c=$t(o),d.x=h.x+o.clientLeft,d.y=h.y+o.clientTop}const p=s&&!f&&!r?Vr(s,l,!0):Oe(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-l.scrollTop*c.y+d.y+p.y}}function nl(t){return Array.from(t.getClientRects())}function ol(t){const e=Pe(t),n=Bn(t),o=t.ownerDocument.body,a=ue(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),r=ue(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let s=-n.scrollLeft+Go(t);const i=-n.scrollTop;return Ce(o).direction==="rtl"&&(s+=ue(e.clientWidth,o.clientWidth)-a),{width:a,height:r,x:s,y:i}}function al(t,e){const n=de(t),o=Pe(t),a=n.visualViewport;let r=o.clientWidth,s=o.clientHeight,i=0,l=0;if(a){r=a.width,s=a.height;const c=qo();(!c||c&&e==="fixed")&&(i=a.offsetLeft,l=a.offsetTop)}return{width:r,height:s,x:i,y:l}}function rl(t,e){const n=rt(t,!0,e==="fixed"),o=n.top+t.clientTop,a=n.left+t.clientLeft,r=Se(t)?$t(t):Oe(1),s=t.clientWidth*r.x,i=t.clientHeight*r.y,l=a*r.x,c=o*r.y;return{width:s,height:i,x:l,y:c}}function Ia(t,e,n){let o;if(e==="viewport")o=al(t,n);else if(e==="document")o=ol(Pe(t));else if($e(e))o=rl(e,n);else{const a=Lr(t);o={x:e.x-a.x,y:e.y-a.y,width:e.width,height:e.height}}return Dn(o)}function Nr(t,e){const n=Xe(t);return n===e||!$e(n)||St(n)?!1:Ce(n).position==="fixed"||Nr(n,e)}function sl(t,e){const n=e.get(t);if(n)return n;let o=Vt(t,[],!1).filter(i=>$e(i)&&ut(i)!=="body"),a=null;const r=Ce(t).position==="fixed";let s=r?Xe(t):t;for(;$e(s)&&!St(s);){const i=Ce(s),l=jo(s);!l&&i.position==="fixed"&&(a=null),(r?!l&&!a:!l&&i.position==="static"&&!!a&&["absolute","fixed"].includes(a.position)||zt(s)&&!l&&Nr(t,s))?o=o.filter(d=>d!==s):a=i,s=Xe(s)}return e.set(t,o),o}function il(t){let{element:e,boundary:n,rootBoundary:o,strategy:a}=t;const s=[...n==="clippingAncestors"?kn(e)?[]:sl(e,this._c):[].concat(n),o],i=s[0],l=s.reduce((c,d)=>{const f=Ia(e,d,a);return c.top=ue(f.top,c.top),c.right=Ge(f.right,c.right),c.bottom=Ge(f.bottom,c.bottom),c.left=ue(f.left,c.left),c},Ia(e,i,a));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function ll(t){const{width:e,height:n}=Fr(t);return{width:e,height:n}}function ul(t,e,n){const o=Se(e),a=Pe(e),r=n==="fixed",s=rt(t,!0,r,e);let i={scrollLeft:0,scrollTop:0};const l=Oe(0);function c(){l.x=Go(a)}if(o||!o&&!r)if((ut(e)!=="body"||zt(a))&&(i=Bn(e)),o){const h=rt(e,!0,r,e);l.x=h.x+e.clientLeft,l.y=h.y+e.clientTop}else a&&c();r&&!o&&a&&c();const d=a&&!o&&!r?Vr(a,i):Oe(0),f=s.left+i.scrollLeft-l.x-d.x,p=s.top+i.scrollTop-l.y-d.y;return{x:f,y:p,width:s.width,height:s.height}}function oo(t){return Ce(t).position==="static"}function Ra(t,e){if(!Se(t)||Ce(t).position==="fixed")return null;if(e)return e(t);let n=t.offsetParent;return Pe(t)===n&&(n=n.ownerDocument.body),n}function Ur(t,e){const n=de(t);if(kn(t))return n;if(!Se(t)){let a=Xe(t);for(;a&&!St(a);){if($e(a)&&!oo(a))return a;a=Xe(a)}return n}let o=Ra(t,e);for(;o&&Xi(o)&&oo(o);)o=Ra(o,e);return o&&St(o)&&oo(o)&&!jo(o)?n:o||Qi(t)||n}const cl=async function(t){const e=this.getOffsetParent||Ur,n=this.getDimensions,o=await n(t.floating);return{reference:ul(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function dl(t){return Ce(t).direction==="rtl"}const fl={convertOffsetParentRelativeRectToViewportRelativeRect:tl,getDocumentElement:Pe,getClippingRect:il,getOffsetParent:Ur,getElementRects:cl,getClientRects:nl,getDimensions:ll,getScale:$t,isElement:$e,isRTL:dl};function Hr(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function pl(t,e){let n=null,o;const a=Pe(t);function r(){var i;clearTimeout(o),(i=n)==null||i.disconnect(),n=null}function s(i,l){i===void 0&&(i=!1),l===void 0&&(l=1),r();const c=t.getBoundingClientRect(),{left:d,top:f,width:p,height:h}=c;if(i||e(),!p||!h)return;const y=on(f),w=on(a.clientWidth-(d+p)),m=on(a.clientHeight-(f+h)),v=on(d),g={rootMargin:-y+"px "+-w+"px "+-m+"px "+-v+"px",threshold:ue(0,Ge(1,l))||1};let C=!0;function _(M){const B=M[0].intersectionRatio;if(B!==l){if(!C)return s();B?s(!1,B):o=setTimeout(()=>{s(!1,1e-7)},1e3)}B===1&&!Hr(c,t.getBoundingClientRect())&&s(),C=!1}try{n=new IntersectionObserver(_,{...g,root:a.ownerDocument})}catch{n=new IntersectionObserver(_,g)}n.observe(t)}return s(!0),r}function hl(t,e,n,o){o===void 0&&(o={});const{ancestorScroll:a=!0,ancestorResize:r=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:i=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,c=Jo(t),d=a||r?[...c?Vt(c):[],...Vt(e)]:[];d.forEach(v=>{a&&v.addEventListener("scroll",n,{passive:!0}),r&&v.addEventListener("resize",n)});const f=c&&i?pl(c,n):null;let p=-1,h=null;s&&(h=new ResizeObserver(v=>{let[b]=v;b&&b.target===c&&h&&(h.unobserve(e),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var g;(g=h)==null||g.observe(e)})),n()}),c&&!l&&h.observe(c),h.observe(e));let y,w=l?rt(t):null;l&&m();function m(){const v=rt(t);w&&!Hr(w,v)&&n(),w=v,y=requestAnimationFrame(m)}return n(),()=>{var v;d.forEach(b=>{a&&b.removeEventListener("scroll",n),r&&b.removeEventListener("resize",n)}),f==null||f(),(v=h)==null||v.disconnect(),h=null,l&&cancelAnimationFrame(y)}}const vl=ji,ml=qi,Fa=Ki,yl=Gi,gl=Yi,bl=Wi,wl=Ji,$l=(t,e,n)=>{const o=new Map,a={platform:fl,...n},r={...a.platform,_c:o};return Hi(t,e,{...a,platform:r})};function Cl(t){return t!=null&&typeof t=="object"&&"$el"in t}function Ao(t){if(Cl(t)){const e=t.$el;return zo(e)&&ut(e)==="#comment"?null:e}return t}function yt(t){return typeof t=="function"?t():u(t)}function Dl(t){return{name:"arrow",options:t,fn(e){const n=Ao(yt(t.element));return n==null?{}:bl({element:n,padding:t.padding}).fn(e)}}}function Wr(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function La(t,e){const n=Wr(t);return Math.round(e*n)/n}function El(t,e,n){n===void 0&&(n={});const o=n.whileElementsMounted,a=P(()=>{var B;return(B=yt(n.open))!=null?B:!0}),r=P(()=>yt(n.middleware)),s=P(()=>{var B;return(B=yt(n.placement))!=null?B:"bottom"}),i=P(()=>{var B;return(B=yt(n.strategy))!=null?B:"absolute"}),l=P(()=>{var B;return(B=yt(n.transform))!=null?B:!0}),c=P(()=>Ao(t.value)),d=P(()=>Ao(e.value)),f=x(0),p=x(0),h=x(i.value),y=x(s.value),w=Sn({}),m=x(!1),v=P(()=>{const B={position:h.value,left:"0",top:"0"};if(!d.value)return B;const F=La(d.value,f.value),S=La(d.value,p.value);return l.value?{...B,transform:"translate("+F+"px, "+S+"px)",...Wr(d.value)>=1.5&&{willChange:"transform"}}:{position:h.value,left:F+"px",top:S+"px"}});let b;function g(){if(c.value==null||d.value==null)return;const B=a.value;$l(c.value,d.value,{middleware:r.value,placement:s.value,strategy:i.value}).then(F=>{f.value=F.x,p.value=F.y,h.value=F.strategy,y.value=F.placement,w.value=F.middlewareData,m.value=B!==!1})}function C(){typeof b=="function"&&(b(),b=void 0)}function _(){if(C(),o===void 0){g();return}if(c.value!=null&&d.value!=null){b=o(c.value,d.value,g);return}}function M(){a.value||(m.value=!1)}return z([r,s,i,a],g,{flush:"sync"}),z([c,d],_,{flush:"sync"}),z(a,M,{flush:"sync"}),Za()&&er(C),{x:ht(f),y:ht(p),strategy:ht(h),placement:ht(y),middlewareData:ht(w),isPositioned:ht(m),floatingStyles:v,update:g}}function Xo(t){return t?t.flatMap(e=>e.type===Ht?Xo(e.children):[e]):[]}const Oo=O({name:"PrimitiveSlot",inheritAttrs:!1,setup(t,{attrs:e,slots:n}){return()=>{var l;if(!n.default)return null;const o=Xo(n.default()),a=o.findIndex(c=>c.type!==tr);if(a===-1)return o;const r=o[a];(l=r.props)==null||delete l.ref;const s=r.props?R(e,r.props):e,i=xs({...r,props:{}},s);return o.length===1?i:(o[a]=i,o)}}}),Al=["area","img","input"],L=O({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(t,{attrs:e,slots:n}){const o=t.asChild?"template":t.as;return typeof o=="string"&&Al.includes(o)?()=>Ue(o,e):o!=="template"?()=>Ue(t.as,e,{default:n.default}):()=>Ue(Oo,e,{default:n.default})}}),Qo=O({__name:"VisuallyHidden",props:{feature:{default:"focusable"},asChild:{type:Boolean},as:{default:"span"}},setup(t){return(e,n)=>(D(),E(u(L),{as:e.as,"as-child":e.asChild,"aria-hidden":e.feature==="focusable"?"true":void 0,"data-hidden":e.feature==="fully-hidden"?"":void 0,tabindex:e.feature==="fully-hidden"?"-1":void 0,style:{position:"absolute",border:0,width:"1px",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:$(()=>[A(e.$slots,"default")]),_:3},8,["as","as-child","aria-hidden","data-hidden","tabindex"]))}});function Ol(t,e){var n;const o=Sn();return ne(()=>{o.value=t()},{...e,flush:(n=void 0)!=null?n:"sync"}),nr(o)}function _t(t){return Za()?(er(t),!0):!1}function Sl(){const t=new Set,e=r=>{t.delete(r)};return{on:r=>{t.add(r);const s=()=>e(r);return _t(s),{off:s}},off:e,trigger:(...r)=>Promise.all(Array.from(t).map(s=>s(...r))),clear:()=>{t.clear()}}}function xl(t){let e=!1,n;const o=or(!0);return(...a)=>(e||(n=o.run(()=>t(...a)),e=!0),n)}function Kr(t){let e=0,n,o;const a=()=>{e-=1,o&&e<=0&&(o.stop(),n=void 0,o=void 0)};return(...r)=>(e+=1,o||(o=or(!0),n=o.run(()=>t(...r))),_t(a),n)}function Pl(t){if(!cn(t))return hn(t);const e=new Proxy({},{get(n,o,a){return u(Reflect.get(t.value,o,a))},set(n,o,a){return cn(t.value[o])&&!cn(a)?t.value[o].value=a:t.value[o]=a,!0},deleteProperty(n,o){return Reflect.deleteProperty(t.value,o)},has(n,o){return Reflect.has(t.value,o)},ownKeys(){return Object.keys(t.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return hn(e)}function _l(t){return Pl(P(t))}function Ml(t,...e){const n=e.flat(),o=n[0];return _l(()=>Object.fromEntries(typeof o=="function"?Object.entries(ee(t)).filter(([a,r])=>!o(te(r),a)):Object.entries(ee(t)).filter(a=>!n.includes(a[0]))))}const _e=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const kl=t=>typeof t<"u",Bl=Object.prototype.toString,Tl=t=>Bl.call(t)==="[object Object]",Va=()=>{},Na=Il();function Il(){var t,e;return _e&&((t=window==null?void 0:window.navigator)==null?void 0:t.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((e=window==null?void 0:window.navigator)==null?void 0:e.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function Rl(t,e){function n(...o){return new Promise((a,r)=>{Promise.resolve(t(()=>e.apply(this,o),{fn:e,thisArg:this,args:o})).then(a).catch(r)})}return n}function Fl(t,e={}){let n,o,a=Va;const r=l=>{clearTimeout(l),a(),a=Va};let s;return l=>{const c=te(t),d=te(e.maxWait);return n&&r(n),c<=0||d!==void 0&&d<=0?(o&&(r(o),o=null),Promise.resolve(l())):new Promise((f,p)=>{a=e.rejectOnCancel?p:f,s=l,d&&!o&&(o=setTimeout(()=>{n&&r(n),o=null,f(s())},d)),n=setTimeout(()=>{o&&r(o),o=null,f(l())},c)})}}function Ll(t){return lt()}function ao(t){return Array.isArray(t)?t:[t]}function Yr(t,e=1e4){return Ps((n,o)=>{let a=te(t),r;const s=()=>setTimeout(()=>{a=te(t),o()},te(e));return _t(()=>{clearTimeout(r)}),{get(){return n(),a},set(i){a=i,o(),clearTimeout(r),r=s()}}})}function zr(t,e=200,n={}){return Rl(Fl(e,n),t)}const Vl=te;function Nl(t,e){Ll()&&To(t,e)}function jr(t,e,n={}){const{immediate:o=!0,immediateCallback:a=!1}=n,r=Sn(!1);let s=null;function i(){s&&(clearTimeout(s),s=null)}function l(){r.value=!1,i()}function c(...d){a&&t(),i(),r.value=!0,s=setTimeout(()=>{r.value=!1,s=null,t(...d)},te(e))}return o&&(r.value=!0,_e&&c()),_t(l),{isPending:nr(r),start:c,stop:l}}function Ul(t,e,n){return z(t,e,{...n,immediate:!0})}function Hl(t,e,n){const o=z(t,(...a)=>(G(()=>o()),e(...a)),n);return o}const Tn=_e?window:void 0;function xe(t){var e;const n=te(t);return(e=n==null?void 0:n.$el)!=null?e:n}function xt(...t){const e=[],n=()=>{e.forEach(i=>i()),e.length=0},o=(i,l,c,d)=>(i.addEventListener(l,c,d),()=>i.removeEventListener(l,c,d)),a=P(()=>{const i=ao(te(t[0])).filter(l=>l!=null);return i.every(l=>typeof l!="string")?i:void 0}),r=Ul(()=>{var i,l;return[(l=(i=a.value)==null?void 0:i.map(c=>xe(c)))!=null?l:[Tn].filter(c=>c!=null),ao(te(a.value?t[1]:t[0])),ao(u(a.value?t[2]:t[1])),te(a.value?t[3]:t[2])]},([i,l,c,d])=>{if(n(),!(i!=null&&i.length)||!(l!=null&&l.length)||!(c!=null&&c.length))return;const f=Tl(d)?{...d}:d;e.push(...i.flatMap(p=>l.flatMap(h=>c.map(y=>o(p,h,y,f)))))},{flush:"post"}),s=()=>{r(),n()};return _t(n),s}function qr(){const t=Sn(!1),e=lt();return e&&J(()=>{t.value=!0},e),t}function Wl(t){const e=qr();return P(()=>(e.value,!!t()))}function Kl(t){return typeof t=="function"?t:typeof t=="string"?e=>e.key===t:Array.isArray(t)?e=>t.includes(e.key):()=>!0}function Yl(...t){let e,n,o={};t.length===3?(e=t[0],n=t[1],o=t[2]):t.length===2?typeof t[1]=="object"?(e=!0,n=t[0],o=t[1]):(e=t[0],n=t[1]):(e=!0,n=t[0]);const{target:a=Tn,eventName:r="keydown",passive:s=!1,dedupe:i=!1}=o,l=Kl(e);return xt(a,r,d=>{d.repeat&&te(i)||l(d)&&n(d)},s)}function zl(t){return JSON.parse(JSON.stringify(t))}function st(t,e,n={}){const{window:o=Tn,...a}=n;let r;const s=Wl(()=>o&&"ResizeObserver"in o),i=()=>{r&&(r.disconnect(),r=void 0)},l=P(()=>{const f=te(t);return Array.isArray(f)?f.map(p=>xe(p)):[xe(f)]}),c=z(l,f=>{if(i(),s.value&&o){r=new ResizeObserver(e);for(const p of f)p&&r.observe(p,a)}},{immediate:!0,flush:"post"}),d=()=>{i(),c()};return _t(d),{isSupported:s,stop:d}}function fe(t,e,n,o={}){var a,r,s;const{clone:i=!1,passive:l=!1,eventName:c,deep:d=!1,defaultValue:f,shouldEmit:p}=o,h=lt(),y=n||(h==null?void 0:h.emit)||((a=h==null?void 0:h.$emit)==null?void 0:a.bind(h))||((s=(r=h==null?void 0:h.proxy)==null?void 0:r.$emit)==null?void 0:s.bind(h==null?void 0:h.proxy));let w=c;e||(e="modelValue"),w=w||`update:${e.toString()}`;const m=g=>i?typeof i=="function"?i(g):zl(g):g,v=()=>kl(t[e])?m(t[e]):f,b=g=>{p?p(g)&&y(w,g):y(w,g)};if(l){const g=v(),C=x(g);let _=!1;return z(()=>t[e],M=>{_||(_=!0,C.value=m(M),G(()=>_=!1))}),z(C,M=>{!_&&(M!==t[e]||d)&&b(M)},{deep:d}),C}else return P({get(){return v()},set(g){b(g)}})}function q(t,e){const n=typeof t=="string"&&!e?`${t}Context`:e,o=Symbol(n);return[s=>{const i=ar(o,s);if(i||i===null)return i;throw new Error(`Injection \`${o.toString()}\` not found. Component must be used within ${Array.isArray(t)?`one of the following components: ${t.join(", ")}`:`\`${t}\``}`)},s=>(rr(o,s),s)]}function Ua(t){return typeof t=="string"?`'${t}'`:new jl().serialize(t)}const jl=function(){var e;class t{constructor(){ca(this,e,new Map)}compare(o,a){const r=typeof o,s=typeof a;return r==="string"&&s==="string"?o.localeCompare(a):r==="number"&&s==="number"?o-a:String.prototype.localeCompare.call(this.serialize(o,!0),this.serialize(a,!0))}serialize(o,a){if(o===null)return"null";switch(typeof o){case"string":return a?o:`'${o}'`;case"bigint":return`${o}n`;case"object":return this.$object(o);case"function":return this.$function(o)}return String(o)}serializeObject(o){const a=Object.prototype.toString.call(o);if(a!=="[object Object]")return this.serializeBuiltInType(a.length<10?`unknown:${a}`:a.slice(8,-1),o);const r=o.constructor,s=r===Object||r===void 0?"":r.name;if(s!==""&&globalThis[s]===r)return this.serializeBuiltInType(s,o);if(typeof o.toJSON=="function"){const i=o.toJSON();return s+(i!==null&&typeof i=="object"?this.$object(i):`(${this.serialize(i)})`)}return this.serializeObjectEntries(s,Object.entries(o))}serializeBuiltInType(o,a){const r=this["$"+o];if(r)return r.call(this,a);if(typeof(a==null?void 0:a.entries)=="function")return this.serializeObjectEntries(o,a.entries());throw new Error(`Cannot serialize ${o}`)}serializeObjectEntries(o,a){const r=Array.from(a).sort((i,l)=>this.compare(i[0],l[0]));let s=`${o}{`;for(let i=0;i<r.length;i++){const[l,c]=r[i];s+=`${this.serialize(l,!0)}:${this.serialize(c)}`,i<r.length-1&&(s+=",")}return s+"}"}$object(o){let a=Tt(this,e).get(o);return a===void 0&&(Tt(this,e).set(o,`#${Tt(this,e).size}`),a=this.serializeObject(o),Tt(this,e).set(o,a)),a}$function(o){const a=Function.prototype.toString.call(o);return a.slice(-15)==="[native code] }"?`${o.name||""}()[native]`:`${o.name}(${o.length})${a.replace(/\s*\n\s*/g,"")}`}$Array(o){let a="[";for(let r=0;r<o.length;r++)a+=this.serialize(o[r]),r<o.length-1&&(a+=",");return a+"]"}$Date(o){try{return`Date(${o.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(o){return`ArrayBuffer[${new Uint8Array(o).join(",")}]`}$Set(o){return`Set${this.$Array(Array.from(o).sort((a,r)=>this.compare(a,r)))}`}$Map(o){return this.serializeObjectEntries("Map",o.entries())}}e=new WeakMap;for(const n of["Error","RegExp","URL"])t.prototype["$"+n]=function(o){return`${n}(${o})`};for(const n of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])t.prototype["$"+n]=function(o){return`${n}[${o.join(",")}]`};for(const n of["BigInt64Array","BigUint64Array"])t.prototype["$"+n]=function(o){return`${n}[${o.join("n,")}${o.length>0?"n":""}]`};return t}();function En(t,e){return t===e||Ua(t)===Ua(e)}function Nt(t){return t==null}function Ha(t,e){return Nt(t)?!1:Array.isArray(t)?t.some(n=>En(n,e)):En(t,e)}const[jt,_d]=q("ConfigProvider");function ct(t){const e=jt({dir:x("ltr")});return P(()=>{var n;return(t==null?void 0:t.value)||((n=e.dir)==null?void 0:n.value)||"ltr"})}function I(){const t=lt(),e=x(),n=P(()=>{var s,i;return["#text","#comment"].includes((s=e.value)==null?void 0:s.$el.nodeName)?(i=e.value)==null?void 0:i.$el.nextElementSibling:xe(e)}),o=Object.assign({},t.exposed),a={};for(const s in t.props)Object.defineProperty(a,s,{enumerable:!0,configurable:!0,get:()=>t.props[s]});if(Object.keys(o).length>0)for(const s in o)Object.defineProperty(a,s,{enumerable:!0,configurable:!0,get:()=>o[s]});Object.defineProperty(a,"$el",{enumerable:!0,configurable:!0,get:()=>t.vnode.el}),t.exposed=a;function r(s){e.value=s,s&&(Object.defineProperty(a,"$el",{enumerable:!0,configurable:!0,get:()=>s instanceof Element?s:s.$el}),t.exposed=a)}return{forwardRef:r,currentRef:e,currentElement:n}}const ql=["INPUT","TEXTAREA"];function Jl(t,e,n,o={}){if(!e||o.enableIgnoredElement&&ql.includes(e.nodeName))return null;const{arrowKeyOptions:a="both",attributeName:r="[data-reka-collection-item]",itemsArray:s=[],loop:i=!0,dir:l="ltr",preventScroll:c=!0,focus:d=!1}=o,[f,p,h,y,w,m]=[t.key==="ArrowRight",t.key==="ArrowLeft",t.key==="ArrowUp",t.key==="ArrowDown",t.key==="Home",t.key==="End"],v=h||y,b=f||p;if(!w&&!m&&(!v&&!b||a==="vertical"&&b||a==="horizontal"&&v))return null;const g=n?Array.from(n.querySelectorAll(r)):s;if(!g.length)return null;c&&t.preventDefault();let C=null;return b||v?C=Jr(g,e,{goForward:v?y:l==="ltr"?f:p,loop:i}):w?C=g.at(0)||null:m&&(C=g.at(-1)||null),d&&(C==null||C.focus()),C}function Jr(t,e,n,o=t.length){if(--o===0)return null;const a=t.indexOf(e),r=n.goForward?a+1:a-1;if(!n.loop&&(r<0||r>=t.length))return null;const s=(r+t.length)%t.length,i=t[s];return i?i.hasAttribute("disabled")&&i.getAttribute("disabled")!=="false"?Jr(t,i,n,o):i:null}let Gl=0;function ve(t,e="reka"){var o;const n=jt({useId:void 0});return Object.hasOwn(_s,"useId")?`${e}-${(o=Ms)==null?void 0:o()}`:n.useId?`${e}-${n.useId()}`:`${e}-${++Gl}`}function Gr(t,e){const n=x(t);function o(r){return e[n.value][r]??n.value}return{state:n,dispatch:r=>{n.value=o(r)}}}function Xl(t,e){var m;const n=x({}),o=x("none"),a=x(t),r=t.value?"mounted":"unmounted";let s;const i=((m=e.value)==null?void 0:m.ownerDocument.defaultView)??Tn,{state:l,dispatch:c}=Gr(r,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),d=v=>{var b;if(_e){const g=new CustomEvent(v,{bubbles:!1,cancelable:!1});(b=e.value)==null||b.dispatchEvent(g)}};z(t,async(v,b)=>{var C;const g=b!==v;if(await G(),g){const _=o.value,M=an(e.value);v?(c("MOUNT"),d("enter"),M==="none"&&d("after-enter")):M==="none"||M==="undefined"||((C=n.value)==null?void 0:C.display)==="none"?(c("UNMOUNT"),d("leave"),d("after-leave")):b&&_!==M?(c("ANIMATION_OUT"),d("leave")):(c("UNMOUNT"),d("after-leave"))}},{immediate:!0});const f=v=>{const b=an(e.value),g=b.includes(v.animationName),C=l.value==="mounted"?"enter":"leave";if(v.target===e.value&&g&&(d(`after-${C}`),c("ANIMATION_END"),!a.value)){const _=e.value.style.animationFillMode;e.value.style.animationFillMode="forwards",s=i==null?void 0:i.setTimeout(()=>{var M;((M=e.value)==null?void 0:M.style.animationFillMode)==="forwards"&&(e.value.style.animationFillMode=_)})}v.target===e.value&&b==="none"&&c("ANIMATION_END")},p=v=>{v.target===e.value&&(o.value=an(e.value))},h=z(e,(v,b)=>{v?(n.value=getComputedStyle(v),v.addEventListener("animationstart",p),v.addEventListener("animationcancel",f),v.addEventListener("animationend",f)):(c("ANIMATION_END"),s!==void 0&&(i==null||i.clearTimeout(s)),b==null||b.removeEventListener("animationstart",p),b==null||b.removeEventListener("animationcancel",f),b==null||b.removeEventListener("animationend",f))},{immediate:!0}),y=z(l,()=>{const v=an(e.value);o.value=l.value==="mounted"?v:"none"});return Qe(()=>{h(),y()}),{isPresent:P(()=>["mounted","unmountSuspended"].includes(l.value))}}function an(t){return t&&getComputedStyle(t).animationName||"none"}const Me=O({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(t,{slots:e,expose:n}){var c;const{present:o,forceMount:a}=ee(t),r=x(),{isPresent:s}=Xl(o,r);n({present:s});let i=e.default({present:s.value});i=Xo(i||[]);const l=lt();if(i&&(i==null?void 0:i.length)>1){const d=(c=l==null?void 0:l.parent)!=null&&c.type.name?`<${l.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${d}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(f=>`  - ${f}`).join(`
`)].join(`
`))}return()=>a.value||o.value||s.value?Ue(e.default({present:s.value})[0],{ref:d=>{const f=xe(d);return typeof(f==null?void 0:f.hasAttribute)>"u"||(f!=null&&f.hasAttribute("data-reka-popper-content-wrapper")?r.value=f.firstElementChild:r.value=f),f}}):null}}),[ke,Ql]=q("DialogRoot"),Md=O({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:!1},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(t,{emit:e}){const n=t,a=fe(n,"open",e,{defaultValue:n.defaultOpen,passive:n.open===void 0}),r=x(),s=x(),{modal:i}=ee(n);return Ql({open:a,modal:i,openModal:()=>{a.value=!0},onOpenChange:l=>{a.value=l},onOpenToggle:()=>{a.value=!a.value},contentId:"",titleId:"",descriptionId:"",triggerElement:r,contentElement:s}),(l,c)=>A(l.$slots,"default",{open:u(a),close:()=>a.value=!1})}}),kd=O({__name:"DialogClose",props:{asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t;I();const n=ke();return(o,a)=>(D(),E(u(L),R(e,{type:o.as==="button"?"button":void 0,onClick:a[0]||(a[0]=r=>u(n).onOpenChange(!1))}),{default:$(()=>[A(o.$slots,"default")]),_:3},16,["type"]))}});function qt(t){const e=lt(),n=e==null?void 0:e.type.emits,o={};return n!=null&&n.length||console.warn(`No emitted event found. Please check component: ${e==null?void 0:e.type.__name}`),n==null||n.forEach(a=>{o[ks(sr(a))]=(...r)=>t(a,...r)}),o}function re(){let t=document.activeElement;if(t==null)return null;for(;t!=null&&t.shadowRoot!=null&&t.shadowRoot.activeElement!=null;)t=t.shadowRoot.activeElement;return t}const Zl="menu.itemSelect",So=["Enter"," "],eu=["ArrowDown","PageUp","Home"],Xr=["ArrowUp","PageDown","End"],tu=[...eu,...Xr];[...So],[...So];function Qr(t){return t?"open":"closed"}function xo(t){const e=re();for(const n of t)if(n===e||(n.focus(),re()!==e))return}function nu(t,e){const{x:n,y:o}=t;let a=!1;for(let r=0,s=e.length-1;r<e.length;s=r++){const i=e[r].x,l=e[r].y,c=e[s].x,d=e[s].y;l>o!=d>o&&n<(c-i)*(o-l)/(d-l)+i&&(a=!a)}return a}function ou(t,e){if(!e)return!1;const n={x:t.clientX,y:t.clientY};return nu(n,e)}function Po(t){return t.pointerType==="mouse"}const au=xl(()=>x([]));function ru(){const t=au();return{add(e){const n=t.value[0];e!==n&&(n==null||n.pause()),t.value=Wa(t.value,e),t.value.unshift(e)},remove(e){var n;t.value=Wa(t.value,e),(n=t.value[0])==null||n.resume()}}}function Wa(t,e){const n=[...t],o=n.indexOf(e);return o!==-1&&n.splice(o,1),n}function su(t){return t.filter(e=>e.tagName!=="A")}const ro="focusScope.autoFocusOnMount",so="focusScope.autoFocusOnUnmount",Ka={bubbles:!1,cancelable:!0};function iu(t,{select:e=!1}={}){const n=re();for(const o of t)if(je(o,{select:e}),re()!==n)return!0}function lu(t){const e=Zr(t),n=Ya(e,t),o=Ya(e.reverse(),t);return[n,o]}function Zr(t){const e=[],n=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const a=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||a?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)e.push(n.currentNode);return e}function Ya(t,e){for(const n of t)if(!uu(n,{upTo:e}))return n}function uu(t,{upTo:e}){if(getComputedStyle(t).visibility==="hidden")return!0;for(;t;){if(e!==void 0&&t===e)return!1;if(getComputedStyle(t).display==="none")return!0;t=t.parentElement}return!1}function cu(t){return t instanceof HTMLInputElement&&"select"in t}function je(t,{select:e=!1}={}){if(t&&t.focus){const n=re();t.focus({preventScroll:!0}),t!==n&&cu(t)&&e&&t.select()}}const In=O({__name:"FocusScope",props:{loop:{type:Boolean,default:!1},trapped:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["mountAutoFocus","unmountAutoFocus"],setup(t,{emit:e}){const n=t,o=e,{currentRef:a,currentElement:r}=I(),s=x(null),i=ru(),l=hn({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});ne(d=>{if(!_e)return;const f=r.value;if(!n.trapped)return;function p(m){if(l.paused||!f)return;const v=m.target;f.contains(v)?s.value=v:je(s.value,{select:!0})}function h(m){if(l.paused||!f)return;const v=m.relatedTarget;v!==null&&(f.contains(v)||je(s.value,{select:!0}))}function y(m){f.contains(s.value)||je(f)}document.addEventListener("focusin",p),document.addEventListener("focusout",h);const w=new MutationObserver(y);f&&w.observe(f,{childList:!0,subtree:!0}),d(()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",h),w.disconnect()})}),ne(async d=>{const f=r.value;if(await G(),!f)return;i.add(l);const p=re();if(!f.contains(p)){const y=new CustomEvent(ro,Ka);f.addEventListener(ro,w=>o("mountAutoFocus",w)),f.dispatchEvent(y),y.defaultPrevented||(iu(su(Zr(f)),{select:!0}),re()===p&&je(f))}d(()=>{f.removeEventListener(ro,m=>o("mountAutoFocus",m));const y=new CustomEvent(so,Ka),w=m=>{o("unmountAutoFocus",m)};f.addEventListener(so,w),f.dispatchEvent(y),setTimeout(()=>{y.defaultPrevented||je(p??document.body,{select:!0}),f.removeEventListener(so,w),i.remove(l)},0)})});function c(d){if(!n.loop&&!n.trapped||l.paused)return;const f=d.key==="Tab"&&!d.altKey&&!d.ctrlKey&&!d.metaKey,p=re();if(f&&p){const h=d.currentTarget,[y,w]=lu(h);y&&w?!d.shiftKey&&p===w?(d.preventDefault(),n.loop&&je(y,{select:!0})):d.shiftKey&&p===y&&(d.preventDefault(),n.loop&&je(w,{select:!0})):p===h&&d.preventDefault()}}return(d,f)=>(D(),E(u(L),{ref_key:"currentRef",ref:a,tabindex:"-1","as-child":d.asChild,as:d.as,onKeydown:c},{default:$(()=>[A(d.$slots,"default")]),_:3},8,["as-child","as"]))}});function Zo(t,e,n){const o=n.originalEvent.target,a=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:n});e&&o.addEventListener(t,e,{once:!0}),o.dispatchEvent(a)}const du="dismissableLayer.pointerDownOutside",fu="dismissableLayer.focusOutside";function es(t,e){const n=e.closest("[data-dismissable-layer]"),o=t.dataset.dismissableLayer===""?t:t.querySelector("[data-dismissable-layer]"),a=Array.from(t.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(n&&(o===n||a.indexOf(o)<a.indexOf(n)))}function pu(t,e,n=!0){var s;const o=((s=e==null?void 0:e.value)==null?void 0:s.ownerDocument)??(globalThis==null?void 0:globalThis.document),a=x(!1),r=x(()=>{});return ne(i=>{if(!_e||!te(n))return;const l=async d=>{const f=d.target;if(!(!(e!=null&&e.value)||!f)){if(es(e.value,f)){a.value=!1;return}if(d.target&&!a.value){let p=function(){Zo(du,t,h)};const h={originalEvent:d};d.pointerType==="touch"?(o.removeEventListener("click",r.value),r.value=p,o.addEventListener("click",r.value,{once:!0})):p()}else o.removeEventListener("click",r.value);a.value=!1}},c=window.setTimeout(()=>{o.addEventListener("pointerdown",l)},0);i(()=>{window.clearTimeout(c),o.removeEventListener("pointerdown",l),o.removeEventListener("click",r.value)})}),{onPointerDownCapture:()=>{te(n)&&(a.value=!0)}}}function hu(t,e,n=!0){var r;const o=((r=e==null?void 0:e.value)==null?void 0:r.ownerDocument)??(globalThis==null?void 0:globalThis.document),a=x(!1);return ne(s=>{if(!_e||!te(n))return;const i=async l=>{if(!(e!=null&&e.value))return;await G(),await G();const c=l.target;!e.value||!c||es(e.value,c)||l.target&&!a.value&&Zo(fu,t,{originalEvent:l})};o.addEventListener("focusin",i),s(()=>o.removeEventListener("focusin",i))}),{onFocusCapture:()=>{te(n)&&(a.value=!0)},onBlurCapture:()=>{te(n)&&(a.value=!1)}}}const Re=hn({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Jt=O({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(t,{emit:e}){const n=t,o=e,{forwardRef:a,currentElement:r}=I(),s=P(()=>{var y;return((y=r.value)==null?void 0:y.ownerDocument)??globalThis.document}),i=P(()=>Re.layersRoot),l=P(()=>r.value?Array.from(i.value).indexOf(r.value):-1),c=P(()=>Re.layersWithOutsidePointerEventsDisabled.size>0),d=P(()=>{const y=Array.from(i.value),[w]=[...Re.layersWithOutsidePointerEventsDisabled].slice(-1),m=y.indexOf(w);return l.value>=m}),f=pu(async y=>{const w=[...Re.branches].some(m=>m==null?void 0:m.contains(y.target));!d.value||w||(o("pointerDownOutside",y),o("interactOutside",y),await G(),y.defaultPrevented||o("dismiss"))},r),p=hu(y=>{[...Re.branches].some(m=>m==null?void 0:m.contains(y.target))||(o("focusOutside",y),o("interactOutside",y),y.defaultPrevented||o("dismiss"))},r);Yl("Escape",y=>{l.value===i.value.size-1&&(o("escapeKeyDown",y),y.defaultPrevented||o("dismiss"))});let h;return ne(y=>{r.value&&(n.disableOutsidePointerEvents&&(Re.layersWithOutsidePointerEventsDisabled.size===0&&(h=s.value.body.style.pointerEvents,s.value.body.style.pointerEvents="none"),Re.layersWithOutsidePointerEventsDisabled.add(r.value)),i.value.add(r.value),y(()=>{n.disableOutsidePointerEvents&&Re.layersWithOutsidePointerEventsDisabled.size===1&&(s.value.body.style.pointerEvents=h)}))}),ne(y=>{y(()=>{r.value&&(i.value.delete(r.value),Re.layersWithOutsidePointerEventsDisabled.delete(r.value))})}),(y,w)=>(D(),E(u(L),{ref:u(a),"as-child":y.asChild,as:y.as,"data-dismissable-layer":"",style:Ze({pointerEvents:c.value?d.value?"auto":"none":void 0}),onFocusCapture:u(p).onFocusCapture,onBlurCapture:u(p).onBlurCapture,onPointerdownCapture:u(f).onPointerDownCapture},{default:$(()=>[A(y.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}}),ts=O({__name:"DialogContentImpl",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,o=e,a=ke(),{forwardRef:r,currentElement:s}=I();return a.titleId||(a.titleId=ve(void 0,"reka-dialog-title")),a.descriptionId||(a.descriptionId=ve(void 0,"reka-dialog-description")),J(()=>{a.contentElement=s,re()!==document.body&&(a.triggerElement.value=re())}),(i,l)=>(D(),E(u(In),{"as-child":"",loop:"",trapped:n.trapFocus,onMountAutoFocus:l[5]||(l[5]=c=>o("openAutoFocus",c)),onUnmountAutoFocus:l[6]||(l[6]=c=>o("closeAutoFocus",c))},{default:$(()=>[H(u(Jt),R({id:u(a).contentId,ref:u(r),as:i.as,"as-child":i.asChild,"disable-outside-pointer-events":i.disableOutsidePointerEvents,role:"dialog","aria-describedby":u(a).descriptionId,"aria-labelledby":u(a).titleId,"data-state":u(Qr)(u(a).open.value)},i.$attrs,{onDismiss:l[0]||(l[0]=c=>u(a).onOpenChange(!1)),onEscapeKeyDown:l[1]||(l[1]=c=>o("escapeKeyDown",c)),onFocusOutside:l[2]||(l[2]=c=>o("focusOutside",c)),onInteractOutside:l[3]||(l[3]=c=>o("interactOutside",c)),onPointerDownOutside:l[4]||(l[4]=c=>o("pointerDownOutside",c))}),{default:$(()=>[A(i.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}});var vu=function(t){if(typeof document>"u")return null;var e=Array.isArray(t)?t[0]:t;return e.ownerDocument.body},mt=new WeakMap,rn=new WeakMap,sn={},io=0,ns=function(t){return t&&(t.host||ns(t.parentNode))},mu=function(t,e){return e.map(function(n){if(t.contains(n))return n;var o=ns(n);return o&&t.contains(o)?o:(console.error("aria-hidden",n,"in not contained inside",t,". Doing nothing"),null)}).filter(function(n){return!!n})},yu=function(t,e,n,o){var a=mu(e,Array.isArray(t)?t:[t]);sn[n]||(sn[n]=new WeakMap);var r=sn[n],s=[],i=new Set,l=new Set(a),c=function(f){!f||i.has(f)||(i.add(f),c(f.parentNode))};a.forEach(c);var d=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(p){if(i.has(p))d(p);else try{var h=p.getAttribute(o),y=h!==null&&h!=="false",w=(mt.get(p)||0)+1,m=(r.get(p)||0)+1;mt.set(p,w),r.set(p,m),s.push(p),w===1&&y&&rn.set(p,!0),m===1&&p.setAttribute(n,"true"),y||p.setAttribute(o,"true")}catch(v){console.error("aria-hidden: cannot operate on ",p,v)}})};return d(e),i.clear(),io++,function(){s.forEach(function(f){var p=mt.get(f)-1,h=r.get(f)-1;mt.set(f,p),r.set(f,h),p||(rn.has(f)||f.removeAttribute(o),rn.delete(f)),h||f.removeAttribute(n)}),io--,io||(mt=new WeakMap,mt=new WeakMap,rn=new WeakMap,sn={})}},gu=function(t,e,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(t)?t:[t]),a=vu(t);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),yu(o,a,n,"aria-hidden")):function(){return null}};function Rn(t){let e;z(()=>xe(t),n=>{n?e=gu(n):e&&e()}),Qe(()=>{e&&e()})}const bu=O({__name:"DialogContentModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,o=e,a=ke(),r=qt(o),{forwardRef:s,currentElement:i}=I();return Rn(i),(l,c)=>(D(),E(ts,R({...n,...u(r)},{ref:u(s),"trap-focus":u(a).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:c[0]||(c[0]=d=>{var f;d.defaultPrevented||(d.preventDefault(),(f=u(a).triggerElement.value)==null||f.focus())}),onPointerDownOutside:c[1]||(c[1]=d=>{const f=d.detail.originalEvent,p=f.button===0&&f.ctrlKey===!0;(f.button===2||p)&&d.preventDefault()}),onFocusOutside:c[2]||(c[2]=d=>{d.preventDefault()})}),{default:$(()=>[A(l.$slots,"default")]),_:3},16,["trap-focus"]))}}),wu=O({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,a=qt(e);I();const r=ke(),s=x(!1),i=x(!1);return(l,c)=>(D(),E(ts,R({...n,...u(a)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:c[0]||(c[0]=d=>{var f;d.defaultPrevented||(s.value||(f=u(r).triggerElement.value)==null||f.focus(),d.preventDefault()),s.value=!1,i.value=!1}),onInteractOutside:c[1]||(c[1]=d=>{var h;d.defaultPrevented||(s.value=!0,d.detail.originalEvent.type==="pointerdown"&&(i.value=!0));const f=d.target;((h=u(r).triggerElement.value)==null?void 0:h.contains(f))&&d.preventDefault(),d.detail.originalEvent.type==="focusin"&&i.value&&d.preventDefault()})}),{default:$(()=>[A(l.$slots,"default")]),_:3},16))}}),Bd=O({__name:"DialogContent",props:{forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,o=e,a=ke(),r=qt(o),{forwardRef:s}=I();return(i,l)=>(D(),E(u(Me),{present:i.forceMount||u(a).open.value},{default:$(()=>[u(a).modal.value?(D(),E(bu,R({key:0,ref:u(s)},{...n,...u(r),...i.$attrs}),{default:$(()=>[A(i.$slots,"default")]),_:3},16)):(D(),E(wu,R({key:1,ref:u(s)},{...n,...u(r),...i.$attrs}),{default:$(()=>[A(i.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Td=O({__name:"DialogDescription",props:{asChild:{type:Boolean},as:{default:"p"}},setup(t){const e=t;I();const n=ke();return(o,a)=>(D(),E(u(L),R(e,{id:u(n).descriptionId}),{default:$(()=>[A(o.$slots,"default")]),_:3},16,["id"]))}});function lo(t){if(t===null||typeof t!="object")return!1;const e=Object.getPrototypeOf(t);return e!==null&&e!==Object.prototype&&Object.getPrototypeOf(e)!==null||Symbol.iterator in t?!1:Symbol.toStringTag in t?Object.prototype.toString.call(t)==="[object Module]":!0}function _o(t,e,n=".",o){if(!lo(e))return _o(t,{},n,o);const a=Object.assign({},e);for(const r in t){if(r==="__proto__"||r==="constructor")continue;const s=t[r];s!=null&&(o&&o(a,r,s,n)||(Array.isArray(s)&&Array.isArray(a[r])?a[r]=[...s,...a[r]]:lo(s)&&lo(a[r])?a[r]=_o(s,a[r],(n?`${n}.`:"")+r.toString(),o):a[r]=s))}return a}function $u(t){return(...e)=>e.reduce((n,o)=>_o(n,o,"",t),{})}const Cu=$u(),Du=Kr(()=>{const t=x(new Map),e=x(),n=P(()=>{for(const s of t.value.values())if(s)return!0;return!1}),o=jt({scrollBody:x(!0)});let a=null;const r=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.documentElement.style.removeProperty("--scrollbar-width"),document.body.style.overflow=e.value??"",Na&&(a==null||a()),e.value=void 0};return z(n,(s,i)=>{var f;if(!_e)return;if(!s){i&&r();return}e.value===void 0&&(e.value=document.body.style.overflow);const l=window.innerWidth-document.documentElement.clientWidth,c={padding:l,margin:0},d=(f=o.scrollBody)!=null&&f.value?typeof o.scrollBody.value=="object"?Cu({padding:o.scrollBody.value.padding===!0?l:o.scrollBody.value.padding,margin:o.scrollBody.value.margin===!0?l:o.scrollBody.value.margin},c):c:{padding:0,margin:0};l>0&&(document.body.style.paddingRight=typeof d.padding=="number"?`${d.padding}px`:String(d.padding),document.body.style.marginRight=typeof d.margin=="number"?`${d.margin}px`:String(d.margin),document.documentElement.style.setProperty("--scrollbar-width",`${l}px`),document.body.style.overflow="hidden"),Na&&(a=xt(document,"touchmove",p=>Eu(p),{passive:!1})),G(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),t});function Fn(t){const e=Math.random().toString(36).substring(2,7),n=Du();n.value.set(e,t??!1);const o=P({get:()=>n.value.get(e)??!1,set:a=>n.value.set(e,a)});return Nl(()=>{n.value.delete(e)}),o}function os(t){const e=window.getComputedStyle(t);if(e.overflowX==="scroll"||e.overflowY==="scroll"||e.overflowX==="auto"&&t.clientWidth<t.scrollWidth||e.overflowY==="auto"&&t.clientHeight<t.scrollHeight)return!0;{const n=t.parentNode;return!(n instanceof Element)||n.tagName==="BODY"?!1:os(n)}}function Eu(t){const e=t||window.event,n=e.target;return n instanceof Element&&os(n)?!1:e.touches.length>1?!0:(e.preventDefault&&e.cancelable&&e.preventDefault(),!1)}const Au=O({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean},as:{}},setup(t){const e=ke();return Fn(!0),I(),(n,o)=>(D(),E(u(L),{as:n.as,"as-child":n.asChild,"data-state":u(e).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:$(()=>[A(n.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Id=O({__name:"DialogOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(t){const e=ke(),{forwardRef:n}=I();return(o,a)=>{var r;return(r=u(e))!=null&&r.modal.value?(D(),E(u(Me),{key:0,present:o.forceMount||u(e).open.value},{default:$(()=>[H(Au,R(o.$attrs,{ref:u(n),as:o.as,"as-child":o.asChild}),{default:$(()=>[A(o.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):ie("",!0)}}}),Gt=O({__name:"Teleport",props:{to:{default:"body"},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const e=qr();return(n,o)=>u(e)||n.forceMount?(D(),E(ir,{key:0,to:n.to,disabled:n.disabled,defer:n.defer},[A(n.$slots,"default")],8,["to","disabled","defer"])):ie("",!0)}});function Xt(t){const e=lt(),n=Object.keys((e==null?void 0:e.type.props)??{}).reduce((a,r)=>{const s=(e==null?void 0:e.type.props[r]).default;return s!==void 0&&(a[r]=s),a},{}),o=Bs(t);return P(()=>{const a={},r=(e==null?void 0:e.vnode.props)??{};return Object.keys(r).forEach(s=>{a[sr(s)]=r[s]}),Object.keys({...n,...a}).reduce((s,i)=>(o.value[i]!==void 0&&(s[i]=o.value[i]),s),{})})}function Ye(t,e){const n=Xt(t),o=e?qt(e):{};return P(()=>({...n.value,...o}))}const Rd=O({__name:"DialogTitle",props:{asChild:{type:Boolean},as:{default:"h2"}},setup(t){const e=t,n=ke();return I(),(o,a)=>(D(),E(u(L),R(e,{id:u(n).titleId}),{default:$(()=>[A(o.$slots,"default")]),_:3},16,["id"]))}}),Fd=O({__name:"DialogTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t,n=ke(),{forwardRef:o,currentElement:a}=I();return n.contentId||(n.contentId=ve(void 0,"reka-dialog-content")),J(()=>{n.triggerElement.value=a.value}),(r,s)=>(D(),E(u(L),R(e,{ref:u(o),type:r.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":u(n).open.value||!1,"aria-controls":u(n).open.value?u(n).contentId:void 0,"data-state":u(n).open.value?"open":"closed",onClick:u(n).onOpenToggle}),{default:$(()=>[A(r.$slots,"default")]),_:3},16,["type","aria-expanded","aria-controls","data-state","onClick"]))}}),[Ou,Su]=q("AvatarRoot"),Ld=O({__name:"AvatarRoot",props:{asChild:{type:Boolean},as:{default:"span"}},setup(t){return I(),Su({imageLoadingStatus:x("idle")}),(e,n)=>(D(),E(u(L),{"as-child":e.asChild,as:e.as},{default:$(()=>[A(e.$slots,"default")]),_:3},8,["as-child","as"]))}}),Vd=O({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{default:"span"}},setup(t){const e=t,n=Ou();I();const o=x(e.delayMs===void 0);return ne(a=>{if(e.delayMs&&_e){const r=window.setTimeout(()=>{o.value=!0},e.delayMs);a(()=>{window.clearTimeout(r)})}}),(a,r)=>o.value&&u(n).imageLoadingStatus.value!=="loaded"?(D(),E(u(L),{key:0,"as-child":a.asChild,as:a.as},{default:$(()=>[A(a.$slots,"default")]),_:3},8,["as-child","as"])):ie("",!0)}});function xu(t){const{defaultValue:e,defaultPlaceholder:n,granularity:o="day",locale:a="en"}=t;if(Array.isArray(e)&&e.length)return e.at(-1).copy();if(e&&!Array.isArray(e))return e.copy();if(n)return n.copy();const r=new Date,s=r.getFullYear(),i=r.getMonth()+1,l=r.getDate(),c=["hour","minute","second"],d=new Fe(a),f=Pi(d.resolvedOptions().calendar);return c.includes(o??"day")?se(new At(s,i,l,0,0,0),f):se(new Q(s,i,l),f)}function Ee(t,e=Yt()){return ea(t)?t.toDate():t.toDate(e)}function Pu(t){return t instanceof At}function ea(t){return t instanceof Ot}function _u(t){return Pu(t)||ea(t)}function it(t){if(t instanceof Date){const e=t.getFullYear(),n=t.getMonth()+1;return new Date(e,n,0).getDate()}else return t.set({day:100}).day}function uo(t,e){return t.compare(e)<0}function co(t,e){return t.compare(e)>0}function Mu(t,e,n){const o=pr(t,n);return e>o?t.subtract({days:o+7-e}):e===o?t:t.subtract({days:o-e})}function ku(t,e,n){const o=pr(t,n),a=e===0?6:e-1;return o===a?t:o>a?t.add({days:7-o+a}):t.add({days:a-o})}function Bu(t,e){const n=[];for(let o=0;o<t.length;o+=e)n.push(t.slice(o,o+e));return n}function Tu(t){const e=t.querySelector("[data-selected]");if(e)return e.focus();const n=t.querySelector("[data-today]");if(n)return n.focus();const o=t.querySelector("[data-reka-calendar-day]");if(o)return o.focus()}function za(t,e){const n=[];let o=t.add({days:1});const a=e;for(;o.compare(a)<0;)n.push(o),o=o.add({days:1});return n}function fo(t){const{dateObj:e,weekStartsOn:n,fixedWeeks:o,locale:a}=t,r=it(e),s=Array.from({length:r},(m,v)=>e.set({day:v+1})),i=vo(e),l=pa(e),c=Mu(i,n,a),d=ku(l,n,a),f=za(c.subtract({days:1}),i),p=za(l,d.add({days:1})),h=f.length+s.length+p.length;if(o&&h<42){const m=42-h;let v=p[p.length-1];v||(v=pa(e));const b=Array.from({length:m},(g,C)=>{const _=C+1;return v.add({days:_})});p.push(...b)}const y=f.concat(s,p),w=Bu(y,7);return{value:e,cells:y,rows:w}}function et(t){const{numberOfMonths:e,dateObj:n,...o}=t,a=[];if(!e||e===1)return a.push(fo({...o,dateObj:n})),a;a.push(fo({...o,dateObj:n}));for(let r=1;r<e;r++){const s=n.add({months:r});a.push(fo({...o,dateObj:s}))}return a}function Iu(t,e={}){const n=x(t);function o(){return n.value}function a(m){n.value=m}function r(m,v){return new Fe(n.value,{...e,...v}).format(m)}function s(m,v=!0){return _u(m)&&v?r(Ee(m),{dateStyle:"long",timeStyle:"long"}):r(Ee(m),{dateStyle:"long"})}function i(m,v={}){return new Fe(n.value,{...e,month:"long",year:"numeric",...v}).format(m)}function l(m,v={}){return new Fe(n.value,{...e,month:"long",...v}).format(m)}function c(){const m=hr(Yt());return[1,2,3,4,5,6,7,8,9,10,11,12].map(b=>({label:l(Ee(m.set({month:b}))),value:b}))}function d(m,v={}){return new Fe(n.value,{...e,year:"numeric",...v}).format(m)}function f(m,v){return ea(m)?new Fe(n.value,{...e,...v,timeZone:m.timeZone}).formatToParts(Ee(m)):new Fe(n.value,{...e,...v}).formatToParts(Ee(m))}function p(m,v="narrow"){return new Fe(n.value,{...e,weekday:v}).format(m)}function h(m){var g;return((g=new Fe(n.value,{...e,hour:"numeric",minute:"numeric"}).formatToParts(m).find(C=>C.type==="dayPeriod"))==null?void 0:g.value)==="PM"?"PM":"AM"}const y={year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"};function w(m,v,b={}){const g={...y,...b},_=f(m,g).find(M=>M.type===v);return _?_.value:""}return{setLocale:a,getLocale:o,fullMonth:l,fullYear:d,fullMonthAndYear:i,toParts:f,custom:r,part:w,dayPeriod:h,selectedDate:s,dayOfWeek:p,getMonths:c}}function Ru(t){function e(o){return Array.isArray(t.date.value)?t.date.value.some(a=>ot(a,o)):t.date.value?ot(t.date.value,o):!1}const n=P(()=>{var o,a,r,s;if(Array.isArray(t.date.value)){if(!t.date.value.length)return!1;for(const i of t.date.value)if((o=t.isDateDisabled)!=null&&o.call(t,i)||(a=t.isDateUnavailable)!=null&&a.call(t,i))return!0}else{if(!t.date.value)return!1;if((r=t.isDateDisabled)!=null&&r.call(t,t.date.value)||(s=t.isDateUnavailable)!=null&&s.call(t,t.date.value))return!0}return!1});return{isDateSelected:e,isInvalid:n}}function Fu(t,e){const n=e(t),o=n.compare(t),a={};return o>=7&&(a.day=1),o>=it(t)&&(a.month=1),n.set({...a})}function Lu(t,e){const n=e(t),o=t.compare(n),a={};return o>=7&&(a.day=35),o>=it(t)&&(a.month=13),n.set({...a})}function Vu(t,e){return e(t)}function Nu(t,e){return e(t)}function Uu(t){const e=Iu(t.locale.value),n=P(()=>{const w={calendar:t.placeholder.value.calendar.identifier};return t.placeholder.value.calendar.identifier==="gregory"&&t.placeholder.value.era==="BC"&&(w.era="short"),w}),o=x(et({dateObj:t.placeholder.value,weekStartsOn:t.weekStartsOn.value,locale:t.locale.value,fixedWeeks:t.fixedWeeks.value,numberOfMonths:t.numberOfMonths.value})),a=P(()=>o.value.map(w=>w.value));function r(w){return!a.value.some(m=>da(w,m))}const s=w=>{if(!t.maxValue.value||!o.value.length)return!1;if(t.disabled.value)return!0;const m=o.value[o.value.length-1].value;if(!w&&!t.nextPage.value){const b=m.add({months:1}).set({day:1});return co(b,t.maxValue.value)}const v=Fu(m,w||t.nextPage.value);return co(v,t.maxValue.value)},i=w=>{if(!t.minValue.value||!o.value.length)return!1;if(t.disabled.value)return!0;const m=o.value[0].value;if(!w&&!t.prevPage.value){const b=m.subtract({months:1}).set({day:35});return uo(b,t.minValue.value)}const v=Lu(m,w||t.prevPage.value);return uo(v,t.minValue.value)};function l(w){var m;return!!((m=t.isDateDisabled)!=null&&m.call(t,w)||t.disabled.value||t.maxValue.value&&co(w,t.maxValue.value)||t.minValue.value&&uo(w,t.minValue.value))}const c=w=>{var m;return!!((m=t.isDateUnavailable)!=null&&m.call(t,w))},d=P(()=>o.value.length?o.value[0].rows[0].map(w=>e.dayOfWeek(Ee(w),t.weekdayFormat.value)):[]),f=w=>{const m=o.value[0].value;if(!w&&!t.nextPage.value){const C=m.add({months:t.pagedNavigation.value?t.numberOfMonths.value:1}),_=et({dateObj:C,weekStartsOn:t.weekStartsOn.value,locale:t.locale.value,fixedWeeks:t.fixedWeeks.value,numberOfMonths:t.numberOfMonths.value});o.value=_,t.placeholder.value=_[0].value.set({day:1});return}const v=Vu(m,w||t.nextPage.value),b=et({dateObj:v,weekStartsOn:t.weekStartsOn.value,locale:t.locale.value,fixedWeeks:t.fixedWeeks.value,numberOfMonths:t.numberOfMonths.value});o.value=b;const g={};if(!w){const C=b[0].value.compare(m);C>=it(m)&&(g.day=1),C>=365&&(g.month=1)}t.placeholder.value=b[0].value.set({...g})},p=w=>{const m=o.value[0].value;if(!w&&!t.prevPage.value){const C=m.subtract({months:t.pagedNavigation.value?t.numberOfMonths.value:1}),_=et({dateObj:C,weekStartsOn:t.weekStartsOn.value,locale:t.locale.value,fixedWeeks:t.fixedWeeks.value,numberOfMonths:t.numberOfMonths.value});o.value=_,t.placeholder.value=_[0].value.set({day:1});return}const v=Nu(m,w||t.prevPage.value),b=et({dateObj:v,weekStartsOn:t.weekStartsOn.value,locale:t.locale.value,fixedWeeks:t.fixedWeeks.value,numberOfMonths:t.numberOfMonths.value});o.value=b;const g={};if(!w){const C=m.compare(b[0].value);C>=it(m)&&(g.day=1),C>=365&&(g.month=1)}t.placeholder.value=b[0].value.set({...g})};z(t.placeholder,w=>{a.value.some(m=>da(m,w))||(o.value=et({dateObj:w,weekStartsOn:t.weekStartsOn.value,locale:t.locale.value,fixedWeeks:t.fixedWeeks.value,numberOfMonths:t.numberOfMonths.value}))}),z([t.locale,t.weekStartsOn,t.fixedWeeks,t.numberOfMonths],()=>{o.value=et({dateObj:t.placeholder.value,weekStartsOn:t.weekStartsOn.value,locale:t.locale.value,fixedWeeks:t.fixedWeeks.value,numberOfMonths:t.numberOfMonths.value})});const h=P(()=>{if(!o.value.length)return"";if(t.locale.value!==e.getLocale()&&e.setLocale(t.locale.value),o.value.length===1){const M=o.value[0].value;return`${e.fullMonthAndYear(Ee(M),n.value)}`}const w=Ee(o.value[0].value),m=Ee(o.value[o.value.length-1].value),v=e.fullMonth(w,n.value),b=e.fullMonth(m,n.value),g=e.fullYear(w,n.value),C=e.fullYear(m,n.value);return g===C?`${v} - ${b} ${C}`:`${v} ${g} - ${b} ${C}`}),y=P(()=>`${t.calendarLabel.value??"Event Date"}, ${h.value}`);return{isDateDisabled:l,isDateUnavailable:c,isNextButtonDisabled:s,isPrevButtonDisabled:i,grid:o,weekdays:d,visibleView:a,isOutsideVisibleView:r,formatter:e,nextPage:f,prevPage:p,headingValue:h,fullCalendarLabel:y}}function Ut(){const t=x(),e=P(()=>{var n,o;return["#text","#comment"].includes((n=t.value)==null?void 0:n.$el.nodeName)?(o=t.value)==null?void 0:o.$el.nextElementSibling:xe(t)});return{primitiveElement:t,currentElement:e}}function Hu(t){const e=jt({locale:x("en")});return P(()=>{var n;return(t==null?void 0:t.value)||((n=e.locale)==null?void 0:n.value)||"en"})}const Wu={style:{border:"0px",clip:"rect(0px, 0px, 0px, 0px)","clip-path":"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0px",position:"absolute","white-space":"nowrap",width:"1px"}},Ku={role:"heading","aria-level":"2"},[Mt,Yu]=q("CalendarRoot"),Nd=O({__name:"CalendarRoot",props:{defaultValue:{default:void 0},defaultPlaceholder:{},placeholder:{default:void 0},pagedNavigation:{type:Boolean,default:!1},preventDeselect:{type:Boolean,default:!1},weekStartsOn:{default:0},weekdayFormat:{default:"narrow"},calendarLabel:{},fixedWeeks:{type:Boolean,default:!1},maxValue:{},minValue:{},locale:{},numberOfMonths:{default:1},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},initialFocus:{type:Boolean,default:!1},isDateDisabled:{type:Function,default:void 0},isDateUnavailable:{type:Function,default:void 0},dir:{},nextPage:{},prevPage:{},modelValue:{},multiple:{type:Boolean,default:!1},disableDaysOutsideCurrentView:{type:Boolean,default:!1},asChild:{type:Boolean},as:{default:"div"}},emits:["update:modelValue","update:placeholder"],setup(t,{emit:e}){const n=t,o=e,{disabled:a,readonly:r,initialFocus:s,pagedNavigation:i,weekStartsOn:l,weekdayFormat:c,fixedWeeks:d,multiple:f,minValue:p,maxValue:h,numberOfMonths:y,preventDeselect:w,isDateDisabled:m,isDateUnavailable:v,calendarLabel:b,defaultValue:g,nextPage:C,prevPage:_,dir:M,locale:B,disableDaysOutsideCurrentView:F}=ee(n),{primitiveElement:S,currentElement:T}=Ut(),k=Hu(B),V=ct(M),N=fe(n,"modelValue",o,{defaultValue:g.value,passive:n.modelValue===void 0}),K=xu({defaultPlaceholder:n.placeholder,defaultValue:N.value,locale:n.locale}),U=fe(n,"placeholder",o,{defaultValue:n.defaultPlaceholder??K.copy(),passive:n.placeholder===void 0});function Z(Y){U.value=Y.copy()}const{fullCalendarLabel:j,headingValue:ae,isDateDisabled:le,isDateUnavailable:Be,isNextButtonDisabled:zn,isPrevButtonDisabled:kt,weekdays:Zt,isOutsideVisibleView:pt,nextPage:jn,prevPage:la,formatter:ye,grid:ge}=Uu({locale:k,placeholder:U,weekStartsOn:l,fixedWeeks:d,numberOfMonths:y,minValue:p,maxValue:h,disabled:a,weekdayFormat:c,pagedNavigation:i,isDateDisabled:m.value,isDateUnavailable:v.value,calendarLabel:b,nextPage:C,prevPage:_}),{isInvalid:De,isDateSelected:Te}=Ru({date:N,isDateDisabled:le,isDateUnavailable:Be});z(N,Y=>{if(Array.isArray(Y)&&Y.length){const Ie=Y[Y.length-1];Ie&&!Jn(U.value,Ie)&&Z(Ie)}else!Array.isArray(Y)&&Y&&!Jn(U.value,Y)&&Z(Y)});function Bt(Y){if(f.value){if(!N.value)N.value=[Y.copy()];else if(Array.isArray(N.value)){if(N.value.findIndex(en=>ot(en,Y))===-1)N.value=[...N.value,Y];else if(!w.value){const en=N.value.filter(qn=>!ot(qn,Y));if(!en.length){U.value=Y.copy(),N.value=void 0;return}N.value=en.map(qn=>qn.copy())}}}else{if(!N.value){N.value=Y.copy();return}!w.value&&Jn(N.value,Y)?(U.value=Y.copy(),N.value=void 0):N.value=Y.copy()}}return J(()=>{s.value&&Tu(T.value)}),Yu({isDateUnavailable:Be,dir:V,isDateDisabled:le,locale:k,formatter:ye,modelValue:N,placeholder:U,disabled:a,initialFocus:s,pagedNavigation:i,grid:ge,weekDays:Zt,weekStartsOn:l,weekdayFormat:c,fixedWeeks:d,multiple:f,numberOfMonths:y,readonly:r,preventDeselect:w,fullCalendarLabel:j,headingValue:ae,isInvalid:De,isDateSelected:Te,isNextButtonDisabled:zn,isPrevButtonDisabled:kt,isOutsideVisibleView:pt,nextPage:jn,prevPage:la,parentElement:T,onPlaceholderChange:Z,onDateChange:Bt,disableDaysOutsideCurrentView:F}),(Y,Ie)=>(D(),E(u(L),{ref_key:"primitiveElement",ref:S,as:Y.as,"as-child":Y.asChild,role:"application","aria-label":u(j),"data-readonly":u(r)?"":void 0,"data-disabled":u(a)?"":void 0,"data-invalid":u(De)?"":void 0,dir:u(V)},{default:$(()=>[A(Y.$slots,"default",{date:u(U),grid:u(ge),weekDays:u(Zt),weekStartsOn:u(l),locale:u(k),fixedWeeks:u(d),modelValue:u(N)}),vn("div",Wu,[vn("div",Ku,Wt(u(j)),1)])]),_:3},8,["as","as-child","aria-label","data-readonly","data-disabled","data-invalid","dir"]))}}),Ud=O({__name:"CalendarCell",props:{date:{},asChild:{type:Boolean},as:{default:"td"}},setup(t){const e=Mt();return(n,o)=>{var a,r;return D(),E(u(L),{as:n.as,"as-child":n.asChild,role:"gridcell","aria-selected":u(e).isDateSelected(n.date)?!0:void 0,"aria-disabled":u(e).isDateDisabled(n.date)||((r=(a=u(e)).isDateUnavailable)==null?void 0:r.call(a,n.date))||u(e).disableDaysOutsideCurrentView.value,"data-disabled":u(e).isDateDisabled(n.date)||u(e).disableDaysOutsideCurrentView.value?"":void 0},{default:$(()=>[A(n.$slots,"default")]),_:3},8,["as","as-child","aria-selected","aria-disabled","data-disabled"])}}}),zu="[data-reka-calendar-cell-trigger]:not([data-outside-view]):not([data-outside-visible-view])";function po(t){return Array.from(t.querySelectorAll(zu))??[]}function ju(){return{ALT:"Alt",ARROW_DOWN:"ArrowDown",ARROW_LEFT:"ArrowLeft",ARROW_RIGHT:"ArrowRight",ARROW_UP:"ArrowUp",BACKSPACE:"Backspace",CAPS_LOCK:"CapsLock",CONTROL:"Control",DELETE:"Delete",END:"End",ENTER:"Enter",ESCAPE:"Escape",F1:"F1",F10:"F10",F11:"F11",F12:"F12",F2:"F2",F3:"F3",F4:"F4",F5:"F5",F6:"F6",F7:"F7",F8:"F8",F9:"F9",HOME:"Home",META:"Meta",PAGE_DOWN:"PageDown",PAGE_UP:"PageUp",SHIFT:"Shift",SPACE:" ",TAB:"Tab",CTRL:"Control",ASTERISK:"*",SPACE_CODE:"Space"}}const Hd=O({__name:"CalendarCellTrigger",props:{day:{},month:{},asChild:{type:Boolean},as:{default:"div"}},setup(t){const e=t,n=ju(),o=Mt(),{primitiveElement:a,currentElement:r}=Ut(),s=P(()=>e.day.day.toLocaleString(o.locale.value)),i=P(()=>o.formatter.custom(Ee(e.day),{weekday:"long",month:"long",day:"numeric",year:"numeric"})),l=P(()=>{var b;return((b=o.isDateUnavailable)==null?void 0:b.call(o,e.day))??!1}),c=P(()=>Vs(e.day,Yt())),d=P(()=>!fr(e.day,e.month)),f=P(()=>o.isOutsideVisibleView(e.day)),p=P(()=>o.isDateDisabled(e.day)||o.disableDaysOutsideCurrentView.value&&d.value),h=P(()=>!o.disabled.value&&ot(e.day,o.placeholder.value)),y=P(()=>o.isDateSelected(e.day));function w(b){var g;o.readonly.value||o.isDateDisabled(b)||(g=o.isDateUnavailable)!=null&&g.call(o,b)||o.onDateChange(b)}function m(){p.value||w(e.day)}function v(b){if(p.value)return;b.preventDefault(),b.stopPropagation();const g=o.parentElement.value,C=7,_=o.dir.value==="rtl"?-1:1;switch(b.code){case n.ARROW_RIGHT:M(r.value,_);break;case n.ARROW_LEFT:M(r.value,-_);break;case n.ARROW_UP:M(r.value,-7);break;case n.ARROW_DOWN:M(r.value,C);break;case n.ENTER:case n.SPACE_CODE:w(e.day)}function M(B,F){const S=po(g);if(!S.length)return;const k=S.indexOf(B)+F;if(k>=0&&k<S.length){S[k].hasAttribute("data-disabled")&&M(S[k],F),S[k].focus();return}if(k<0){if(o.isPrevButtonDisabled())return;o.prevPage(),G(()=>{const V=po(g);if(!V.length)return;if(!o.pagedNavigation.value&&o.numberOfMonths.value>1){const U=it(o.placeholder.value)-Math.abs(k);V[U].hasAttribute("data-disabled")&&M(V[U],F),V[U].focus();return}const N=V.length-Math.abs(k);V[N].hasAttribute("data-disabled")&&M(V[N],F),V[N].focus()});return}if(k>=S.length){if(o.isNextButtonDisabled())return;o.nextPage(),G(()=>{const V=po(g);if(!V.length)return;if(!o.pagedNavigation.value&&o.numberOfMonths.value>1){const K=it(o.placeholder.value.add({months:o.numberOfMonths.value-1})),U=k-S.length+(V.length-K);V[U].hasAttribute("data-disabled")&&M(V[U],F),V[U].focus();return}const N=k-S.length;V[N].hasAttribute("data-disabled")&&M(V[N],F),V[N].focus()})}}}return(b,g)=>(D(),E(u(L),R({ref_key:"primitiveElement",ref:a},e,{role:"button","aria-label":i.value,"data-reka-calendar-cell-trigger":"","aria-disabled":p.value||l.value?!0:void 0,"data-selected":y.value?!0:void 0,"data-value":b.day.toString(),"data-disabled":p.value?"":void 0,"data-unavailable":l.value?"":void 0,"data-today":c.value?"":void 0,"data-outside-view":d.value?"":void 0,"data-outside-visible-view":f.value?"":void 0,"data-focused":h.value?"":void 0,tabindex:h.value?0:d.value||p.value?void 0:-1,onClick:m,onKeydown:[Ct(v,["up","down","left","right","space","enter"]),g[0]||(g[0]=Ct(ce(()=>{},["prevent"]),["enter"]))]}),{default:$(()=>[A(b.$slots,"default",{dayValue:s.value,disabled:p.value,today:c.value,selected:y.value,outsideView:d.value,outsideVisibleView:f.value,unavailable:l.value},()=>[Ke(Wt(s.value),1)])]),_:3},16,["aria-label","aria-disabled","data-selected","data-value","data-disabled","data-unavailable","data-today","data-outside-view","data-outside-visible-view","data-focused","tabindex"]))}}),Wd=O({__name:"CalendarGrid",props:{asChild:{type:Boolean},as:{default:"table"}},setup(t){const e=t,n=Mt(),o=P(()=>n.disabled.value?!0:void 0),a=P(()=>n.readonly.value?!0:void 0);return(r,s)=>(D(),E(u(L),R(e,{tabindex:"-1",role:"grid","aria-readonly":a.value,"aria-disabled":o.value,"data-readonly":a.value&&"","data-disabled":o.value&&""}),{default:$(()=>[A(r.$slots,"default")]),_:3},16,["aria-readonly","aria-disabled","data-readonly","data-disabled"]))}}),Kd=O({__name:"CalendarGridBody",props:{asChild:{type:Boolean},as:{default:"tbody"}},setup(t){const e=t;return(n,o)=>(D(),E(u(L),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),Yd=O({__name:"CalendarGridHead",props:{asChild:{type:Boolean},as:{default:"thead"}},setup(t){const e=t;return(n,o)=>(D(),E(u(L),R(e,{"aria-hidden":"true"}),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),zd=O({__name:"CalendarGridRow",props:{asChild:{type:Boolean},as:{default:"tr"}},setup(t){const e=t;return(n,o)=>(D(),E(u(L),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),jd=O({__name:"CalendarHeadCell",props:{asChild:{type:Boolean},as:{default:"th"}},setup(t){const e=t;return(n,o)=>(D(),E(u(L),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),qd=O({__name:"CalendarHeader",props:{asChild:{type:Boolean},as:{default:"div"}},setup(t){const e=t;return(n,o)=>(D(),E(u(L),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),Jd=O({__name:"CalendarHeading",props:{asChild:{type:Boolean},as:{default:"div"}},setup(t){const e=t,n=Mt();return(o,a)=>(D(),E(u(L),R(e,{"data-disabled":u(n).disabled.value?"":void 0}),{default:$(()=>[A(o.$slots,"default",{headingValue:u(n).headingValue.value},()=>[Ke(Wt(u(n).headingValue.value),1)])]),_:3},16,["data-disabled"]))}}),Gd=O({__name:"CalendarNext",props:{nextPage:{},asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t,n=P(()=>o.disabled.value||o.isNextButtonDisabled(e.nextPage)),o=Mt();return(a,r)=>(D(),E(u(L),{as:e.as,"as-child":e.asChild,"aria-label":"Next page",type:a.as==="button"?"button":void 0,"aria-disabled":n.value||void 0,"data-disabled":n.value||void 0,disabled:n.value,onClick:r[0]||(r[0]=s=>u(o).nextPage(e.nextPage))},{default:$(()=>[A(a.$slots,"default",{disabled:n.value},()=>[r[1]||(r[1]=Ke(" Next page "))])]),_:3},8,["as","as-child","type","aria-disabled","data-disabled","disabled"]))}}),Xd=O({__name:"CalendarPrev",props:{prevPage:{},asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t,n=P(()=>o.disabled.value||o.isPrevButtonDisabled(e.prevPage)),o=Mt();return(a,r)=>(D(),E(u(L),{"aria-label":"Previous page",as:e.as,"as-child":e.asChild,type:a.as==="button"?"button":void 0,"aria-disabled":n.value||void 0,"data-disabled":n.value||void 0,disabled:n.value,onClick:r[0]||(r[0]=s=>u(o).prevPage(e.prevPage))},{default:$(()=>[A(a.$slots,"default",{disabled:n.value},()=>[r[1]||(r[1]=Ke(" Prev page "))])]),_:3},8,["as","as-child","type","aria-disabled","data-disabled","disabled"]))}});function ta(t){return P(()=>{var e;return Vl(t)?!!((e=xe(t))!=null&&e.closest("form")):!0})}const ja="data-reka-collection-item";function ze(t={}){const{key:e="",isProvider:n=!1}=t,o=`${e}CollectionProvider`;let a;if(n){const d=x(new Map);a={collectionRef:x(),itemMap:d},rr(o,a)}else a=ar(o);const r=(d=!1)=>{const f=a.collectionRef.value;if(!f)return[];const p=Array.from(f.querySelectorAll(`[${ja}]`)),y=Array.from(a.itemMap.value.values()).sort((w,m)=>p.indexOf(w.ref)-p.indexOf(m.ref));return d?y:y.filter(w=>w.ref.dataset.disabled!=="")},s=O({name:"CollectionSlot",setup(d,{slots:f}){const{primitiveElement:p,currentElement:h}=Ut();return z(h,()=>{a.collectionRef.value=h.value}),()=>Ue(Oo,{ref:p},f)}}),i=O({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(d,{slots:f,attrs:p}){const{primitiveElement:h,currentElement:y}=Ut();return ne(w=>{if(y.value){const m=Ts(y.value);a.itemMap.value.set(m,{ref:y.value,value:d.value}),w(()=>a.itemMap.value.delete(m))}}),()=>Ue(Oo,{...p,[ja]:"",ref:h},f)}}),l=P(()=>Array.from(a.itemMap.value.values())),c=P(()=>a.itemMap.value.size);return{getItems:r,reactiveItems:l,itemMapSize:c,CollectionSlot:s,CollectionItem:i}}const qu="rovingFocusGroup.onEntryFocus",Ju={bubbles:!1,cancelable:!0},Gu={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Xu(t,e){return e!=="rtl"?t:t==="ArrowLeft"?"ArrowRight":t==="ArrowRight"?"ArrowLeft":t}function Qu(t,e,n){const o=Xu(t.key,n);if(!(e==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(e==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return Gu[o]}function as(t,e=!1){const n=re();for(const o of t)if(o===n||(o.focus({preventScroll:e}),re()!==n))return}function Zu(t,e){return t.map((n,o)=>t[(e+o)%t.length])}const[ec,tc]=q("RovingFocusGroup"),rs=O({__name:"RovingFocusGroup",props:{orientation:{default:void 0},dir:{},loop:{type:Boolean,default:!1},currentTabStopId:{},defaultCurrentTabStopId:{},preventScrollOnEntryFocus:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["entryFocus","update:currentTabStopId"],setup(t,{expose:e,emit:n}){const o=t,a=n,{loop:r,orientation:s,dir:i}=ee(o),l=ct(i),c=fe(o,"currentTabStopId",a,{defaultValue:o.defaultCurrentTabStopId,passive:o.currentTabStopId===void 0}),d=x(!1),f=x(!1),p=x(0),{getItems:h,CollectionSlot:y}=ze({isProvider:!0});function w(v){const b=!f.value;if(v.currentTarget&&v.target===v.currentTarget&&b&&!d.value){const g=new CustomEvent(qu,Ju);if(v.currentTarget.dispatchEvent(g),a("entryFocus",g),!g.defaultPrevented){const C=h().map(F=>F.ref).filter(F=>F.dataset.disabled!==""),_=C.find(F=>F.getAttribute("data-active")===""),M=C.find(F=>F.id===c.value),B=[_,M,...C].filter(Boolean);as(B,o.preventScrollOnEntryFocus)}}f.value=!1}function m(){setTimeout(()=>{f.value=!1},1)}return e({getItems:h}),tc({loop:r,dir:l,orientation:s,currentTabStopId:c,onItemFocus:v=>{c.value=v},onItemShiftTab:()=>{d.value=!0},onFocusableItemAdd:()=>{p.value++},onFocusableItemRemove:()=>{p.value--}}),(v,b)=>(D(),E(u(y),null,{default:$(()=>[H(u(L),{tabindex:d.value||p.value===0?-1:0,"data-orientation":u(s),as:v.as,"as-child":v.asChild,dir:u(l),style:{outline:"none"},onMousedown:b[0]||(b[0]=g=>f.value=!0),onMouseup:m,onFocus:w,onBlur:b[1]||(b[1]=g=>d.value=!1)},{default:$(()=>[A(v.$slots,"default")]),_:3},8,["tabindex","data-orientation","as","as-child","dir"])]),_:3}))}}),qa=O({inheritAttrs:!1,__name:"VisuallyHiddenInputBubble",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(t){const e=t,{primitiveElement:n,currentElement:o}=Ut(),a=P(()=>e.checked??e.value);return z(a,(r,s)=>{if(!o.value)return;const i=o.value,l=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(d&&r!==s){const f=new Event("input",{bubbles:!0}),p=new Event("change",{bubbles:!0});d.call(i,r),i.dispatchEvent(f),i.dispatchEvent(p)}}),(r,s)=>(D(),E(Qo,R({ref_key:"primitiveElement",ref:n},{...e,...r.$attrs},{as:"input"}),null,16))}}),ss=O({inheritAttrs:!1,__name:"VisuallyHiddenInput",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(t){const e=t,n=P(()=>typeof e.value=="object"&&Array.isArray(e.value)&&e.value.length===0&&e.required),o=P(()=>typeof e.value=="string"||typeof e.value=="number"||typeof e.value=="boolean"?[{name:e.name,value:e.value}]:typeof e.value=="object"&&Array.isArray(e.value)?e.value.flatMap((a,r)=>typeof a=="object"?Object.entries(a).map(([s,i])=>({name:`[${e.name}][${r}][${s}]`,value:i})):{name:`[${e.name}][${r}]`,value:a}):e.value!==null&&typeof e.value=="object"&&!Array.isArray(e.value)?Object.entries(e.value).map(([a,r])=>({name:`[${e.name}][${a}]`,value:r})):[]);return(a,r)=>n.value?(D(),E(qa,R({key:a.name},{...e,...a.$attrs},{name:a.name,value:a.value}),null,16,["name","value"])):(D(!0),he(Ht,{key:1},lr(o.value,s=>(D(),E(qa,R({key:s.name,ref_for:!0},{...e,...a.$attrs},{name:s.name,value:s.value}),null,16,["name","value"]))),128))}}),[nc,Qd]=q("CheckboxGroupRoot");function An(t){return t==="indeterminate"}function is(t){return An(t)?"indeterminate":t?"checked":"unchecked"}const ls=O({__name:"RovingFocusItem",props:{tabStopId:{},focusable:{type:Boolean,default:!0},active:{type:Boolean},allowShiftKey:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(t){const e=t,n=ec(),o=ve(),a=P(()=>e.tabStopId||o),r=P(()=>n.currentTabStopId.value===a.value),{getItems:s,CollectionItem:i}=ze();J(()=>{e.focusable&&n.onFocusableItemAdd()}),Qe(()=>{e.focusable&&n.onFocusableItemRemove()});function l(c){if(c.key==="Tab"&&c.shiftKey){n.onItemShiftTab();return}if(c.target!==c.currentTarget)return;const d=Qu(c,n.orientation.value,n.dir.value);if(d!==void 0){if(c.metaKey||c.ctrlKey||c.altKey||!e.allowShiftKey&&c.shiftKey)return;c.preventDefault();let f=[...s().map(p=>p.ref).filter(p=>p.dataset.disabled!=="")];if(d==="last")f.reverse();else if(d==="prev"||d==="next"){d==="prev"&&f.reverse();const p=f.indexOf(c.currentTarget);f=n.loop.value?Zu(f,p+1):f.slice(p+1)}G(()=>as(f))}}return(c,d)=>(D(),E(u(i),null,{default:$(()=>[H(u(L),{tabindex:r.value?0:-1,"data-orientation":u(n).orientation.value,"data-active":c.active?"":void 0,"data-disabled":c.focusable?void 0:"",as:c.as,"as-child":c.asChild,onMousedown:d[0]||(d[0]=f=>{c.focusable?u(n).onItemFocus(a.value):f.preventDefault()}),onFocus:d[1]||(d[1]=f=>u(n).onItemFocus(a.value)),onKeydown:l},{default:$(()=>[A(c.$slots,"default")]),_:3},8,["tabindex","data-orientation","data-active","data-disabled","as","as-child"])]),_:3}))}}),[oc,ac]=q("CheckboxRoot"),Zd=O({inheritAttrs:!1,__name:"CheckboxRoot",props:{defaultValue:{type:[Boolean,String]},modelValue:{type:[Boolean,String,null],default:void 0},disabled:{type:Boolean},value:{default:"on"},id:{},asChild:{type:Boolean},as:{default:"button"},name:{},required:{type:Boolean}},emits:["update:modelValue"],setup(t,{emit:e}){const n=t,o=e,{forwardRef:a,currentElement:r}=I(),s=nc(null),i=fe(n,"modelValue",o,{defaultValue:n.defaultValue,passive:n.modelValue===void 0}),l=P(()=>(s==null?void 0:s.disabled.value)||n.disabled),c=P(()=>Nt(s==null?void 0:s.modelValue.value)?i.value==="indeterminate"?"indeterminate":i.value:Ha(s.modelValue.value,n.value));function d(){if(Nt(s==null?void 0:s.modelValue.value))i.value=An(i.value)?!0:!i.value;else{const h=[...s.modelValue.value||[]];if(Ha(h,n.value)){const y=h.findIndex(w=>En(w,n.value));h.splice(y,1)}else h.push(n.value);s.modelValue.value=h}}const f=ta(r),p=P(()=>{var h;return n.id&&r.value?(h=document.querySelector(`[for="${n.id}"]`))==null?void 0:h.innerText:void 0});return ac({disabled:l,state:c}),(h,y)=>{var w,m;return D(),E(xn((w=u(s))!=null&&w.rovingFocus.value?u(ls):u(L)),R(h.$attrs,{id:h.id,ref:u(a),role:"checkbox","as-child":h.asChild,as:h.as,type:h.as==="button"?"button":void 0,"aria-checked":u(An)(c.value)?"mixed":c.value,"aria-required":h.required,"aria-label":h.$attrs["aria-label"]||p.value,"data-state":u(is)(c.value),"data-disabled":l.value?"":void 0,disabled:l.value,focusable:(m=u(s))!=null&&m.rovingFocus.value?!l.value:void 0,onKeydown:Ct(ce(()=>{},["prevent"]),["enter"]),onClick:d}),{default:$(()=>[A(h.$slots,"default",{modelValue:u(i),state:c.value}),u(f)&&h.name&&!u(s)?(D(),E(u(ss),{key:0,type:"checkbox",checked:!!c.value,name:h.name,value:h.value,disabled:l.value,required:h.required},null,8,["checked","name","value","disabled","required"])):ie("",!0)]),_:3},16,["id","as-child","as","type","aria-checked","aria-required","aria-label","data-state","data-disabled","disabled","focusable","onKeydown"])}}}),ef=O({__name:"CheckboxIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(t){const{forwardRef:e}=I(),n=oc();return(o,a)=>(D(),E(u(Me),{present:o.forceMount||u(An)(u(n).state.value)||u(n).state.value===!0},{default:$(()=>[H(u(L),R({ref:u(e),"data-state":u(is)(u(n).state.value),"data-disabled":u(n).disabled.value?"":void 0,style:{pointerEvents:"none"},"as-child":o.asChild,as:o.as},o.$attrs),{default:$(()=>[A(o.$slots,"default")]),_:3},16,["data-state","data-disabled","as-child","as"])]),_:3},8,["present"]))}}),[us,rc]=q("PopperRoot"),Ln=O({inheritAttrs:!1,__name:"PopperRoot",setup(t){const e=x();return rc({anchor:e,onAnchorChange:n=>e.value=n}),(n,o)=>A(n.$slots,"default")}}),Vn=O({__name:"PopperAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(t){const e=t,{forwardRef:n,currentElement:o}=I(),a=us();return ur(()=>{a.onAnchorChange(e.reference??o.value)}),(r,s)=>(D(),E(u(L),{ref:u(n),as:r.as,"as-child":r.asChild},{default:$(()=>[A(r.$slots,"default")]),_:3},8,["as","as-child"]))}}),sc={key:0,d:"M0 0L6 6L12 0"},ic={key:1,d:"M0 0L4.58579 4.58579C5.36683 5.36683 6.63316 5.36684 7.41421 4.58579L12 0"},lc=O({__name:"Arrow",props:{width:{default:10},height:{default:5},rounded:{type:Boolean},asChild:{type:Boolean},as:{default:"svg"}},setup(t){const e=t;return I(),(n,o)=>(D(),E(u(L),R(e,{width:n.width,height:n.height,viewBox:n.asChild?void 0:"0 0 12 6",preserveAspectRatio:n.asChild?void 0:"none"}),{default:$(()=>[A(n.$slots,"default",{},()=>[n.rounded?(D(),he("path",ic)):(D(),he("path",sc))])]),_:3},16,["width","height","viewBox","preserveAspectRatio"]))}});function uc(t){return t!==null}function cc(t){return{name:"transformOrigin",options:t,fn(e){var m,v,b;const{placement:n,rects:o,middlewareData:a}=e,s=((m=a.arrow)==null?void 0:m.centerOffset)!==0,i=s?0:t.arrowWidth,l=s?0:t.arrowHeight,[c,d]=Mo(n),f={start:"0%",center:"50%",end:"100%"}[d],p=(((v=a.arrow)==null?void 0:v.x)??0)+i/2,h=(((b=a.arrow)==null?void 0:b.y)??0)+l/2;let y="",w="";return c==="bottom"?(y=s?f:`${p}px`,w=`${-l}px`):c==="top"?(y=s?f:`${p}px`,w=`${o.floating.height+l}px`):c==="right"?(y=`${-l}px`,w=s?f:`${h}px`):c==="left"&&(y=`${o.floating.width+l}px`,w=s?f:`${h}px`),{data:{x:y,y:w}}}}}function Mo(t){const[e,n="center"]=t.split("-");return[e,n]}function dc(t){const e=x(),n=P(()=>{var a;return((a=e.value)==null?void 0:a.width)??0}),o=P(()=>{var a;return((a=e.value)==null?void 0:a.height)??0});return J(()=>{const a=xe(t);if(a){e.value={width:a.offsetWidth,height:a.offsetHeight};const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const i=s[0];let l,c;if("borderBoxSize"in i){const d=i.borderBoxSize,f=Array.isArray(d)?d[0]:d;l=f.inlineSize,c=f.blockSize}else l=a.offsetWidth,c=a.offsetHeight;e.value={width:l,height:c}});return r.observe(a,{box:"border-box"}),()=>r.unobserve(a)}else e.value=void 0}),{width:n,height:o}}const cs={side:"bottom",sideOffset:0,align:"center",alignOffset:0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,positionStrategy:"fixed",updatePositionStrategy:"optimized",prioritizePosition:!1},[fc,pc]=q("PopperContent"),Nn=O({inheritAttrs:!1,__name:"PopperContent",props:cr({side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...cs}),emits:["placed"],setup(t,{emit:e}){const n=t,o=e,a=us(),{forwardRef:r,currentElement:s}=I(),i=x(),l=x(),{width:c,height:d}=dc(l),f=P(()=>n.side+(n.align!=="center"?`-${n.align}`:"")),p=P(()=>typeof n.collisionPadding=="number"?n.collisionPadding:{top:0,right:0,bottom:0,left:0,...n.collisionPadding}),h=P(()=>Array.isArray(n.collisionBoundary)?n.collisionBoundary:[n.collisionBoundary]),y=P(()=>({padding:p.value,boundary:h.value.filter(uc),altBoundary:h.value.length>0})),w=Ol(()=>[vl({mainAxis:n.sideOffset+d.value,alignmentAxis:n.alignOffset}),n.prioritizePosition&&n.avoidCollisions&&Fa({...y.value}),n.avoidCollisions&&ml({mainAxis:!0,crossAxis:!!n.prioritizePosition,limiter:n.sticky==="partial"?wl():void 0,...y.value}),!n.prioritizePosition&&n.avoidCollisions&&Fa({...y.value}),yl({...y.value,apply:({elements:k,rects:V,availableWidth:N,availableHeight:K})=>{const{width:U,height:Z}=V.reference,j=k.floating.style;j.setProperty("--reka-popper-available-width",`${N}px`),j.setProperty("--reka-popper-available-height",`${K}px`),j.setProperty("--reka-popper-anchor-width",`${U}px`),j.setProperty("--reka-popper-anchor-height",`${Z}px`)}}),l.value&&Dl({element:l.value,padding:n.arrowPadding}),cc({arrowWidth:c.value,arrowHeight:d.value}),n.hideWhenDetached&&gl({strategy:"referenceHidden",...y.value})]),m=P(()=>n.reference??a.anchor.value),{floatingStyles:v,placement:b,isPositioned:g,middlewareData:C}=El(m,i,{strategy:n.positionStrategy,placement:f,whileElementsMounted:(...k)=>hl(...k,{layoutShift:!n.disableUpdateOnLayoutShift,animationFrame:n.updatePositionStrategy==="always"}),middleware:w}),_=P(()=>Mo(b.value)[0]),M=P(()=>Mo(b.value)[1]);ur(()=>{g.value&&o("placed")});const B=P(()=>{var k;return((k=C.value.arrow)==null?void 0:k.centerOffset)!==0}),F=x("");ne(()=>{s.value&&(F.value=window.getComputedStyle(s.value).zIndex)});const S=P(()=>{var k;return((k=C.value.arrow)==null?void 0:k.x)??0}),T=P(()=>{var k;return((k=C.value.arrow)==null?void 0:k.y)??0});return pc({placedSide:_,onArrowChange:k=>l.value=k,arrowX:S,arrowY:T,shouldHideArrow:B}),(k,V)=>{var N,K,U;return D(),he("div",{ref_key:"floatingRef",ref:i,"data-reka-popper-content-wrapper":"",style:Ze({...u(v),transform:u(g)?u(v).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:F.value,"--reka-popper-transform-origin":[(N=u(C).transformOrigin)==null?void 0:N.x,(K=u(C).transformOrigin)==null?void 0:K.y].join(" "),...((U=u(C).hide)==null?void 0:U.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}})},[H(u(L),R({ref:u(r)},k.$attrs,{"as-child":n.asChild,as:k.as,"data-side":_.value,"data-align":M.value,style:{animation:u(g)?void 0:"none"}}),{default:$(()=>[A(k.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4)}}}),hc={top:"bottom",right:"left",bottom:"top",left:"right"},vc=O({inheritAttrs:!1,__name:"PopperArrow",props:{width:{},height:{},rounded:{type:Boolean},asChild:{type:Boolean},as:{default:"svg"}},setup(t){const{forwardRef:e}=I(),n=fc(),o=P(()=>hc[n.placedSide.value]);return(a,r)=>{var s,i,l,c;return D(),he("span",{ref:d=>{u(n).onArrowChange(d)},style:Ze({position:"absolute",left:(s=u(n).arrowX)!=null&&s.value?`${(i=u(n).arrowX)==null?void 0:i.value}px`:void 0,top:(l=u(n).arrowY)!=null&&l.value?`${(c=u(n).arrowY)==null?void 0:c.value}px`:void 0,[o.value]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[u(n).placedSide.value],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[u(n).placedSide.value],visibility:u(n).shouldHideArrow.value?"hidden":void 0})},[H(lc,R(a.$attrs,{ref:u(e),style:{display:"block"},as:a.as,"as-child":a.asChild,rounded:a.rounded,width:a.width,height:a.height}),{default:$(()=>[A(a.$slots,"default")]),_:3},16,["as","as-child","rounded","width","height"])],4)}}});function na(t){const e=Yr("",1e3);return{search:e,handleTypeaheadSearch:(a,r)=>{e.value=e.value+a;{const s=re(),i=r.map(p=>{var h,y;return{...p,textValue:((h=p.value)==null?void 0:h.textValue)??((y=p.ref.textContent)==null?void 0:y.trim())??""}}),l=i.find(p=>p.ref===s),c=i.map(p=>p.textValue),d=yc(c,e.value,l==null?void 0:l.textValue),f=i.find(p=>p.textValue===d);return f&&f.ref.focus(),f==null?void 0:f.ref}},resetTypeahead:()=>{e.value=""}}}function mc(t,e){return t.map((n,o)=>t[(e+o)%t.length])}function yc(t,e,n){const a=e.length>1&&Array.from(e).every(c=>c===e[0])?e[0]:e,r=n?t.indexOf(n):-1;let s=mc(t,Math.max(r,0));a.length===1&&(s=s.filter(c=>c!==n));const l=s.find(c=>c.toLowerCase().startsWith(a.toLowerCase()));return l!==n?l:void 0}function ds(t){const e=jt({nonce:x()});return P(()=>{var n;return(t==null?void 0:t.value)||((n=e.nonce)==null?void 0:n.value)})}function gc(){const t=x(!1);return J(()=>{xt("keydown",()=>{t.value=!0},{capture:!0,passive:!0}),xt(["pointerdown","pointermove"],()=>{t.value=!1},{capture:!0,passive:!0})}),t}const bc=Kr(gc),[Un,wc]=q(["MenuRoot","MenuSub"],"MenuContext"),[oa,$c]=q("MenuRoot"),Cc=O({__name:"MenuRoot",props:{open:{type:Boolean,default:!1},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(t,{emit:e}){const n=t,o=e,{modal:a,dir:r}=ee(n),s=ct(r),i=fe(n,"open",o),l=x(),c=bc();return wc({open:i,onOpenChange:d=>{i.value=d},content:l,onContentChange:d=>{l.value=d}}),$c({onClose:()=>{i.value=!1},isUsingKeyboardRef:c,dir:s,modal:a}),(d,f)=>(D(),E(u(Ln),null,{default:$(()=>[A(d.$slots,"default")]),_:3}))}});let ho=0;function aa(){ne(t=>{if(!_e)return;const e=document.querySelectorAll("[data-reka-focus-guard]");document.body.insertAdjacentElement("afterbegin",e[0]??Ja()),document.body.insertAdjacentElement("beforeend",e[1]??Ja()),ho++,t(()=>{ho===1&&document.querySelectorAll("[data-reka-focus-guard]").forEach(n=>n.remove()),ho--})})}function Ja(){const t=document.createElement("span");return t.setAttribute("data-reka-focus-guard",""),t.tabIndex=0,t.style.outline="none",t.style.opacity="0",t.style.position="fixed",t.style.pointerEvents="none",t}const[fs,Dc]=q("MenuContent"),ps=O({__name:"MenuContentImpl",props:cr({loop:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},disableOutsideScroll:{type:Boolean},trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},{...cs}),emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus","dismiss"],setup(t,{emit:e}){const n=t,o=e,a=Un(),r=oa(),{trapFocus:s,disableOutsidePointerEvents:i,loop:l}=ee(n);aa(),Fn(i.value);const c=x(""),d=x(0),f=x(0),p=x(null),h=x("right"),y=x(0),w=x(null),m=x(),{forwardRef:v,currentElement:b}=I(),{handleTypeaheadSearch:g}=na();z(b,S=>{a.onContentChange(S)}),Qe(()=>{window.clearTimeout(d.value)});function C(S){var k,V;return h.value===((k=p.value)==null?void 0:k.side)&&ou(S,(V=p.value)==null?void 0:V.area)}async function _(S){var T;o("openAutoFocus",S),!S.defaultPrevented&&(S.preventDefault(),(T=b.value)==null||T.focus({preventScroll:!0}))}function M(S){var j;if(S.defaultPrevented)return;const k=S.target.closest("[data-reka-menu-content]")===S.currentTarget,V=S.ctrlKey||S.altKey||S.metaKey,N=S.key.length===1,K=Jl(S,re(),b.value,{loop:l.value,arrowKeyOptions:"vertical",dir:r==null?void 0:r.dir.value,focus:!0,attributeName:"[data-reka-collection-item]:not([data-disabled])"});if(K)return K==null?void 0:K.focus();if(S.code==="Space")return;const U=((j=m.value)==null?void 0:j.getItems())??[];if(k&&(S.key==="Tab"&&S.preventDefault(),!V&&N&&g(S.key,U)),S.target!==b.value||!tu.includes(S.key))return;S.preventDefault();const Z=[...U.map(ae=>ae.ref)];Xr.includes(S.key)&&Z.reverse(),xo(Z)}function B(S){var T,k;(k=(T=S==null?void 0:S.currentTarget)==null?void 0:T.contains)!=null&&k.call(T,S.target)||(window.clearTimeout(d.value),c.value="")}function F(S){var V;if(!Po(S))return;const T=S.target,k=y.value!==S.clientX;if((V=S==null?void 0:S.currentTarget)!=null&&V.contains(T)&&k){const N=S.clientX>y.value?"right":"left";h.value=N,y.value=S.clientX}}return Dc({onItemEnter:S=>!!C(S),onItemLeave:S=>{var T;C(S)||((T=b.value)==null||T.focus(),w.value=null)},onTriggerLeave:S=>!!C(S),searchRef:c,pointerGraceTimerRef:f,onPointerGraceIntentChange:S=>{p.value=S}}),(S,T)=>(D(),E(u(In),{"as-child":"",trapped:u(s),onMountAutoFocus:_,onUnmountAutoFocus:T[7]||(T[7]=k=>o("closeAutoFocus",k))},{default:$(()=>[H(u(Jt),{"as-child":"","disable-outside-pointer-events":u(i),onEscapeKeyDown:T[2]||(T[2]=k=>o("escapeKeyDown",k)),onPointerDownOutside:T[3]||(T[3]=k=>o("pointerDownOutside",k)),onFocusOutside:T[4]||(T[4]=k=>o("focusOutside",k)),onInteractOutside:T[5]||(T[5]=k=>o("interactOutside",k)),onDismiss:T[6]||(T[6]=k=>o("dismiss"))},{default:$(()=>[H(u(rs),{ref_key:"rovingFocusGroupRef",ref:m,"current-tab-stop-id":w.value,"onUpdate:currentTabStopId":T[0]||(T[0]=k=>w.value=k),"as-child":"",orientation:"vertical",dir:u(r).dir.value,loop:u(l),onEntryFocus:T[1]||(T[1]=k=>{o("entryFocus",k),u(r).isUsingKeyboardRef.value||k.preventDefault()})},{default:$(()=>[H(u(Nn),{ref:u(v),role:"menu",as:S.as,"as-child":S.asChild,"aria-orientation":"vertical","data-reka-menu-content":"","data-state":u(Qr)(u(a).open.value),dir:u(r).dir.value,side:S.side,"side-offset":S.sideOffset,align:S.align,"align-offset":S.alignOffset,"avoid-collisions":S.avoidCollisions,"collision-boundary":S.collisionBoundary,"collision-padding":S.collisionPadding,"arrow-padding":S.arrowPadding,"prioritize-position":S.prioritizePosition,"position-strategy":S.positionStrategy,"update-position-strategy":S.updatePositionStrategy,sticky:S.sticky,"hide-when-detached":S.hideWhenDetached,reference:S.reference,onKeydown:M,onBlur:B,onPointermove:F},{default:$(()=>[A(S.$slots,"default")]),_:3},8,["as","as-child","data-state","dir","side","side-offset","align","align-offset","avoid-collisions","collision-boundary","collision-padding","arrow-padding","prioritize-position","position-strategy","update-position-strategy","sticky","hide-when-detached","reference"])]),_:3},8,["current-tab-stop-id","dir","loop"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),Ec=O({inheritAttrs:!1,__name:"MenuItemImpl",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(t){const e=t,n=fs(),{forwardRef:o}=I(),{CollectionItem:a}=ze(),r=x(!1);async function s(l){if(!l.defaultPrevented&&Po(l)){if(e.disabled)n.onItemLeave(l);else if(!n.onItemEnter(l)){const d=l.currentTarget;d==null||d.focus({preventScroll:!0})}}}async function i(l){await G(),!l.defaultPrevented&&Po(l)&&n.onItemLeave(l)}return(l,c)=>(D(),E(u(a),{value:{textValue:l.textValue}},{default:$(()=>[H(u(L),R({ref:u(o),role:"menuitem",tabindex:"-1"},l.$attrs,{as:l.as,"as-child":l.asChild,"aria-disabled":l.disabled||void 0,"data-disabled":l.disabled?"":void 0,"data-highlighted":r.value?"":void 0,onPointermove:s,onPointerleave:i,onFocus:c[0]||(c[0]=async d=>{await G(),!(d.defaultPrevented||l.disabled)&&(r.value=!0)}),onBlur:c[1]||(c[1]=async d=>{await G(),!d.defaultPrevented&&(r.value=!1)})}),{default:$(()=>[A(l.$slots,"default")]),_:3},16,["as","as-child","aria-disabled","data-disabled","data-highlighted"])]),_:3},8,["value"]))}}),Ac=O({__name:"MenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(t,{emit:e}){const n=t,o=e,{forwardRef:a,currentElement:r}=I(),s=oa(),i=fs(),l=x(!1);async function c(){const d=r.value;if(!n.disabled&&d){const f=new CustomEvent(Zl,{bubbles:!0,cancelable:!0});o("select",f),await G(),f.defaultPrevented?l.value=!1:s.onClose()}}return(d,f)=>(D(),E(Ec,R(n,{ref:u(a),onClick:c,onPointerdown:f[0]||(f[0]=()=>{l.value=!0}),onPointerup:f[1]||(f[1]=async p=>{var h;await G(),!p.defaultPrevented&&(l.value||(h=p.currentTarget)==null||h.click())}),onKeydown:f[2]||(f[2]=async p=>{const h=u(i).searchRef.value!=="";d.disabled||h&&p.key===" "||u(So).includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})}),{default:$(()=>[A(d.$slots,"default")]),_:3},16))}}),Oc=O({__name:"MenuRootContentModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,o=e,a=Ye(n,o),r=Un(),{forwardRef:s,currentElement:i}=I();return Rn(i),(l,c)=>(D(),E(ps,R(u(a),{ref:u(s),"trap-focus":u(r).open.value,"disable-outside-pointer-events":u(r).open.value,"disable-outside-scroll":!0,onDismiss:c[0]||(c[0]=d=>u(r).onOpenChange(!1)),onFocusOutside:c[1]||(c[1]=ce(d=>o("focusOutside",d),["prevent"]))}),{default:$(()=>[A(l.$slots,"default")]),_:3},16,["trap-focus","disable-outside-pointer-events"]))}}),Sc=O({__name:"MenuRootContentNonModal",props:{loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const a=Ye(t,e),r=Un();return(s,i)=>(D(),E(ps,R(u(a),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:i[0]||(i[0]=l=>u(r).onOpenChange(!1))}),{default:$(()=>[A(s.$slots,"default")]),_:3},16))}}),xc=O({__name:"MenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const a=Ye(t,e),r=Un(),s=oa();return(i,l)=>(D(),E(u(Me),{present:i.forceMount||u(r).open.value},{default:$(()=>[u(s).modal.value?(D(),E(Oc,X(R({key:0},{...i.$attrs,...u(a)})),{default:$(()=>[A(i.$slots,"default")]),_:3},16)):(D(),E(Sc,X(R({key:1},{...i.$attrs,...u(a)})),{default:$(()=>[A(i.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Pc=O({__name:"MenuGroup",props:{asChild:{type:Boolean},as:{}},setup(t){const e=t;return(n,o)=>(D(),E(u(L),R({role:"group"},e),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),_c=O({__name:"MenuLabel",props:{asChild:{type:Boolean},as:{default:"div"}},setup(t){const e=t;return(n,o)=>(D(),E(u(L),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),Mc=O({__name:"MenuPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const e=t;return(n,o)=>(D(),E(u(Gt),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),kc=O({__name:"MenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(t){const e=t;return(n,o)=>(D(),E(u(L),R(e,{role:"separator","aria-orientation":"horizontal"}),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),Bc=O({__name:"MenuAnchor",props:{reference:{},asChild:{type:Boolean},as:{}},setup(t){const e=t;return(n,o)=>(D(),E(u(Vn),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),[Qt,Tc]=q("PopoverRoot"),tf=O({__name:"PopoverRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(t,{emit:e}){const n=t,o=e,{modal:a}=ee(n),r=fe(n,"open",o,{defaultValue:n.defaultOpen,passive:n.open===void 0}),s=x(),i=x(!1);return Tc({contentId:"",triggerId:"",modal:a,open:r,onOpenChange:l=>{r.value=l},onOpenToggle:()=>{r.value=!r.value},triggerElement:s,hasCustomAnchor:i}),(l,c)=>(D(),E(u(Ln),null,{default:$(()=>[A(l.$slots,"default",{open:u(r)})]),_:3}))}}),nf=O({__name:"PopoverPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const e=t;return(n,o)=>(D(),E(u(Gt),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),hs=O({__name:"PopoverContentImpl",props:{trapFocus:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,o=e,a=Xt(Ml(n,"trapFocus","disableOutsidePointerEvents")),{forwardRef:r}=I(),s=Qt();return aa(),(i,l)=>(D(),E(u(In),{"as-child":"",loop:"",trapped:i.trapFocus,onMountAutoFocus:l[5]||(l[5]=c=>o("openAutoFocus",c)),onUnmountAutoFocus:l[6]||(l[6]=c=>o("closeAutoFocus",c))},{default:$(()=>[H(u(Jt),{"as-child":"","disable-outside-pointer-events":i.disableOutsidePointerEvents,onPointerDownOutside:l[0]||(l[0]=c=>o("pointerDownOutside",c)),onInteractOutside:l[1]||(l[1]=c=>o("interactOutside",c)),onEscapeKeyDown:l[2]||(l[2]=c=>o("escapeKeyDown",c)),onFocusOutside:l[3]||(l[3]=c=>o("focusOutside",c)),onDismiss:l[4]||(l[4]=c=>u(s).onOpenChange(!1))},{default:$(()=>[H(u(Nn),R(u(a),{id:u(s).contentId,ref:u(r),"data-state":u(s).open.value?"open":"closed","aria-labelledby":u(s).triggerId,style:{"--reka-popover-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-popover-content-available-width":"var(--reka-popper-available-width)","--reka-popover-content-available-height":"var(--reka-popper-available-height)","--reka-popover-trigger-width":"var(--reka-popper-anchor-width)","--reka-popover-trigger-height":"var(--reka-popper-anchor-height)"},role:"dialog"}),{default:$(()=>[A(i.$slots,"default")]),_:3},16,["id","data-state","aria-labelledby"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),Ic=O({__name:"PopoverContentModal",props:{side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,o=e,a=Qt(),r=x(!1);Fn(!0);const s=Ye(n,o),{forwardRef:i,currentElement:l}=I();return Rn(l),(c,d)=>(D(),E(hs,R(u(s),{ref:u(i),"trap-focus":u(a).open.value,"disable-outside-pointer-events":"",onCloseAutoFocus:d[0]||(d[0]=ce(f=>{var p;o("closeAutoFocus",f),r.value||(p=u(a).triggerElement.value)==null||p.focus()},["prevent"])),onPointerDownOutside:d[1]||(d[1]=f=>{o("pointerDownOutside",f);const p=f.detail.originalEvent,h=p.button===0&&p.ctrlKey===!0,y=p.button===2||h;r.value=y}),onFocusOutside:d[2]||(d[2]=ce(()=>{},["prevent"]))}),{default:$(()=>[A(c.$slots,"default")]),_:3},16,["trap-focus"]))}}),Rc=O({__name:"PopoverContentNonModal",props:{side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,o=e,a=Qt(),r=x(!1),s=x(!1),i=Ye(n,o);return(l,c)=>(D(),E(hs,R(u(i),{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:c[0]||(c[0]=d=>{var f;o("closeAutoFocus",d),d.defaultPrevented||(r.value||(f=u(a).triggerElement.value)==null||f.focus(),d.preventDefault()),r.value=!1,s.value=!1}),onInteractOutside:c[1]||(c[1]=async d=>{var h;o("interactOutside",d),d.defaultPrevented||(r.value=!0,d.detail.originalEvent.type==="pointerdown"&&(s.value=!0));const f=d.target;((h=u(a).triggerElement.value)==null?void 0:h.contains(f))&&d.preventDefault(),d.detail.originalEvent.type==="focusin"&&s.value&&d.preventDefault()})}),{default:$(()=>[A(l.$slots,"default")]),_:3},16))}}),of=O({__name:"PopoverContent",props:{forceMount:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(t,{emit:e}){const n=t,o=e,a=Qt(),r=Ye(n,o),{forwardRef:s}=I();return a.contentId||(a.contentId=ve(void 0,"reka-popover-content")),(i,l)=>(D(),E(u(Me),{present:i.forceMount||u(a).open.value},{default:$(()=>[u(a).modal.value?(D(),E(Ic,R({key:0},u(r),{ref:u(s)}),{default:$(()=>[A(i.$slots,"default")]),_:3},16)):(D(),E(Rc,R({key:1},u(r),{ref:u(s)}),{default:$(()=>[A(i.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),af=O({__name:"PopoverTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t,n=Qt(),{forwardRef:o,currentElement:a}=I();return n.triggerId||(n.triggerId=ve(void 0,"reka-popover-trigger")),J(()=>{n.triggerElement.value=a.value}),(r,s)=>(D(),E(xn(u(n).hasCustomAnchor.value?u(L):u(Vn)),{"as-child":""},{default:$(()=>[H(u(L),{id:u(n).triggerId,ref:u(o),type:r.as==="button"?"button":void 0,"aria-haspopup":"dialog","aria-expanded":u(n).open.value,"aria-controls":u(n).contentId,"data-state":u(n).open.value?"open":"closed",as:r.as,"as-child":e.asChild,onClick:u(n).onOpenToggle},{default:$(()=>[A(r.$slots,"default")]),_:3},8,["id","type","aria-expanded","aria-controls","data-state","as","as-child","onClick"])]),_:3}))}}),rf=O({__name:"DialogPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const e=t;return(n,o)=>(D(),E(u(Gt),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),[vs,Fc]=q("DropdownMenuRoot"),sf=O({__name:"DropdownMenuRoot",props:{defaultOpen:{type:Boolean},open:{type:Boolean,default:void 0},dir:{},modal:{type:Boolean,default:!0}},emits:["update:open"],setup(t,{emit:e}){const n=t,o=e;I();const a=fe(n,"open",o,{defaultValue:n.defaultOpen,passive:n.open===void 0}),r=x(),{modal:s,dir:i}=ee(n),l=ct(i);return Fc({open:a,onOpenChange:c=>{a.value=c},onOpenToggle:()=>{a.value=!a.value},triggerId:"",triggerElement:r,contentId:"",modal:s,dir:l}),(c,d)=>(D(),E(u(Cc),{open:u(a),"onUpdate:open":d[0]||(d[0]=f=>cn(a)?a.value=f:null),dir:u(l),modal:u(s)},{default:$(()=>[A(c.$slots,"default",{open:u(a)})]),_:3},8,["open","dir","modal"]))}}),lf=O({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(t,{emit:e}){const a=Ye(t,e);I();const r=vs(),s=x(!1);function i(l){l.defaultPrevented||(s.value||setTimeout(()=>{var c;(c=r.triggerElement.value)==null||c.focus()},0),s.value=!1,l.preventDefault())}return r.contentId||(r.contentId=ve(void 0,"reka-dropdown-menu-content")),(l,c)=>{var d;return D(),E(u(xc),R(u(a),{id:u(r).contentId,"aria-labelledby":(d=u(r))==null?void 0:d.triggerId,style:{"--reka-dropdown-menu-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-dropdown-menu-content-available-width":"var(--reka-popper-available-width)","--reka-dropdown-menu-content-available-height":"var(--reka-popper-available-height)","--reka-dropdown-menu-trigger-width":"var(--reka-popper-anchor-width)","--reka-dropdown-menu-trigger-height":"var(--reka-popper-anchor-height)"},onCloseAutoFocus:i,onInteractOutside:c[0]||(c[0]=f=>{var w;if(f.defaultPrevented)return;const p=f.detail.originalEvent,h=p.button===0&&p.ctrlKey===!0,y=p.button===2||h;(!u(r).modal.value||y)&&(s.value=!0),(w=u(r).triggerElement.value)!=null&&w.contains(f.target)&&f.preventDefault()})}),{default:$(()=>[A(l.$slots,"default")]),_:3},16,["id","aria-labelledby"])}}}),uf=O({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(t){const e=t;return I(),(n,o)=>(D(),E(u(Pc),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),cf=O({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(t,{emit:e}){const n=t,a=qt(e);return I(),(r,s)=>(D(),E(u(Ac),X(oe({...n,...u(a)})),{default:$(()=>[A(r.$slots,"default")]),_:3},16))}}),df=O({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{}},setup(t){const e=t;return I(),(n,o)=>(D(),E(u(_c),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),ff=O({__name:"DropdownMenuPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const e=t;return(n,o)=>(D(),E(u(Mc),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),pf=O({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{}},setup(t){const e=t;return I(),(n,o)=>(D(),E(u(kc),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),hf=O({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t,n=vs(),{forwardRef:o,currentElement:a}=I();return J(()=>{n.triggerElement=a}),n.triggerId||(n.triggerId=ve(void 0,"reka-dropdown-menu-trigger")),(r,s)=>(D(),E(u(Bc),{"as-child":""},{default:$(()=>[H(u(L),{id:u(n).triggerId,ref:u(o),type:r.as==="button"?"button":void 0,"as-child":e.asChild,as:r.as,"aria-haspopup":"menu","aria-expanded":u(n).open.value,"aria-controls":u(n).open.value?u(n).contentId:void 0,"data-disabled":r.disabled?"":void 0,disabled:r.disabled,"data-state":u(n).open.value?"open":"closed",onClick:s[0]||(s[0]=async i=>{var l;!r.disabled&&i.button===0&&i.ctrlKey===!1&&((l=u(n))==null||l.onOpenToggle(),await G(),u(n).open.value&&i.preventDefault())}),onKeydown:s[1]||(s[1]=Ct(i=>{r.disabled||(["Enter"," "].includes(i.key)&&u(n).onOpenToggle(),i.key==="ArrowDown"&&u(n).onOpenChange(!0),["Enter"," ","ArrowDown"].includes(i.key)&&i.preventDefault())},["enter","space","arrow-down"]))},{default:$(()=>[A(r.$slots,"default")]),_:3},8,["id","type","as-child","as","aria-expanded","aria-controls","data-disabled","disabled","data-state"])]),_:3}))}});function Lc(t,e){const n=Yr(!1,300),o=x(null),a=Sl();function r(){o.value=null,n.value=!1}function s(i,l){const c=i.currentTarget,d={x:i.clientX,y:i.clientY},f=Vc(d,c.getBoundingClientRect()),p=Nc(d,f),h=Uc(l.getBoundingClientRect()),y=Wc([...p,...h]);o.value=y,n.value=!0}return ne(i=>{if(t.value&&e.value){const l=d=>s(d,e.value),c=d=>s(d,t.value);t.value.addEventListener("pointerleave",l),e.value.addEventListener("pointerleave",c),i(()=>{var d,f;(d=t.value)==null||d.removeEventListener("pointerleave",l),(f=e.value)==null||f.removeEventListener("pointerleave",c)})}}),ne(i=>{var l;if(o.value){const c=d=>{var m,v;if(!o.value||!(d.target instanceof HTMLElement))return;const f=d.target,p={x:d.clientX,y:d.clientY},h=((m=t.value)==null?void 0:m.contains(f))||((v=e.value)==null?void 0:v.contains(f)),y=!Hc(p,o.value),w=!!f.closest("[data-grace-area-trigger]");h?r():(y||w)&&(r(),a.trigger())};(l=t.value)==null||l.ownerDocument.addEventListener("pointermove",c),i(()=>{var d;return(d=t.value)==null?void 0:d.ownerDocument.removeEventListener("pointermove",c)})}}),{isPointerInTransit:n,onPointerExit:a.on}}function Vc(t,e){const n=Math.abs(e.top-t.y),o=Math.abs(e.bottom-t.y),a=Math.abs(e.right-t.x),r=Math.abs(e.left-t.x);switch(Math.min(n,o,a,r)){case r:return"left";case a:return"right";case n:return"top";case o:return"bottom";default:throw new Error("unreachable")}}function Nc(t,e,n=5){const o=[];switch(e){case"top":o.push({x:t.x-n,y:t.y+n},{x:t.x+n,y:t.y+n});break;case"bottom":o.push({x:t.x-n,y:t.y-n},{x:t.x+n,y:t.y-n});break;case"left":o.push({x:t.x+n,y:t.y-n},{x:t.x+n,y:t.y+n});break;case"right":o.push({x:t.x-n,y:t.y-n},{x:t.x-n,y:t.y+n});break}return o}function Uc(t){const{top:e,right:n,bottom:o,left:a}=t;return[{x:a,y:e},{x:n,y:e},{x:n,y:o},{x:a,y:o}]}function Hc(t,e){const{x:n,y:o}=t;let a=!1;for(let r=0,s=e.length-1;r<e.length;s=r++){const i=e[r].x,l=e[r].y,c=e[s].x,d=e[s].y;l>o!=d>o&&n<(c-i)*(o-l)/(d-l)+i&&(a=!a)}return a}function Wc(t){const e=t.slice();return e.sort((n,o)=>n.x<o.x?-1:n.x>o.x?1:n.y<o.y?-1:n.y>o.y?1:0),Kc(e)}function Kc(t){if(t.length<=1)return t.slice();const e=[];for(let o=0;o<t.length;o++){const a=t[o];for(;e.length>=2;){const r=e[e.length-1],s=e[e.length-2];if((r.x-s.x)*(a.y-s.y)>=(r.y-s.y)*(a.x-s.x))e.pop();else break}e.push(a)}e.pop();const n=[];for(let o=t.length-1;o>=0;o--){const a=t[o];for(;n.length>=2;){const r=n[n.length-1],s=n[n.length-2];if((r.x-s.x)*(a.y-s.y)>=(r.y-s.y)*(a.x-s.x))n.pop();else break}n.push(a)}return n.pop(),e.length===1&&n.length===1&&e[0].x===n[0].x&&e[0].y===n[0].y?e:e.concat(n)}const vf=O({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{default:"label"}},setup(t){const e=t;return I(),(n,o)=>(D(),E(u(L),R(e,{onMousedown:o[0]||(o[0]=a=>{!a.defaultPrevented&&a.detail>1&&a.preventDefault()})}),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}});function ko(t,e=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY){return Math.min(n,Math.max(e,t))}const[me,Yc]=q("ScrollAreaRoot"),mf=O({__name:"ScrollAreaRoot",props:{type:{default:"hover"},dir:{},scrollHideDelay:{default:600},asChild:{type:Boolean},as:{}},setup(t,{expose:e}){const n=t,o=x(0),a=x(0),r=x(),s=x(),i=x(),l=x(),c=x(!1),d=x(!1),{type:f,dir:p,scrollHideDelay:h}=ee(n),y=ct(p);function w(){var g;(g=r.value)==null||g.scrollTo({top:0})}function m(){var g;(g=r.value)==null||g.scrollTo({top:0,left:0})}e({viewport:r,scrollTop:w,scrollTopLeft:m});const{forwardRef:v,currentElement:b}=I();return Yc({type:f,dir:y,scrollHideDelay:h,scrollArea:b,viewport:r,onViewportChange:g=>{r.value=g||void 0},content:s,onContentChange:g=>{s.value=g},scrollbarX:i,scrollbarXEnabled:c,scrollbarY:l,scrollbarYEnabled:d,onScrollbarXChange:g=>{i.value=g||void 0},onScrollbarYChange:g=>{l.value=g||void 0},onScrollbarXEnabledChange:g=>{c.value=g},onScrollbarYEnabledChange:g=>{d.value=g},onCornerWidthChange:g=>{o.value=g},onCornerHeightChange:g=>{a.value=g}}),(g,C)=>(D(),E(u(L),{ref:u(v),"as-child":n.asChild,as:g.as,dir:u(y),style:Ze({position:"relative","--reka-scroll-area-corner-width":`${o.value}px`,"--reka-scroll-area-corner-height":`${a.value}px`})},{default:$(()=>[A(g.$slots,"default")]),_:3},8,["as-child","as","dir","style"]))}}),zc=O({__name:"ScrollAreaCornerImpl",setup(t){const e=me(),n=x(0),o=x(0),a=P(()=>!!n.value&&!!o.value);function r(){var l;const i=((l=e.scrollbarX.value)==null?void 0:l.offsetHeight)||0;e.onCornerHeightChange(i),o.value=i}function s(){var l;const i=((l=e.scrollbarY.value)==null?void 0:l.offsetWidth)||0;e.onCornerWidthChange(i),n.value=i}return st(e.scrollbarX.value,r),st(e.scrollbarY.value,s),z(()=>e.scrollbarX.value,r),z(()=>e.scrollbarY.value,s),(i,l)=>{var c;return a.value?(D(),E(u(L),R({key:0,style:{width:`${n.value}px`,height:`${o.value}px`,position:"absolute",right:u(e).dir.value==="ltr"?0:void 0,left:u(e).dir.value==="rtl"?0:void 0,bottom:0}},(c=i.$parent)==null?void 0:c.$props),{default:$(()=>[A(i.$slots,"default")]),_:3},16,["style"])):ie("",!0)}}}),yf=O({__name:"ScrollAreaCorner",props:{asChild:{type:Boolean},as:{}},setup(t){const e=t,{forwardRef:n}=I(),o=me(),a=P(()=>!!o.scrollbarX.value&&!!o.scrollbarY.value),r=P(()=>o.type.value!=="scroll"&&a.value);return(s,i)=>r.value?(D(),E(zc,R({key:0},e,{ref:u(n)}),{default:$(()=>[A(s.$slots,"default")]),_:3},16)):ie("",!0)}});function ms(t,e){return n=>{if(t[0]===t[1]||e[0]===e[1])return e[0];const o=(e[1]-e[0])/(t[1]-t[0]);return e[0]+o*(n-t[0])}}function Hn(t){const e=ys(t.viewport,t.content),n=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,o=(t.scrollbar.size-n)*e;return Math.max(o,18)}function ys(t,e){const n=t/e;return Number.isNaN(n)?0:n}function jc(t,e=()=>{}){let n={left:t.scrollLeft,top:t.scrollTop},o=0;return function a(){const r={left:t.scrollLeft,top:t.scrollTop},s=n.left!==r.left,i=n.top!==r.top;(s||i)&&e(),n=r,o=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(o)}function Ga(t,e,n="ltr"){const o=Hn(e),a=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,r=e.scrollbar.size-a,s=e.content-e.viewport,i=r-o,l=n==="ltr"?[0,s]:[s*-1,0],c=ko(t,l[0],l[1]);return ms([0,s],[0,i])(c)}function ln(t){return t?Number.parseInt(t,10):0}function qc(t,e,n,o="ltr"){const a=Hn(n),r=a/2,s=e||r,i=a-s,l=n.scrollbar.paddingStart+s,c=n.scrollbar.size-n.scrollbar.paddingEnd-i,d=n.content-n.viewport,f=o==="ltr"?[0,d]:[d*-1,0];return ms([l,c],f)(t)}function Xa(t,e){return t>0&&t<e}const gs=O({__name:"ScrollAreaScrollbarImpl",props:{isHorizontal:{type:Boolean}},emits:["onDragScroll","onWheelScroll","onThumbPointerDown"],setup(t,{emit:e}){const n=t,o=e,a=me(),r=Wn(),s=Kn(),{forwardRef:i,currentElement:l}=I(),c=x(""),d=x();function f(v){var b,g;if(d.value){const C=v.clientX-((b=d.value)==null?void 0:b.left),_=v.clientY-((g=d.value)==null?void 0:g.top);o("onDragScroll",{x:C,y:_})}}function p(v){v.button===0&&(v.target.setPointerCapture(v.pointerId),d.value=l.value.getBoundingClientRect(),c.value=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",a.viewport&&(a.viewport.value.style.scrollBehavior="auto"),f(v))}function h(v){f(v)}function y(v){const b=v.target;b.hasPointerCapture(v.pointerId)&&b.releasePointerCapture(v.pointerId),document.body.style.webkitUserSelect=c.value,a.viewport&&(a.viewport.value.style.scrollBehavior=""),d.value=void 0}function w(v){var _;const b=v.target,g=(_=l.value)==null?void 0:_.contains(b),C=r.sizes.value.content-r.sizes.value.viewport;g&&r.handleWheelScroll(v,C)}J(()=>{document.addEventListener("wheel",w,{passive:!1})}),Qe(()=>{document.removeEventListener("wheel",w)});function m(){var v,b,g,C,_;l.value&&(n.isHorizontal?r.handleSizeChange({content:((v=a.viewport.value)==null?void 0:v.scrollWidth)??0,viewport:((b=a.viewport.value)==null?void 0:b.offsetWidth)??0,scrollbar:{size:l.value.clientWidth??0,paddingStart:ln(getComputedStyle(l.value).paddingLeft),paddingEnd:ln(getComputedStyle(l.value).paddingRight)}}):r.handleSizeChange({content:((g=a.viewport.value)==null?void 0:g.scrollHeight)??0,viewport:((C=a.viewport.value)==null?void 0:C.offsetHeight)??0,scrollbar:{size:((_=l.value)==null?void 0:_.clientHeight)??0,paddingStart:ln(getComputedStyle(l.value).paddingLeft),paddingEnd:ln(getComputedStyle(l.value).paddingRight)}}))}return st(l,m),st(a.content,m),(v,b)=>(D(),E(u(L),{ref:u(i),style:{position:"absolute"},"data-scrollbarimpl":"",as:u(s).as.value,"as-child":u(s).asChild.value,onPointerdown:p,onPointermove:h,onPointerup:y},{default:$(()=>[A(v.$slots,"default")]),_:3},8,["as","as-child"]))}}),Jc=O({__name:"ScrollAreaScrollbarX",setup(t){const e=me(),n=Wn(),{forwardRef:o,currentElement:a}=I();J(()=>{a.value&&e.onScrollbarXChange(a.value)});const r=P(()=>n.sizes.value);return(s,i)=>(D(),E(gs,{ref:u(o),"is-horizontal":!0,"data-orientation":"horizontal",style:Ze({bottom:0,left:u(e).dir.value==="rtl"?"var(--reka-scroll-area-corner-width)":0,right:u(e).dir.value==="ltr"?"var(--reka-scroll-area-corner-width)":0,"--reka-scroll-area-thumb-width":r.value?`${u(Hn)(r.value)}px`:void 0}),onOnDragScroll:i[0]||(i[0]=l=>u(n).onDragScroll(l.x))},{default:$(()=>[A(s.$slots,"default")]),_:3},8,["style"]))}}),Gc=O({__name:"ScrollAreaScrollbarY",setup(t){const e=me(),n=Wn(),{forwardRef:o,currentElement:a}=I();J(()=>{a.value&&e.onScrollbarYChange(a.value)});const r=P(()=>n.sizes.value);return(s,i)=>(D(),E(gs,{ref:u(o),"is-horizontal":!1,"data-orientation":"vertical",style:Ze({top:0,right:u(e).dir.value==="ltr"?0:void 0,left:u(e).dir.value==="rtl"?0:void 0,bottom:"var(--reka-scroll-area-corner-height)","--reka-scroll-area-thumb-height":r.value?`${u(Hn)(r.value)}px`:void 0}),onOnDragScroll:i[0]||(i[0]=l=>u(n).onDragScroll(l.y))},{default:$(()=>[A(s.$slots,"default")]),_:3},8,["style"]))}}),[Wn,Xc]=q("ScrollAreaScrollbarVisible"),ra=O({__name:"ScrollAreaScrollbarVisible",setup(t){const e=me(),n=Kn(),{forwardRef:o}=I(),a=x({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),r=P(()=>{const v=ys(a.value.viewport,a.value.content);return v>0&&v<1}),s=x(),i=x(0);function l(v,b){if(h.value){const g=e.viewport.value.scrollLeft+v.deltaY;e.viewport.value.scrollLeft=g,Xa(g,b)&&v.preventDefault()}else{const g=e.viewport.value.scrollTop+v.deltaY;e.viewport.value.scrollTop=g,Xa(g,b)&&v.preventDefault()}}function c(v,b){h.value?i.value=b.x:i.value=b.y}function d(v){i.value=0}function f(v){a.value=v}function p(v,b){return qc(v,i.value,a.value,b)}const h=P(()=>n.isHorizontal.value);function y(v){h.value?e.viewport.value.scrollLeft=p(v,e.dir.value):e.viewport.value.scrollTop=p(v)}function w(){if(h.value){if(e.viewport.value&&s.value){const v=e.viewport.value.scrollLeft,b=Ga(v,a.value,e.dir.value);s.value.style.transform=`translate3d(${b}px, 0, 0)`}}else if(e.viewport.value&&s.value){const v=e.viewport.value.scrollTop,b=Ga(v,a.value);s.value.style.transform=`translate3d(0, ${b}px, 0)`}}function m(v){s.value=v}return Xc({sizes:a,hasThumb:r,handleWheelScroll:l,handleThumbDown:c,handleThumbUp:d,handleSizeChange:f,onThumbPositionChange:w,onThumbChange:m,onDragScroll:y}),(v,b)=>h.value?(D(),E(Jc,R({key:0},v.$attrs,{ref:u(o)}),{default:$(()=>[A(v.$slots,"default")]),_:3},16)):(D(),E(Gc,R({key:1},v.$attrs,{ref:u(o)}),{default:$(()=>[A(v.$slots,"default")]),_:3},16))}}),bs=O({__name:"ScrollAreaScrollbarAuto",props:{forceMount:{type:Boolean}},setup(t){const e=me(),n=Kn(),{forwardRef:o}=I(),a=x(!1),r=zr(()=>{if(e.viewport.value){const s=e.viewport.value.offsetWidth<e.viewport.value.scrollWidth,i=e.viewport.value.offsetHeight<e.viewport.value.scrollHeight;a.value=n.isHorizontal.value?s:i}},10);return J(()=>r()),st(e.viewport,r),st(e.content,r),(s,i)=>(D(),E(u(Me),{present:s.forceMount||a.value},{default:$(()=>[H(ra,R(s.$attrs,{ref:u(o),"data-state":a.value?"visible":"hidden"}),{default:$(()=>[A(s.$slots,"default")]),_:3},16,["data-state"])]),_:3},8,["present"]))}}),Qc=O({inheritAttrs:!1,__name:"ScrollAreaScrollbarHover",props:{forceMount:{type:Boolean}},setup(t){const e=me(),{forwardRef:n}=I();let o;const a=x(!1);function r(){window.clearTimeout(o),a.value=!0}function s(){o=window.setTimeout(()=>{a.value=!1},e.scrollHideDelay.value)}return J(()=>{const i=e.scrollArea.value;i&&(i.addEventListener("pointerenter",r),i.addEventListener("pointerleave",s))}),Qe(()=>{const i=e.scrollArea.value;i&&(window.clearTimeout(o),i.removeEventListener("pointerenter",r),i.removeEventListener("pointerleave",s))}),(i,l)=>(D(),E(u(Me),{present:i.forceMount||a.value},{default:$(()=>[H(bs,R(i.$attrs,{ref:u(n),"data-state":a.value?"visible":"hidden"}),{default:$(()=>[A(i.$slots,"default")]),_:3},16,["data-state"])]),_:3},8,["present"]))}}),Zc=O({__name:"ScrollAreaScrollbarScroll",props:{forceMount:{type:Boolean}},setup(t){const e=me(),n=Kn(),{forwardRef:o}=I(),{state:a,dispatch:r}=Gr("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});ne(i=>{if(a.value==="idle"){const l=window.setTimeout(()=>r("HIDE"),e.scrollHideDelay.value);i(()=>{window.clearTimeout(l)})}});const s=zr(()=>r("SCROLL_END"),100);return ne(i=>{const l=e.viewport.value,c=n.isHorizontal.value?"scrollLeft":"scrollTop";if(l){let d=l[c];const f=()=>{const p=l[c];d!==p&&(r("SCROLL"),s()),d=p};l.addEventListener("scroll",f),i(()=>{l.removeEventListener("scroll",f)})}}),(i,l)=>(D(),E(u(Me),{present:i.forceMount||u(a)!=="hidden"},{default:$(()=>[H(ra,R(i.$attrs,{ref:u(o)}),{default:$(()=>[A(i.$slots,"default")]),_:3},16)]),_:3},8,["present"]))}}),[Kn,ed]=q("ScrollAreaScrollbar"),gf=O({inheritAttrs:!1,__name:"ScrollAreaScrollbar",props:{orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"div"}},setup(t){const e=t,{forwardRef:n}=I(),o=me(),a=P(()=>e.orientation==="horizontal");z(a,()=>{a.value?o.onScrollbarXEnabledChange(!0):o.onScrollbarYEnabledChange(!0)},{immediate:!0}),Qe(()=>{o.onScrollbarXEnabledChange(!1),o.onScrollbarYEnabledChange(!1)});const{orientation:r,forceMount:s,asChild:i,as:l}=ee(e);return ed({orientation:r,forceMount:s,isHorizontal:a,as:l,asChild:i}),(c,d)=>u(o).type.value==="hover"?(D(),E(Qc,R({key:0},c.$attrs,{ref:u(n),"force-mount":u(s)}),{default:$(()=>[A(c.$slots,"default")]),_:3},16,["force-mount"])):u(o).type.value==="scroll"?(D(),E(Zc,R({key:1},c.$attrs,{ref:u(n),"force-mount":u(s)}),{default:$(()=>[A(c.$slots,"default")]),_:3},16,["force-mount"])):u(o).type.value==="auto"?(D(),E(bs,R({key:2},c.$attrs,{ref:u(n),"force-mount":u(s)}),{default:$(()=>[A(c.$slots,"default")]),_:3},16,["force-mount"])):u(o).type.value==="always"?(D(),E(ra,R({key:3},c.$attrs,{ref:u(n),"data-state":"visible"}),{default:$(()=>[A(c.$slots,"default")]),_:3},16)):ie("",!0)}}),bf=O({__name:"ScrollAreaThumb",props:{asChild:{type:Boolean},as:{}},setup(t){const e=t,n=me(),o=Wn();function a(p){const y=p.target.getBoundingClientRect(),w=p.clientX-y.left,m=p.clientY-y.top;o.handleThumbDown(p,{x:w,y:m})}function r(p){o.handleThumbUp(p)}const{forwardRef:s,currentElement:i}=I(),l=x(),c=P(()=>n.viewport.value);function d(){if(!l.value){const p=jc(c.value,o.onThumbPositionChange);l.value=p,o.onThumbPositionChange()}}const f=P(()=>o.sizes.value);return Hl(f,()=>{o.onThumbChange(i.value),c.value&&(o.onThumbPositionChange(),c.value.addEventListener("scroll",d))}),Qe(()=>{var p;c.value.removeEventListener("scroll",d),(p=n.viewport.value)==null||p.removeEventListener("scroll",d)}),(p,h)=>(D(),E(u(L),{ref:u(s),"data-state":u(o).hasThumb?"visible":"hidden",style:{width:"var(--reka-scroll-area-thumb-width)",height:"var(--reka-scroll-area-thumb-height)"},"as-child":e.asChild,as:p.as,onPointerdown:a,onPointerup:r},{default:$(()=>[A(p.$slots,"default")]),_:3},8,["data-state","as-child","as"]))}}),wf=O({inheritAttrs:!1,__name:"ScrollAreaViewport",props:{nonce:{},asChild:{type:Boolean},as:{}},setup(t,{expose:e}){const n=t,{nonce:o}=ee(n),a=ds(o),r=me(),s=x();J(()=>{r.onViewportChange(s.value),r.onContentChange(l.value)}),e({viewportElement:s});const{forwardRef:i,currentElement:l}=I();return(c,d)=>(D(),he(Ht,null,[vn("div",R({ref_key:"viewportElement",ref:s,"data-reka-scroll-area-viewport":"",style:{overflowX:u(r).scrollbarXEnabled.value?"scroll":"hidden",overflowY:u(r).scrollbarYEnabled.value?"scroll":"hidden"}},c.$attrs,{tabindex:0}),[H(u(L),{ref:u(i),style:Ze({minWidth:u(r).scrollbarXEnabled.value?"fit-content":void 0}),"as-child":n.asChild,as:c.as},{default:$(()=>[A(c.$slots,"default")]),_:3},8,["style","as-child","as"])],16),H(u(L),{as:"style",nonce:u(a)},{default:$(()=>d[0]||(d[0]=[Ke(" /* Hide scrollbars cross-browser and enable momentum scroll for touch devices */ [data-reka-scroll-area-viewport] { scrollbar-width:none; -ms-overflow-style:none; -webkit-overflow-scrolling:touch; } [data-reka-scroll-area-viewport]::-webkit-scrollbar { display:none; } ")])),_:1},8,["nonce"])],64))}}),td=[" ","Enter","ArrowUp","ArrowDown"],nd=[" ","Enter"],be=10;function On(t,e,n){return t===void 0?!1:Array.isArray(t)?t.some(o=>Bo(o,e,n)):Bo(t,e,n)}function Bo(t,e,n){return t===void 0||e===void 0?!1:typeof t=="string"?t===e:typeof n=="function"?n(t,e):typeof n=="string"?(t==null?void 0:t[n])===(e==null?void 0:e[n]):En(t,e)}function od(t){return t==null||t===""||Array.isArray(t)&&t.length===0}const ad=O({__name:"BubbleSelect",props:{autocomplete:{},autofocus:{type:Boolean},disabled:{type:Boolean},form:{},multiple:{type:Boolean},name:{},required:{type:Boolean},size:{},value:{}},setup(t){const e=t,n=x();return z(()=>e.value,(o,a)=>{const r=window.HTMLSelectElement.prototype,i=Object.getOwnPropertyDescriptor(r,"value").set;if(o!==a&&i&&n.value){const l=new Event("change",{bubbles:!0});i.call(n.value,o),n.value.dispatchEvent(l)}}),(o,a)=>(D(),E(u(Qo),{"as-child":""},{default:$(()=>[vn("select",R({ref_key:"selectElement",ref:n},e),[A(o.$slots,"default")],16)]),_:3}))}}),rd={key:0,value:""},[dt,ws]=q("SelectRoot"),$f=O({inheritAttrs:!1,__name:"SelectRoot",props:{open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean},defaultValue:{},modelValue:{default:void 0},by:{},dir:{},multiple:{type:Boolean},autocomplete:{},disabled:{type:Boolean},name:{},required:{type:Boolean}},emits:["update:modelValue","update:open"],setup(t,{emit:e}){const n=t,o=e,{required:a,disabled:r,multiple:s,dir:i}=ee(n),l=fe(n,"modelValue",o,{defaultValue:n.defaultValue??(s.value?[]:void 0),passive:n.modelValue===void 0,deep:!0}),c=fe(n,"open",o,{defaultValue:n.defaultOpen,passive:n.open===void 0}),d=x(),f=x(),p=x({x:0,y:0}),h=P(()=>{var g;return s.value&&Array.isArray(l.value)?((g=l.value)==null?void 0:g.length)===0:Nt(l.value)});ze({isProvider:!0});const y=ct(i),w=ta(d),m=x(new Set),v=P(()=>Array.from(m.value).map(g=>g.value).join(";"));function b(g){if(s.value){const C=Array.isArray(l.value)?[...l.value]:[],_=C.findIndex(M=>Bo(M,g,n.by));_===-1?C.push(g):C.splice(_,1),l.value=[...C]}else l.value=g}return ws({triggerElement:d,onTriggerChange:g=>{d.value=g},valueElement:f,onValueElementChange:g=>{f.value=g},contentId:"",modelValue:l,onValueChange:b,by:n.by,open:c,multiple:s,required:a,onOpenChange:g=>{c.value=g},dir:y,triggerPointerDownPosRef:p,disabled:r,isEmptyModelValue:h,optionsSet:m,onOptionAdd:g=>m.value.add(g),onOptionRemove:g=>m.value.delete(g)}),(g,C)=>(D(),E(u(Ln),null,{default:$(()=>[A(g.$slots,"default",{modelValue:u(l),open:u(c)}),u(w)?(D(),E(ad,{key:v.value,"aria-hidden":"true",tabindex:"-1",multiple:u(s),required:u(a),name:g.name,autocomplete:g.autocomplete,disabled:u(r),value:u(l)},{default:$(()=>[u(Nt)(u(l))?(D(),he("option",rd)):ie("",!0),(D(!0),he(Ht,null,lr(Array.from(m.value),_=>(D(),he("option",R({key:_.value??"",ref_for:!0},_),null,16))),128))]),_:1},8,["multiple","required","name","autocomplete","disabled","value"])):ie("",!0)]),_:3}))}}),[sa,sd]=q("SelectItemAlignedPosition"),id=O({inheritAttrs:!1,__name:"SelectItemAlignedPosition",props:{asChild:{type:Boolean},as:{}},emits:["placed"],setup(t,{emit:e}){const n=t,o=e,{getItems:a}=ze(),r=dt(),s=ft(),i=x(!1),l=x(!0),c=x(),{forwardRef:d,currentElement:f}=I(),{viewport:p,selectedItem:h,selectedItemText:y,focusSelectedItem:w}=s;function m(){if(r.triggerElement.value&&r.valueElement.value&&c.value&&f.value&&(p!=null&&p.value)&&(h!=null&&h.value)&&(y!=null&&y.value)){const g=r.triggerElement.value.getBoundingClientRect(),C=f.value.getBoundingClientRect(),_=r.valueElement.value.getBoundingClientRect(),M=y.value.getBoundingClientRect();if(r.dir.value!=="rtl"){const ye=M.left-C.left,ge=_.left-ye,De=g.left-ge,Te=g.width+De,Bt=Math.max(Te,C.width),Y=window.innerWidth-be,Ie=ko(ge,be,Math.max(be,Y-Bt));c.value.style.minWidth=`${Te}px`,c.value.style.left=`${Ie}px`}else{const ye=C.right-M.right,ge=window.innerWidth-_.right-ye,De=window.innerWidth-g.right-ge,Te=g.width+De,Bt=Math.max(Te,C.width),Y=window.innerWidth-be,Ie=ko(ge,be,Math.max(be,Y-Bt));c.value.style.minWidth=`${Te}px`,c.value.style.right=`${Ie}px`}const B=a().map(ye=>ye.ref),F=window.innerHeight-be*2,S=p.value.scrollHeight,T=window.getComputedStyle(f.value),k=Number.parseInt(T.borderTopWidth,10),V=Number.parseInt(T.paddingTop,10),N=Number.parseInt(T.borderBottomWidth,10),K=Number.parseInt(T.paddingBottom,10),U=k+V+S+K+N,Z=Math.min(h.value.offsetHeight*5,U),j=window.getComputedStyle(p.value),ae=Number.parseInt(j.paddingTop,10),le=Number.parseInt(j.paddingBottom,10),Be=g.top+g.height/2-be,zn=F-Be,kt=h.value.offsetHeight/2,Zt=h.value.offsetTop+kt,pt=k+V+Zt,jn=U-pt;if(pt<=Be){const ye=h.value===B[B.length-1];c.value.style.bottom="0px";const ge=f.value.clientHeight-p.value.offsetTop-p.value.offsetHeight,De=Math.max(zn,kt+(ye?le:0)+ge+N),Te=pt+De;c.value.style.height=`${Te}px`}else{const ye=h.value===B[0];c.value.style.top="0px";const De=Math.max(Be,k+p.value.offsetTop+(ye?ae:0)+kt)+jn;c.value.style.height=`${De}px`,p.value.scrollTop=pt-Be+p.value.offsetTop}c.value.style.margin=`${be}px 0`,c.value.style.minHeight=`${Z}px`,c.value.style.maxHeight=`${F}px`,o("placed"),requestAnimationFrame(()=>i.value=!0)}}const v=x("");J(async()=>{await G(),m(),f.value&&(v.value=window.getComputedStyle(f.value).zIndex)});function b(g){g&&l.value===!0&&(m(),w==null||w(),l.value=!1)}return st(r.triggerElement,()=>{m()}),sd({contentWrapper:c,shouldExpandOnScrollRef:i,onScrollButtonChange:b}),(g,C)=>(D(),he("div",{ref_key:"contentWrapperElement",ref:c,style:Ze({display:"flex",flexDirection:"column",position:"fixed",zIndex:v.value})},[H(u(L),R({ref:u(d),style:{boxSizing:"border-box",maxHeight:"100%"}},{...g.$attrs,...n}),{default:$(()=>[A(g.$slots,"default")]),_:3},16)],4))}}),ld=O({__name:"SelectPopperPosition",props:{side:{},sideOffset:{},align:{default:"start"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{default:be},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},setup(t){const n=Xt(t);return(o,a)=>(D(),E(u(Nn),R(u(n),{style:{boxSizing:"border-box","--reka-select-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-select-content-available-width":"var(--reka-popper-available-width)","--reka-select-content-available-height":"var(--reka-popper-available-height)","--reka-select-trigger-width":"var(--reka-popper-anchor-width)","--reka-select-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:$(()=>[A(o.$slots,"default")]),_:3},16))}}),ud={onViewportChange:()=>{},itemTextRefCallback:()=>{},itemRefCallback:()=>{}},[ft,$s]=q("SelectContent"),cd=O({__name:"SelectContentImpl",props:{position:{default:"item-aligned"},bodyLock:{type:Boolean,default:!0},side:{},sideOffset:{},align:{default:"start"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(t,{emit:e}){const n=t,o=e,a=dt();aa(),Fn(n.bodyLock);const{CollectionSlot:r,getItems:s}=ze(),i=x();Rn(i);const{search:l,handleTypeaheadSearch:c}=na(),d=x(),f=x(),p=x(),h=x(!1),y=x(!1),w=x(!1);function m(){f.value&&i.value&&xo([f.value,i.value])}z(h,()=>{m()});const{onOpenChange:v,triggerPointerDownPosRef:b}=a;ne(M=>{if(!i.value)return;let B={x:0,y:0};const F=T=>{var k,V;B={x:Math.abs(Math.round(T.pageX)-(((k=b.value)==null?void 0:k.x)??0)),y:Math.abs(Math.round(T.pageY)-(((V=b.value)==null?void 0:V.y)??0))}},S=T=>{var k;T.pointerType!=="touch"&&(B.x<=10&&B.y<=10?T.preventDefault():(k=i.value)!=null&&k.contains(T.target)||v(!1),document.removeEventListener("pointermove",F),b.value=null)};b.value!==null&&(document.addEventListener("pointermove",F),document.addEventListener("pointerup",S,{capture:!0,once:!0})),M(()=>{document.removeEventListener("pointermove",F),document.removeEventListener("pointerup",S,{capture:!0})})});function g(M){const B=M.ctrlKey||M.altKey||M.metaKey;if(M.key==="Tab"&&M.preventDefault(),!B&&M.key.length===1&&c(M.key,s()),["ArrowUp","ArrowDown","Home","End"].includes(M.key)){let S=[...s().map(T=>T.ref)];if(["ArrowUp","End"].includes(M.key)&&(S=S.slice().reverse()),["ArrowUp","ArrowDown"].includes(M.key)){const T=M.target,k=S.indexOf(T);S=S.slice(k+1)}setTimeout(()=>xo(S)),M.preventDefault()}}const C=P(()=>n.position==="popper"?n:{}),_=Xt(C.value);return $s({content:i,viewport:d,onViewportChange:M=>{d.value=M},itemRefCallback:(M,B,F)=>{const S=!y.value&&!F,T=On(a.modelValue.value,B,a.by);if(a.multiple.value){if(w.value)return;(T||S)&&(f.value=M,T&&(w.value=!0))}else(T||S)&&(f.value=M);S&&(y.value=!0)},selectedItem:f,selectedItemText:p,onItemLeave:()=>{var M;(M=i.value)==null||M.focus()},itemTextRefCallback:(M,B,F)=>{const S=!y.value&&!F;(On(a.modelValue.value,B,a.by)||S)&&(p.value=M)},focusSelectedItem:m,position:n.position,isPositioned:h,searchRef:l}),(M,B)=>(D(),E(u(r),null,{default:$(()=>[H(u(In),{"as-child":"",onMountAutoFocus:B[6]||(B[6]=ce(()=>{},["prevent"])),onUnmountAutoFocus:B[7]||(B[7]=F=>{var S;o("closeAutoFocus",F),!F.defaultPrevented&&((S=u(a).triggerElement.value)==null||S.focus({preventScroll:!0}),F.preventDefault())})},{default:$(()=>[H(u(Jt),{"as-child":"","disable-outside-pointer-events":"",onFocusOutside:B[2]||(B[2]=ce(()=>{},["prevent"])),onDismiss:B[3]||(B[3]=F=>u(a).onOpenChange(!1)),onEscapeKeyDown:B[4]||(B[4]=F=>o("escapeKeyDown",F)),onPointerDownOutside:B[5]||(B[5]=F=>o("pointerDownOutside",F))},{default:$(()=>[(D(),E(xn(M.position==="popper"?ld:id),R({...M.$attrs,...u(_)},{id:u(a).contentId,ref:F=>{i.value=u(xe)(F)},role:"listbox","data-state":u(a).open.value?"open":"closed",dir:u(a).dir.value,style:{display:"flex",flexDirection:"column",outline:"none"},onContextmenu:B[0]||(B[0]=ce(()=>{},["prevent"])),onPlaced:B[1]||(B[1]=F=>h.value=!0),onKeydown:g}),{default:$(()=>[A(M.$slots,"default")]),_:3},16,["id","data-state","dir","onKeydown"]))]),_:3})]),_:3})]),_:3}))}}),dd=O({inheritAttrs:!1,__name:"SelectProvider",props:{context:{}},setup(t){return ws(t.context),$s(ud),(n,o)=>A(n.$slots,"default")}}),fd={key:1},Cf=O({inheritAttrs:!1,__name:"SelectContent",props:{forceMount:{type:Boolean},position:{},bodyLock:{type:Boolean},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{}},emits:["closeAutoFocus","escapeKeyDown","pointerDownOutside"],setup(t,{emit:e}){const n=t,a=Ye(n,e),r=dt(),s=x();J(()=>{s.value=new DocumentFragment});const i=x(),l=P(()=>n.forceMount||r.open.value);return(c,d)=>{var f;return l.value?(D(),E(u(Me),{key:0,ref_key:"presenceRef",ref:i,present:!0},{default:$(()=>[H(cd,X(oe({...u(a),...c.$attrs})),{default:$(()=>[A(c.$slots,"default")]),_:3},16)]),_:3},512)):!((f=i.value)!=null&&f.present)&&s.value?(D(),he("div",fd,[(D(),E(ir,{to:s.value},[H(dd,{context:u(r)},{default:$(()=>[A(c.$slots,"default")]),_:3},8,["context"])],8,["to"]))])):ie("",!0)}}}),Df=O({__name:"SelectIcon",props:{asChild:{type:Boolean},as:{default:"span"}},setup(t){return(e,n)=>(D(),E(u(L),{"aria-hidden":"true",as:e.as,"as-child":e.asChild},{default:$(()=>[A(e.$slots,"default",{},()=>[n[0]||(n[0]=Ke("▼"))])]),_:3},8,["as","as-child"]))}}),[Cs,pd]=q("SelectItem"),Ef=O({__name:"SelectItem",props:{value:{},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(t,{emit:e}){const n=t,o=e,{disabled:a}=ee(n),r=dt(),s=ft(),{forwardRef:i,currentElement:l}=I(),{CollectionItem:c}=ze(),d=P(()=>{var C;return On((C=r.modelValue)==null?void 0:C.value,n.value,r.by)}),f=x(!1),p=x(n.textValue??""),h=ve(void 0,"reka-select-item-text"),y="select.select";async function w(C){if(C.defaultPrevented)return;const _={originalEvent:C,value:n.value};Zo(y,m,_)}async function m(C){await G(),o("select",C),!C.defaultPrevented&&(a.value||(r.onValueChange(n.value),r.multiple.value||r.onOpenChange(!1)))}async function v(C){var _,M;await G(),!C.defaultPrevented&&(a.value?(_=s.onItemLeave)==null||_.call(s):(M=C.currentTarget)==null||M.focus({preventScroll:!0}))}async function b(C){var _;await G(),!C.defaultPrevented&&C.currentTarget===re()&&((_=s.onItemLeave)==null||_.call(s))}async function g(C){var M;await G(),!(C.defaultPrevented||((M=s.searchRef)==null?void 0:M.value)!==""&&C.key===" ")&&(nd.includes(C.key)&&w(C),C.key===" "&&C.preventDefault())}if(n.value==="")throw new Error("A <SelectItem /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return J(()=>{l.value&&s.itemRefCallback(l.value,n.value,n.disabled)}),pd({value:n.value,disabled:a,textId:h,isSelected:d,onItemTextChange:C=>{p.value=((p.value||(C==null?void 0:C.textContent))??"").trim()}}),(C,_)=>(D(),E(u(c),{value:{textValue:p.value}},{default:$(()=>[H(u(L),{ref:u(i),role:"option","aria-labelledby":u(h),"data-highlighted":f.value?"":void 0,"aria-selected":d.value,"data-state":d.value?"checked":"unchecked","aria-disabled":u(a)||void 0,"data-disabled":u(a)?"":void 0,tabindex:u(a)?void 0:-1,as:C.as,"as-child":C.asChild,onFocus:_[0]||(_[0]=M=>f.value=!0),onBlur:_[1]||(_[1]=M=>f.value=!1),onPointerup:w,onPointerdown:_[2]||(_[2]=M=>{M.currentTarget.focus({preventScroll:!0})}),onTouchend:_[3]||(_[3]=ce(()=>{},["prevent","stop"])),onPointermove:v,onPointerleave:b,onKeydown:g},{default:$(()=>[A(C.$slots,"default")]),_:3},8,["aria-labelledby","data-highlighted","aria-selected","data-state","aria-disabled","data-disabled","tabindex","as","as-child"])]),_:3},8,["value"]))}}),Af=O({__name:"SelectItemIndicator",props:{asChild:{type:Boolean},as:{default:"span"}},setup(t){const e=t,n=Cs();return(o,a)=>u(n).isSelected.value?(D(),E(u(L),R({key:0,"aria-hidden":"true"},e),{default:$(()=>[A(o.$slots,"default")]),_:3},16)):ie("",!0)}}),Of=O({inheritAttrs:!1,__name:"SelectItemText",props:{asChild:{type:Boolean},as:{default:"span"}},setup(t){const e=t,n=dt(),o=ft(),a=Cs(),{forwardRef:r,currentElement:s}=I(),i=P(()=>{var l,c;return{value:a.value,disabled:a.disabled.value,textContent:((l=s.value)==null?void 0:l.textContent)??((c=a.value)==null?void 0:c.toString())??""}});return J(()=>{s.value&&(a.onItemTextChange(s.value),o.itemTextRefCallback(s.value,a.value,a.disabled.value),n.onOptionAdd(i.value))}),To(()=>{n.onOptionRemove(i.value)}),(l,c)=>(D(),E(u(L),R({id:u(a).textId,ref:u(r)},{...e,...l.$attrs}),{default:$(()=>[A(l.$slots,"default")]),_:3},16,["id"]))}}),Sf=O({__name:"SelectPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const e=t;return(n,o)=>(D(),E(u(Gt),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),Ds=O({__name:"SelectScrollButtonImpl",emits:["autoScroll"],setup(t,{emit:e}){const n=e,{getItems:o}=ze(),a=ft(),r=x(null);function s(){r.value!==null&&(window.clearInterval(r.value),r.value=null)}ne(()=>{const c=o().map(d=>d.ref).find(d=>d===re());c==null||c.scrollIntoView({block:"nearest"})});function i(){r.value===null&&(r.value=window.setInterval(()=>{n("autoScroll")},50))}function l(){var c;(c=a.onItemLeave)==null||c.call(a),r.value===null&&(r.value=window.setInterval(()=>{n("autoScroll")},50))}return To(()=>s()),(c,d)=>{var f;return D(),E(u(L),R({"aria-hidden":"true",style:{flexShrink:0}},(f=c.$parent)==null?void 0:f.$props,{onPointerdown:i,onPointermove:l,onPointerleave:d[0]||(d[0]=()=>{s()})}),{default:$(()=>[A(c.$slots,"default")]),_:3},16)}}}),xf=O({__name:"SelectScrollDownButton",props:{asChild:{type:Boolean},as:{}},setup(t){const e=ft(),n=e.position==="item-aligned"?sa():void 0,{forwardRef:o,currentElement:a}=I(),r=x(!1);return ne(s=>{var i,l;if((i=e.viewport)!=null&&i.value&&((l=e.isPositioned)!=null&&l.value)){let c=function(){const f=d.scrollHeight-d.clientHeight;r.value=Math.ceil(d.scrollTop)<f};const d=e.viewport.value;c(),d.addEventListener("scroll",c),s(()=>d.removeEventListener("scroll",c))}}),z(a,()=>{a.value&&(n==null||n.onScrollButtonChange(a.value))}),(s,i)=>r.value?(D(),E(Ds,{key:0,ref:u(o),onAutoScroll:i[0]||(i[0]=()=>{const{viewport:l,selectedItem:c}=u(e);l!=null&&l.value&&(c!=null&&c.value)&&(l.value.scrollTop=l.value.scrollTop+c.value.offsetHeight)})},{default:$(()=>[A(s.$slots,"default")]),_:3},512)):ie("",!0)}}),Pf=O({__name:"SelectScrollUpButton",props:{asChild:{type:Boolean},as:{}},setup(t){const e=ft(),n=e.position==="item-aligned"?sa():void 0,{forwardRef:o,currentElement:a}=I(),r=x(!1);return ne(s=>{var i,l;if((i=e.viewport)!=null&&i.value&&((l=e.isPositioned)!=null&&l.value)){let c=function(){r.value=d.scrollTop>0};const d=e.viewport.value;c(),d.addEventListener("scroll",c),s(()=>d.removeEventListener("scroll",c))}}),z(a,()=>{a.value&&(n==null||n.onScrollButtonChange(a.value))}),(s,i)=>r.value?(D(),E(Ds,{key:0,ref:u(o),onAutoScroll:i[0]||(i[0]=()=>{const{viewport:l,selectedItem:c}=u(e);l!=null&&l.value&&(c!=null&&c.value)&&(l.value.scrollTop=l.value.scrollTop-c.value.offsetHeight)})},{default:$(()=>[A(s.$slots,"default")]),_:3},512)):ie("",!0)}}),_f=O({__name:"SelectTrigger",props:{disabled:{type:Boolean},reference:{},asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t,n=dt(),{forwardRef:o,currentElement:a}=I(),r=P(()=>{var p;return((p=n.disabled)==null?void 0:p.value)||e.disabled});n.contentId||(n.contentId=ve(void 0,"reka-select-content")),J(()=>{n.onTriggerChange(a.value)});const{getItems:s}=ze(),{search:i,handleTypeaheadSearch:l,resetTypeahead:c}=na();function d(){r.value||(n.onOpenChange(!0),c())}function f(p){d(),n.triggerPointerDownPosRef.value={x:Math.round(p.pageX),y:Math.round(p.pageY)}}return(p,h)=>(D(),E(u(Vn),{"as-child":"",reference:p.reference},{default:$(()=>{var y,w,m,v;return[H(u(L),{ref:u(o),role:"combobox",type:p.as==="button"?"button":void 0,"aria-controls":u(n).contentId,"aria-expanded":u(n).open.value||!1,"aria-required":(y=u(n).required)==null?void 0:y.value,"aria-autocomplete":"none",disabled:r.value,dir:(w=u(n))==null?void 0:w.dir.value,"data-state":(m=u(n))!=null&&m.open.value?"open":"closed","data-disabled":r.value?"":void 0,"data-placeholder":u(od)((v=u(n).modelValue)==null?void 0:v.value)?"":void 0,"as-child":p.asChild,as:p.as,onClick:h[0]||(h[0]=b=>{var g;(g=b==null?void 0:b.currentTarget)==null||g.focus()}),onPointerdown:h[1]||(h[1]=b=>{if(b.pointerType==="touch")return b.preventDefault();const g=b.target;g.hasPointerCapture(b.pointerId)&&g.releasePointerCapture(b.pointerId),b.button===0&&b.ctrlKey===!1&&(f(b),b.preventDefault())}),onPointerup:h[2]||(h[2]=ce(b=>{b.pointerType==="touch"&&f(b)},["prevent"])),onKeydown:h[3]||(h[3]=b=>{const g=u(i)!=="";!(b.ctrlKey||b.altKey||b.metaKey)&&b.key.length===1&&g&&b.key===" "||(u(l)(b.key,u(s)()),u(td).includes(b.key)&&(d(),b.preventDefault()))})},{default:$(()=>[A(p.$slots,"default")]),_:3},8,["type","aria-controls","aria-expanded","aria-required","disabled","dir","data-state","data-disabled","data-placeholder","as-child","as"])]}),_:3},8,["reference"]))}}),Mf=O({__name:"SelectValue",props:{placeholder:{default:""},asChild:{type:Boolean},as:{default:"span"}},setup(t){const e=t,{forwardRef:n,currentElement:o}=I(),a=dt();J(()=>{a.valueElement=o});const r=P(()=>{var d;let i=[];const l=Array.from(a.optionsSet.value),c=f=>l.find(p=>On(f,p.value,a.by));return Array.isArray(a.modelValue.value)?i=a.modelValue.value.map(f=>{var p;return((p=c(f))==null?void 0:p.textContent)??""}):i=[((d=c(a.modelValue.value))==null?void 0:d.textContent)??""],i.filter(Boolean)}),s=P(()=>r.value.length?r.value.join(", "):e.placeholder);return(i,l)=>(D(),E(u(L),{ref:u(n),as:i.as,"as-child":i.asChild,style:{pointerEvents:"none"},"data-placeholder":r.value.length?void 0:e.placeholder},{default:$(()=>[A(i.$slots,"default",{selectedLabel:r.value,modelValue:u(a).modelValue.value},()=>[Ke(Wt(s.value),1)])]),_:3},8,["as","as-child","data-placeholder"]))}}),kf=O({__name:"SelectViewport",props:{nonce:{},asChild:{type:Boolean},as:{}},setup(t){const e=t,{nonce:n}=ee(e),o=ds(n),a=ft(),r=a.position==="item-aligned"?sa():void 0,{forwardRef:s,currentElement:i}=I();J(()=>{a==null||a.onViewportChange(i.value)});const l=x(0);function c(d){const f=d.currentTarget,{shouldExpandOnScrollRef:p,contentWrapper:h}=r??{};if(p!=null&&p.value&&(h!=null&&h.value)){const y=Math.abs(l.value-f.scrollTop);if(y>0){const w=window.innerHeight-be*2,m=Number.parseFloat(h.value.style.minHeight),v=Number.parseFloat(h.value.style.height),b=Math.max(m,v);if(b<w){const g=b+y,C=Math.min(w,g),_=g-C;h.value.style.height=`${C}px`,h.value.style.bottom==="0px"&&(f.scrollTop=_>0?_:0,h.value.style.justifyContent="flex-end")}}}l.value=f.scrollTop}return(d,f)=>(D(),he(Ht,null,[H(u(L),R({ref:u(s),"data-reka-select-viewport":"",role:"presentation"},{...d.$attrs,...e},{style:{position:"relative",flex:1,overflow:"hidden auto"},onScroll:c}),{default:$(()=>[A(d.$slots,"default")]),_:3},16),H(u(L),{as:"style",nonce:u(o)},{default:$(()=>f[0]||(f[0]=[Ke(" /* Hide scrollbars cross-browser and enable momentum scroll for touch devices */ [data-reka-select-viewport] { scrollbar-width:none; -ms-overflow-style: none; -webkit-overflow-scrolling: touch; } [data-reka-select-viewport]::-webkit-scrollbar { display: none; } ")])),_:1},8,["nonce"])],64))}}),hd=O({__name:"BaseSeparator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(t){const e=t,n=["horizontal","vertical"];function o(i){return n.includes(i)}const a=P(()=>o(e.orientation)?e.orientation:"horizontal"),r=P(()=>a.value==="vertical"?e.orientation:void 0),s=P(()=>e.decorative?{role:"none"}:{"aria-orientation":r.value,role:"separator"});return(i,l)=>(D(),E(u(L),R({as:i.as,"as-child":i.asChild,"data-orientation":a.value},s.value),{default:$(()=>[A(i.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),Bf=O({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(t){const e=t;return(n,o)=>(D(),E(hd,X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),[vd,md]=q("SwitchRoot"),Tf=O({__name:"SwitchRoot",props:{defaultValue:{type:Boolean},modelValue:{type:[Boolean,null],default:void 0},disabled:{type:Boolean},id:{},value:{default:"on"},asChild:{type:Boolean},as:{default:"button"},name:{},required:{type:Boolean}},emits:["update:modelValue"],setup(t,{emit:e}){const n=t,o=e,{disabled:a}=ee(n),r=fe(n,"modelValue",o,{defaultValue:n.defaultValue,passive:n.modelValue===void 0});function s(){a.value||(r.value=!r.value)}const{forwardRef:i,currentElement:l}=I(),c=ta(l),d=P(()=>{var f;return n.id&&l.value?(f=document.querySelector(`[for="${n.id}"]`))==null?void 0:f.innerText:void 0});return md({modelValue:r,toggleCheck:s,disabled:a}),(f,p)=>(D(),E(u(L),R(f.$attrs,{id:f.id,ref:u(i),role:"switch",type:f.as==="button"?"button":void 0,value:f.value,"aria-label":f.$attrs["aria-label"]||d.value,"aria-checked":u(r),"aria-required":f.required,"data-state":u(r)?"checked":"unchecked","data-disabled":u(a)?"":void 0,"as-child":f.asChild,as:f.as,disabled:u(a),onClick:s,onKeydown:Ct(ce(s,["prevent"]),["enter"])}),{default:$(()=>[A(f.$slots,"default",{modelValue:u(r)}),u(c)&&f.name?(D(),E(u(ss),{key:0,type:"checkbox",name:f.name,disabled:u(a),required:f.required,value:f.value,checked:!!u(r)},null,8,["name","disabled","required","value","checked"])):ie("",!0)]),_:3},16,["id","type","value","aria-label","aria-checked","aria-required","data-state","data-disabled","as-child","as","disabled","onKeydown"]))}}),If=O({__name:"SwitchThumb",props:{asChild:{type:Boolean},as:{default:"span"}},setup(t){const e=vd();return I(),(n,o)=>{var a;return D(),E(u(L),{"data-state":(a=u(e).modelValue)!=null&&a.value?"checked":"unchecked","data-disabled":u(e).disabled.value?"":void 0,"as-child":n.asChild,as:n.as},{default:$(()=>[A(n.$slots,"default")]),_:3},8,["data-state","data-disabled","as-child","as"])}}});function yd(t,e){return`${t}-trigger-${e}`}function gd(t,e){return`${t}-content-${e}`}const[Es,bd]=q("TabsRoot"),Rf=O({__name:"TabsRoot",props:{defaultValue:{},orientation:{default:"horizontal"},dir:{},activationMode:{default:"automatic"},modelValue:{},unmountOnHide:{type:Boolean,default:!0},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(t,{emit:e}){const n=t,o=e,{orientation:a,unmountOnHide:r,dir:s}=ee(n),i=ct(s);I();const l=fe(n,"modelValue",o,{defaultValue:n.defaultValue,passive:n.modelValue===void 0}),c=x();return bd({modelValue:l,changeModelValue:d=>{l.value=d},orientation:a,dir:i,unmountOnHide:r,activationMode:n.activationMode,baseId:ve(void 0,"reka-tabs"),tabsList:c}),(d,f)=>(D(),E(u(L),{dir:u(i),"data-orientation":u(a),"as-child":d.asChild,as:d.as},{default:$(()=>[A(d.$slots,"default",{modelValue:u(l)})]),_:3},8,["dir","data-orientation","as-child","as"]))}}),Ff=O({__name:"TabsList",props:{loop:{type:Boolean,default:!0},asChild:{type:Boolean},as:{}},setup(t){const e=t,{loop:n}=ee(e),{forwardRef:o,currentElement:a}=I(),r=Es();return r.tabsList=a,(s,i)=>(D(),E(u(rs),{"as-child":"",orientation:u(r).orientation.value,dir:u(r).dir.value,loop:u(n)},{default:$(()=>[H(u(L),{ref:u(o),role:"tablist","as-child":s.asChild,as:s.as,"aria-orientation":u(r).orientation.value},{default:$(()=>[A(s.$slots,"default")]),_:3},8,["as-child","as","aria-orientation"])]),_:3},8,["orientation","dir","loop"]))}}),Lf=O({__name:"TabsTrigger",props:{value:{},disabled:{type:Boolean,default:!1},asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t,{forwardRef:n}=I(),o=Es(),a=P(()=>yd(o.baseId,e.value)),r=P(()=>gd(o.baseId,e.value)),s=P(()=>e.value===o.modelValue.value);return(i,l)=>(D(),E(u(ls),{"as-child":"",focusable:!i.disabled,active:s.value},{default:$(()=>[H(u(L),{id:a.value,ref:u(n),role:"tab",type:i.as==="button"?"button":void 0,as:i.as,"as-child":i.asChild,"aria-selected":s.value?"true":"false","aria-controls":r.value,"data-state":s.value?"active":"inactive",disabled:i.disabled,"data-disabled":i.disabled?"":void 0,"data-orientation":u(o).orientation.value,onMousedown:l[0]||(l[0]=ce(c=>{!i.disabled&&c.ctrlKey===!1?u(o).changeModelValue(i.value):c.preventDefault()},["left"])),onKeydown:l[1]||(l[1]=Ct(c=>u(o).changeModelValue(i.value),["enter","space"])),onFocus:l[2]||(l[2]=()=>{const c=u(o).activationMode!=="manual";!s.value&&!i.disabled&&c&&u(o).changeModelValue(i.value)})},{default:$(()=>[A(i.$slots,"default")]),_:3},8,["id","type","as","as-child","aria-selected","aria-controls","data-state","disabled","data-disabled","data-orientation"])]),_:3},8,["focusable","active"]))}}),Vf=O({__name:"TooltipArrow",props:{width:{default:10},height:{default:5},asChild:{type:Boolean},as:{default:"svg"}},setup(t){const e=t;return I(),(n,o)=>(D(),E(u(vc),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),As="tooltip.open",[ia,wd]=q("TooltipProvider"),Nf=O({inheritAttrs:!1,__name:"TooltipProvider",props:{delayDuration:{default:700},skipDelayDuration:{default:300},disableHoverableContent:{type:Boolean,default:!1},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean,default:!1}},setup(t){const e=t,{delayDuration:n,skipDelayDuration:o,disableHoverableContent:a,disableClosingTrigger:r,ignoreNonKeyboardFocus:s,disabled:i}=ee(e);I();const l=x(!0),c=x(!1),{start:d,stop:f}=jr(()=>{l.value=!0},o,{immediate:!1});return wd({isOpenDelayed:l,delayDuration:n,onOpen(){f(),l.value=!1},onClose(){d()},isPointerInTransitRef:c,disableHoverableContent:a,disableClosingTrigger:r,disabled:i,ignoreNonKeyboardFocus:s}),(p,h)=>A(p.$slots,"default")}}),[Yn,$d]=q("TooltipRoot"),Uf=O({__name:"TooltipRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},delayDuration:{default:void 0},disableHoverableContent:{type:Boolean,default:void 0},disableClosingTrigger:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},ignoreNonKeyboardFocus:{type:Boolean,default:void 0}},emits:["update:open"],setup(t,{emit:e}){const n=t,o=e;I();const a=ia(),r=P(()=>n.disableHoverableContent??a.disableHoverableContent.value),s=P(()=>n.disableClosingTrigger??a.disableClosingTrigger.value),i=P(()=>n.disabled??a.disabled.value),l=P(()=>n.delayDuration??a.delayDuration.value),c=P(()=>n.ignoreNonKeyboardFocus??a.ignoreNonKeyboardFocus.value),d=fe(n,"open",o,{defaultValue:n.defaultOpen,passive:n.open===void 0});z(d,g=>{a.onClose&&(g?(a.onOpen(),document.dispatchEvent(new CustomEvent(As))):a.onClose())});const f=x(!1),p=x(),h=P(()=>d.value?f.value?"delayed-open":"instant-open":"closed"),{start:y,stop:w}=jr(()=>{f.value=!0,d.value=!0},l,{immediate:!1});function m(){w(),f.value=!1,d.value=!0}function v(){w(),d.value=!1}function b(){y()}return $d({contentId:"",open:d,stateAttribute:h,trigger:p,onTriggerChange(g){p.value=g},onTriggerEnter(){a.isOpenDelayed.value?b():m()},onTriggerLeave(){r.value?v():w()},onOpen:m,onClose:v,disableHoverableContent:r,disableClosingTrigger:s,disabled:i,ignoreNonKeyboardFocus:c}),(g,C)=>(D(),E(u(Ln),null,{default:$(()=>[A(g.$slots,"default",{open:u(d)})]),_:3}))}}),Os=O({__name:"TooltipContentImpl",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{default:0},align:{default:"center"},alignOffset:{},avoidCollisions:{type:Boolean,default:!0},collisionBoundary:{default:()=>[]},collisionPadding:{default:0},arrowPadding:{default:0},sticky:{default:"partial"},hideWhenDetached:{type:Boolean,default:!1},positionStrategy:{},updatePositionStrategy:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(t,{emit:e}){const n=t,o=e,a=Yn(),{forwardRef:r}=I(),s=Is(),i=P(()=>{var d;return(d=s.default)==null?void 0:d.call(s,{})}),l=P(()=>{var p;if(n.ariaLabel)return n.ariaLabel;let d="";function f(h){typeof h.children=="string"&&h.type!==tr?d+=h.children:Array.isArray(h.children)&&h.children.forEach(y=>f(y))}return(p=i.value)==null||p.forEach(h=>f(h)),d}),c=P(()=>{const{ariaLabel:d,...f}=n;return f});return J(()=>{xt(window,"scroll",d=>{const f=d.target;f!=null&&f.contains(a.trigger.value)&&a.onClose()}),xt(window,As,a.onClose)}),(d,f)=>(D(),E(u(Jt),{"as-child":"","disable-outside-pointer-events":!1,onEscapeKeyDown:f[0]||(f[0]=p=>o("escapeKeyDown",p)),onPointerDownOutside:f[1]||(f[1]=p=>{var h;u(a).disableClosingTrigger.value&&((h=u(a).trigger.value)!=null&&h.contains(p.target))&&p.preventDefault(),o("pointerDownOutside",p)}),onFocusOutside:f[2]||(f[2]=ce(()=>{},["prevent"])),onDismiss:f[3]||(f[3]=p=>u(a).onClose())},{default:$(()=>[H(u(Nn),R({ref:u(r),"data-state":u(a).stateAttribute.value},{...d.$attrs,...c.value},{style:{"--reka-tooltip-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-tooltip-content-available-width":"var(--reka-popper-available-width)","--reka-tooltip-content-available-height":"var(--reka-popper-available-height)","--reka-tooltip-trigger-width":"var(--reka-popper-anchor-width)","--reka-tooltip-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:$(()=>[A(d.$slots,"default"),H(u(Qo),{id:u(a).contentId,role:"tooltip"},{default:$(()=>[Ke(Wt(l.value),1)]),_:1},8,["id"])]),_:3},16,["data-state"])]),_:3}))}}),Cd=O({__name:"TooltipContentHoverable",props:{ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{}},setup(t){const n=Xt(t),{forwardRef:o,currentElement:a}=I(),{trigger:r,onClose:s}=Yn(),i=ia(),{isPointerInTransit:l,onPointerExit:c}=Lc(r,a);return i.isPointerInTransitRef=l,c(()=>{s()}),(d,f)=>(D(),E(Os,R({ref:u(o)},u(n)),{default:$(()=>[A(d.$slots,"default")]),_:3},16))}}),Hf=O({__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{default:"top"},sideOffset:{},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(t,{emit:e}){const n=t,o=e,a=Yn(),r=Ye(n,o),{forwardRef:s}=I();return(i,l)=>(D(),E(u(Me),{present:i.forceMount||u(a).open.value},{default:$(()=>[(D(),E(xn(u(a).disableHoverableContent.value?Os:Cd),R({ref:u(s)},u(r)),{default:$(()=>[A(i.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Wf=O({__name:"TooltipPortal",props:{to:{},disabled:{type:Boolean},defer:{type:Boolean},forceMount:{type:Boolean}},setup(t){const e=t;return(n,o)=>(D(),E(u(Gt),X(oe(e)),{default:$(()=>[A(n.$slots,"default")]),_:3},16))}}),Kf=O({__name:"TooltipTrigger",props:{reference:{},asChild:{type:Boolean},as:{default:"button"}},setup(t){const e=t,n=Yn(),o=ia();n.contentId||(n.contentId=ve(void 0,"reka-tooltip-content"));const{forwardRef:a,currentElement:r}=I(),s=x(!1),i=x(!1),l=P(()=>n.disabled.value?{}:{click:w,focus:h,pointermove:f,pointerleave:p,pointerdown:d,blur:y});J(()=>{n.onTriggerChange(r.value)});function c(){setTimeout(()=>{s.value=!1},1)}function d(){n.open&&!n.disableClosingTrigger.value&&n.onClose(),s.value=!0,document.addEventListener("pointerup",c,{once:!0})}function f(m){m.pointerType!=="touch"&&!i.value&&!o.isPointerInTransitRef.value&&(n.onTriggerEnter(),i.value=!0)}function p(){n.onTriggerLeave(),i.value=!1}function h(m){var v,b;s.value||n.ignoreNonKeyboardFocus.value&&!((b=(v=m.target).matches)!=null&&b.call(v,":focus-visible"))||n.onOpen()}function y(){n.onClose()}function w(){n.disableClosingTrigger.value||n.onClose()}return(m,v)=>(D(),E(u(Vn),{"as-child":"",reference:m.reference},{default:$(()=>[H(u(L),R({ref:u(a),"aria-describedby":u(n).open.value?u(n).contentId:void 0,"data-state":u(n).stateAttribute.value,as:m.as,"as-child":e.asChild,"data-grace-area-trigger":""},Rs(l.value)),{default:$(()=>[A(m.$slots,"default")]),_:3},16,["aria-describedby","data-state","as","as-child"])]),_:3},8,["reference"]))}});/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qa=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Dd=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,n,o)=>o?o.toUpperCase():n.toLowerCase()),Ed=t=>{const e=Dd(t);return e.charAt(0).toUpperCase()+e.slice(1)},Ad=(...t)=>t.filter((e,n,o)=>!!e&&e.trim()!==""&&o.indexOf(e)===n).join(" ").trim();/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var un={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Od=({size:t,strokeWidth:e=2,absoluteStrokeWidth:n,color:o,iconNode:a,name:r,class:s,...i},{slots:l})=>Ue("svg",{...un,width:t||un.width,height:t||un.height,stroke:o||un.stroke,"stroke-width":n?Number(e)*24/Number(t):e,class:Ad("lucide",...r?[`lucide-${Qa(Ed(r))}-icon`,`lucide-${Qa(r)}`]:["lucide-icon"]),...i},[...a.map(c=>Ue(...c)),...l.default?[l.default()]:[]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=(t,e)=>(n,{slots:o})=>Ue(Od,{...n,iconNode:e,name:t},o);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yf=W("audio-waveform",[["path",{d:"M2 13a2 2 0 0 0 2-2V7a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0V4a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0v-4a2 2 0 0 1 2-2",key:"57tc96"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zf=W("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jf=W("ban",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.9 4.9 14.2 14.2",key:"1m5liu"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qf=W("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jf=W("book-check",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}],["path",{d:"m9 9.5 2 2 4-4",key:"1dth82"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gf=W("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xf=W("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qf=W("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zf=W("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ep=W("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tp=W("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const np=W("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const op=W("command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ap=W("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rp=W("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sp=W("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ip=W("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lp=W("layout-list",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}],["path",{d:"M14 4h7",key:"3xa0d5"}],["path",{d:"M14 9h7",key:"1icrd9"}],["path",{d:"M14 15h7",key:"1mj8o2"}],["path",{d:"M14 20h7",key:"11slyb"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const up=W("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cp=W("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dp=W("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fp=W("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pp=W("power-off",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hp=W("power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vp=W("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mp=W("slash",[["path",{d:"M22 2 2 22",key:"y4kqgn"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yp=W("square-mouse-pointer",[["path",{d:"M12.034 12.681a.498.498 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.943l-3.444 1.068a1 1 0 0 0-.66.66l-1.067 3.443a.5.5 0 0 1-.943.033z",key:"xwnzip"}],["path",{d:"M21 11V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h6",key:"14rsvq"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gp=W("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bp=W("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wp=W("user-pen",[["path",{d:"M11.5 15H7a4 4 0 0 0-4 4v2",key:"15lzij"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"7",r:"4",key:"e45bow"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $p=W("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cp=W("zap-off",[["path",{d:"M10.513 4.856 13.12 2.17a.5.5 0 0 1 .86.46l-1.377 4.317",key:"193nxd"}],["path",{d:"M15.656 10H20a1 1 0 0 1 .78 1.63l-1.72 1.773",key:"27a7lr"}],["path",{d:"M16.273 16.273 10.88 21.83a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14H4a1 1 0 0 1-.78-1.63l4.507-4.643",key:"1e0qe9"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-vue-next v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dp=W("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);export{hr as $,Xt as A,cf as B,ep as C,df as D,ip as E,pf as F,hf as G,np as H,zf as I,qf as J,cp as K,up as L,yp as M,Jf as N,lp as O,L as P,fp as Q,op as R,Oo as S,Yf as T,wp as U,Cp as V,jf as W,$p as X,gf as Y,Dp as Z,vf as _,Yt as a,bf as a0,wf as a1,yf as a2,mf as a3,tf as a4,of as a5,nf as a6,af as a7,Fd as a8,Nd as a9,Mf as aA,vp as aB,mp as aC,Gf as aD,gp as aE,rp as aF,ap as aG,hp as aH,pp as aI,bp as aJ,Zd as aK,ef as aL,If as aM,Tf as aN,Rf as aO,Ff as aP,Lf as aQ,Pd as aR,Ud as aa,Hd as ab,Wd as ac,Kd as ad,Yd as ae,zd as af,jd as ag,qd as ah,Jd as ai,Gd as aj,Xd as ak,Zf as al,$f as am,Cf as an,kf as ao,Sf as ap,Af as aq,Xf as ar,Of as as,Ef as at,xf as au,Qf as av,Pf as aw,tp as ax,Df as ay,_f as az,sp as b,q as c,Nf as d,Bf as e,dp as f,Ye as g,Md as h,Id as i,rf as j,Bd as k,kd as l,Td as m,Rd as n,Uf as o,Wf as p,Hf as q,Vf as r,Kf as s,Ld as t,ve as u,Vd as v,sf as w,ff as x,lf as y,uf as z};
