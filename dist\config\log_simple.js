"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.log = exports.loggers = exports.TaskLogger = exports.APILogger = exports.DatabaseLogger = exports.SessionLogger = exports.ServiceLogger = void 0;
const winston_1 = __importDefault(require("winston"));
const colors = {
    error: '\x1b[31m',
    warn: '\x1b[33m',
    info: '\x1b[36m',
    debug: '\x1b[32m',
    verbose: '\x1b[35m',
    silly: '\x1b[37m'
};
const reset = '\x1b[0m';
const icons = {
    error: '❌',
    warn: '⚠️',
    info: 'ℹ️',
    debug: '🐛',
    verbose: '📝',
    silly: '🔍'
};
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.printf(({ timestamp, level, message, service, stack, ...meta }) => {
    const color = colors[level] || '';
    const icon = icons[level] || '📋';
    const timeStr = `\x1b[90m[${timestamp}]${reset}`;
    const serviceStr = service ? `\x1b[96m[${service}]${reset}` : '';
    const levelStr = `${color}${icon} ${level.toUpperCase()}${reset}`;
    const messageStr = typeof message === 'object' ? JSON.stringify(message, null, 2) : message;
    let logLine = `${timeStr} ${serviceStr} ${levelStr} ${messageStr}`;
    if (Object.keys(meta).length > 0) {
        const metaStr = `\x1b[90m${JSON.stringify(meta, null, 2)}${reset}`;
        logLine += `\n\x1b[90m📊 Meta:${reset} ${metaStr}`;
    }
    if (stack) {
        logLine += `\n\x1b[31m🔥 Stack:${reset} \x1b[31m${stack}${reset}`;
    }
    return logLine;
}));
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
const error_log = new winston_1.default.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    format: fileFormat,
    maxsize: 5242880,
    maxFiles: 5
});
const combined = new winston_1.default.transports.File({
    filename: 'logs/combined.log',
    format: fileFormat,
    maxsize: 5242880,
    maxFiles: 5
});
const console_log = new winston_1.default.transports.Console({
    format: consoleFormat,
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug'
});
const baseLoggerConfig = {
    level: process.env.LOG_LEVEL || 'info',
    transports: [
        error_log,
        combined,
        console_log
    ],
    exceptionHandlers: [
        new winston_1.default.transports.File({ filename: 'logs/exceptions.log' })
    ],
    rejectionHandlers: [
        new winston_1.default.transports.File({ filename: 'logs/rejections.log' })
    ]
};
exports.ServiceLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '🏛️ 图书馆预约服务' }
});
exports.SessionLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '🔐 图书馆预约会话' }
});
exports.DatabaseLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '💾 数据库服务' }
});
exports.APILogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '🌐 API服务' }
});
exports.TaskLogger = winston_1.default.createLogger({
    ...baseLoggerConfig,
    defaultMeta: { service: '⚙️ 任务调度' }
});
exports.loggers = {
    service: exports.ServiceLogger,
    session: exports.SessionLogger,
    database: exports.DatabaseLogger,
    api: exports.APILogger,
    task: exports.TaskLogger
};
exports.log = {
    error: (message, meta) => exports.ServiceLogger.error(message, meta),
    warn: (message, meta) => exports.ServiceLogger.warn(message, meta),
    info: (message, meta) => exports.ServiceLogger.info(message, meta),
    debug: (message, meta) => exports.ServiceLogger.debug(message, meta),
    verbose: (message, meta) => exports.ServiceLogger.verbose(message, meta)
};
console.log('\x1b[36m\n🚀 日志系统已初始化\x1b[0m');
console.log('\x1b[90m📁 日志文件目录: logs/\x1b[0m');
console.log(`\x1b[90m📊 日志级别: ${process.env.LOG_LEVEL || 'info'}\x1b[0m`);
console.log(`\x1b[90m🌍 运行环境: ${process.env.NODE_ENV || 'development'}\x1b[0m`);
console.log('\x1b[36m' + '─'.repeat(50) + '\x1b[0m');
