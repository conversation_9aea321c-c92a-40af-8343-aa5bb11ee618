{"name": "chokidar", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "version": "1.7.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "homepage": "https://github.com/paulmillr/chokidar", "author": "<PERSON> (http://paulmillr.com), <PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/paulmillr/chokidar.git"}, "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "license": "MIT", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "files": ["index.js", "lib/"], "devDependencies": {"chai": "^3.2.0", "coveralls": "^2.11.2", "graceful-fs": "4.1.4", "istanbul": "^0.3.20", "mocha": "^3.0.0", "rimraf": "^2.4.3", "sinon": "^1.10.3", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "dependencies": {"anymatch": "^1.3.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^2.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0"}}