# 智能任务执行器说明

## 概述

新的智能任务执行器完善了任务执行功能，实现了多目标ID的智能重试机制，大大提高了预约成功率。

## 核心功能

### 1. 多目标ID支持

任务现在可以包含多个目标ID，执行器会智能地尝试每个ID直到成功：

```typescript
const task: BookingTask = {
    // ... 其他字段
    target: {
        id: ['room_001', 'room_002', 'room_003', 'room_004'], // 多个目标ID
        date: '2024-01-15',
        start_time: '14:00',
        end_time: '16:00'
    }
};
```

### 2. 智能重试策略

根据目标ID数量自动选择最优的重试策略：

#### 策略A：循环重试（目标ID ≤ 3个）
- **适用场景**：目标ID较少时
- **重试逻辑**：重复循环所有目标ID，直到达到3次总尝试
- **优势**：充分利用有限的目标资源

**示例**：
```
目标ID: ['room_001', 'room_002']
尝试顺序: room_001 → room_002 → room_001 (共3次)
```

#### 策略B：单次尝试（目标ID > 3个）
- **适用场景**：目标ID较多时
- **重试逻辑**：每个目标ID尝试一次，直到成功或全部尝试完
- **优势**：避免重复尝试，提高效率

**示例**：
```
目标ID: ['room_001', 'room_002', 'room_003', 'room_004', 'room_005']
尝试顺序: room_001 → room_002 → room_003 → room_004 (成功后停止)
```

### 3. 详细执行结果记录

每次执行都会记录详细的结果信息：

```typescript
interface ExecutionResult {
    success: boolean;           // 是否成功
    message: string;           // 执行消息
    attemptedIds: string[];    // 尝试过的ID列表
    successfulId?: string;     // 成功的ID
    totalAttempts: number;     // 总尝试次数
}
```

### 4. 数据库状态同步

执行结果自动同步到数据库和任务状态：

```typescript
task.lastExecutionResult = {
    success: result.success,
    message: result.message,
    timestamp: executionTime,
    attemptedIds: result.attemptedIds,    // 新增
    successfulId: result.successfulId,    // 新增
    totalAttempts: result.totalAttempts   // 新增
};
```

## 技术实现

### 1. TaskExecutor 类

新增的智能执行器类，负责处理复杂的重试逻辑：

```typescript
export class TaskExecutor {
    constructor(session: LibrarySession, userId: string);
    
    // 主执行方法
    async executeTask(task: BookingTask): Promise<ExecutionResult>;
    
    // 私有方法
    private async executeWithCyclicRetry(task, maxRetries): Promise<ExecutionResult>;
    private async executeWithSingleAttempt(task): Promise<ExecutionResult>;
    private async attemptBooking(task, targetId): Promise<boolean>;
}
```

### 2. 集成到现有系统

- **SessionPool**: 修改 `executeTaskInternal` 方法使用新执行器
- **TaskPool**: 增强执行结果记录功能
- **数据库**: 扩展 `lastExecutionResult` 字段

### 3. 错误处理和延迟

- **网络延迟**：每次尝试间隔1秒，错误后间隔2秒
- **异常处理**：捕获并记录所有执行异常
- **日志记录**：详细的执行过程日志

## 使用示例

### 1. 创建多目标任务

```typescript
const multiTargetTask: BookingTask = {
    active: true,
    task_id: 'multi_target_001',
    repetive: false,
    type: 'space',
    target: {
        id: ['room_147', 'room_148', 'room_149'], // 多个备选房间
        date: '2024-01-15',
        start_time: '14:00',
        end_time: '16:00'
    },
    execute: {
        day: 1,
        time: '09:00'
    }
};

await sessionPool.addTask('user123', multiTargetTask);
```

### 2. 查看执行结果

```typescript
// 获取任务列表，查看执行结果
const tasks = await fetch('/user/tasks');
const task = tasks.find(t => t.task_id === 'multi_target_001');

console.log('执行结果:', {
    success: task.lastExecutionResult.success,
    message: task.lastExecutionResult.message,
    attemptedIds: task.lastExecutionResult.attemptedIds,
    successfulId: task.lastExecutionResult.successfulId,
    totalAttempts: task.lastExecutionResult.totalAttempts
});
```

## 执行流程图

```mermaid
graph TD
    A[开始执行任务] --> B{目标ID数量}
    B -->|≤ 3个| C[循环重试策略]
    B -->|> 3个| D[单次尝试策略]
    
    C --> E[循环遍历目标ID]
    E --> F[尝试预约]
    F --> G{预约成功?}
    G -->|是| H[记录成功结果]
    G -->|否| I{达到3次尝试?}
    I -->|否| E
    I -->|是| J[记录失败结果]
    
    D --> K[遍历目标ID]
    K --> L[尝试预约]
    L --> M{预约成功?}
    M -->|是| H
    M -->|否| N{还有其他ID?}
    N -->|是| K
    N -->|否| J
    
    H --> O[更新数据库]
    J --> O
    O --> P[结束]
```

## 性能优化

### 1. 智能策略选择
- 根据目标ID数量自动选择最优策略
- 避免不必要的重复尝试

### 2. 早期成功退出
- 一旦预约成功立即停止尝试
- 节省时间和资源

### 3. 合理的延迟控制
- 避免过于频繁的请求
- 错误后适当增加延迟

### 4. 详细的日志记录
- 便于调试和性能分析
- 记录每次尝试的详细信息

## 兼容性

### 向后兼容
- 保持原有API接口不变
- 单目标ID任务正常工作
- 现有任务数据自动适配

### 数据迁移
- 现有任务自动支持新的执行结果格式
- 缺失字段自动补充默认值

## 监控和调试

### 执行统计
```typescript
// 获取任务状态统计
const stats = await fetch('/user/taskstatus');
console.log('成功率:', stats.success / stats.total);
```

### 详细日志
- 每次尝试的目标ID和结果
- 策略选择的原因
- 执行时间和延迟信息

### 错误追踪
- 预约失败的具体原因
- 网络异常和重试情况
- 系统性能指标

## 最佳实践

### 1. 目标ID排序
建议将成功率高的ID放在前面：
```typescript
target: {
    id: ['popular_room', 'backup_room1', 'backup_room2']
}
```

### 2. 合理的目标数量
- 2-5个目标ID通常是最优选择
- 过多目标ID可能导致执行时间过长

### 3. 监控执行结果
定期检查任务执行统计，优化目标ID选择

### 4. 错误处理
对执行失败的任务进行分析，调整策略或目标ID

## 注意事项

1. **网络延迟**：每次尝试都有1-2秒延迟，多目标任务执行时间较长
2. **资源消耗**：多次尝试会增加服务器负载
3. **成功率**：虽然提高了成功率，但不能保证100%成功
4. **时间窗口**：预约时间窗口内的竞争仍然存在
5. **账户限制**：某些账户可能有预约次数限制
