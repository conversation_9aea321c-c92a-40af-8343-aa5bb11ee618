"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskExecutor = void 0;
const log_1 = require("../config/log");
class TaskExecutor {
    constructor(session, userId) {
        this.session = session;
        this.userId = userId;
    }
    async executeTask(task) {
        const targetIds = task.target.id;
        const maxRetries = 3;
        log_1.ServiceLogger.info('开始执行任务', {
            userId: this.userId,
            taskId: task.task_id,
            taskType: task.type,
            targetIds: targetIds,
            targetCount: targetIds.length
        });
        if (targetIds.length <= 3) {
            return await this.executeWithCyclicRetry(task, maxRetries);
        }
        else {
            return await this.executeWithSingleAttempt(task);
        }
    }
    async executeWithCyclicRetry(task, maxRetries) {
        const targetIds = task.target.id;
        const attemptedIds = [];
        let totalAttempts = 0;
        log_1.ServiceLogger.info('使用循环重试策略', {
            userId: this.userId,
            taskId: task.task_id,
            targetIds: targetIds,
            maxRetries: maxRetries
        });
        while (totalAttempts < maxRetries) {
            for (const targetId of targetIds) {
                totalAttempts++;
                attemptedIds.push(targetId);
                log_1.ServiceLogger.info('尝试预约', {
                    userId: this.userId,
                    taskId: task.task_id,
                    targetId: targetId,
                    attempt: totalAttempts,
                    maxRetries: maxRetries
                });
                try {
                    const success = await this.attemptBooking(task, targetId);
                    if (success) {
                        log_1.ServiceLogger.info('预约成功', {
                            userId: this.userId,
                            taskId: task.task_id,
                            targetId: targetId,
                            totalAttempts: totalAttempts
                        });
                        return {
                            success: true,
                            message: `预约成功，使用目标ID: ${targetId}`,
                            attemptedIds: attemptedIds,
                            successfulId: targetId,
                            totalAttempts: totalAttempts
                        };
                    }
                    log_1.ServiceLogger.warn('预约失败，继续尝试下一个', {
                        userId: this.userId,
                        taskId: task.task_id,
                        targetId: targetId,
                        attempt: totalAttempts
                    });
                    if (totalAttempts >= maxRetries) {
                        break;
                    }
                    await this.delay(500);
                }
                catch (error) {
                    log_1.ServiceLogger.error('预约过程中发生错误', {
                        userId: this.userId,
                        taskId: task.task_id,
                        targetId: targetId,
                        error: error.message,
                        attempt: totalAttempts
                    });
                    if (totalAttempts >= maxRetries) {
                        break;
                    }
                    await this.delay(1000);
                }
            }
            if (totalAttempts >= maxRetries) {
                break;
            }
        }
        return {
            success: false,
            message: `所有预约尝试失败，共尝试 ${totalAttempts} 次`,
            attemptedIds: attemptedIds,
            totalAttempts: totalAttempts
        };
    }
    async executeWithSingleAttempt(task) {
        const targetIds = task.target.id;
        const attemptedIds = [];
        let totalAttempts = 0;
        log_1.ServiceLogger.info('使用单次尝试策略', {
            userId: this.userId,
            taskId: task.task_id,
            targetIds: targetIds,
            targetCount: targetIds.length
        });
        for (const targetId of targetIds) {
            totalAttempts++;
            attemptedIds.push(targetId);
            log_1.ServiceLogger.info('尝试预约', {
                userId: this.userId,
                taskId: task.task_id,
                targetId: targetId,
                attempt: totalAttempts,
                totalTargets: targetIds.length
            });
            try {
                const success = await this.attemptBooking(task, targetId);
                if (success) {
                    log_1.ServiceLogger.info('预约成功', {
                        userId: this.userId,
                        taskId: task.task_id,
                        targetId: targetId,
                        totalAttempts: totalAttempts
                    });
                    return {
                        success: true,
                        message: `预约成功，使用目标ID: ${targetId}`,
                        attemptedIds: attemptedIds,
                        successfulId: targetId,
                        totalAttempts: totalAttempts
                    };
                }
                log_1.ServiceLogger.warn('预约失败，继续尝试下一个', {
                    userId: this.userId,
                    taskId: task.task_id,
                    targetId: targetId,
                    attempt: totalAttempts
                });
                await this.delay(500);
            }
            catch (error) {
                log_1.ServiceLogger.error('预约过程中发生错误', {
                    userId: this.userId,
                    taskId: task.task_id,
                    targetId: targetId,
                    error: error.message,
                    attempt: totalAttempts
                });
                await this.delay(1000);
            }
        }
        return {
            success: false,
            message: `所有目标ID预约失败，共尝试 ${totalAttempts} 个目标`,
            attemptedIds: attemptedIds,
            totalAttempts: totalAttempts
        };
    }
    async attemptBooking(task, targetId) {
        switch (task.type) {
            case 'space':
                return await this.session.bookSpace(targetId, task.target.date, task.target.start_time, task.target.end_time);
            case 'seat':
                log_1.ServiceLogger.info('座位预约功能待实现', {
                    userId: this.userId,
                    taskId: task.task_id,
                    targetId: targetId
                });
                return true;
            default:
                throw new Error(`未知的预约类型: ${task.type}`);
        }
    }
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.TaskExecutor = TaskExecutor;
