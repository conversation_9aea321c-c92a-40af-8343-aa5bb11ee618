"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runTaskStatusTests = runTaskStatusTests;
const taskPool_1 = require("./taskPool");
const log_1 = require("../config/log");
async function mockExecuteTask(userId, task) {
    log_1.ServiceLogger.info(`模拟执行任务`, {
        userId,
        taskId: task.task_id,
        taskType: task.type,
        status: task.status
    });
    await new Promise(resolve => setTimeout(resolve, 100));
    return Math.random() > 0.2;
}
async function mockUpdateTaskStatus(userId, task) {
    console.log(`[状态更新] 用户: ${userId}, 任务: ${task.task_id}, 状态: ${task.status}, 时间: ${task.updatedAt}`);
    if (task.lastExecutionResult) {
        console.log(`[执行结果] 成功: ${task.lastExecutionResult.success}, 消息: ${task.lastExecutionResult.message}`);
    }
}
async function testTaskStatusManagement() {
    console.log('\n=== 测试任务状态管理 ===');
    const taskPool = new taskPool_1.TaskPool(mockExecuteTask, mockUpdateTaskStatus, async (userId) => {
        console.log(`模拟预登录用户: ${userId}`);
        return true;
    });
    const now = new Date();
    const executeTime = new Date(now.getTime() + 2000);
    const task = {
        active: true,
        task_id: 'test_status_001',
        repetive: false,
        type: 'space',
        target: {
            id: ['room_147'],
            date: taskPool_1.TimeUtils.dateToString(now),
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 0,
            time: taskPool_1.TimeUtils.minutesToTimeString(executeTime.getHours() * 60 + executeTime.getMinutes())
        }
    };
    console.log('创建任务:', taskPool.createTask('user001', task));
    console.log('等待任务执行...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    const stats = taskPool.getStats();
    console.log('任务池统计:', stats);
    const allTasks = taskPool.getAllTasksInfo();
    console.log('所有任务信息:', allTasks);
    taskPool.shutdown();
    console.log('任务池已关闭');
}
async function testRepetitiveTaskStatus() {
    console.log('\n=== 测试重复任务状态管理 ===');
    const taskPool = new taskPool_1.TaskPool(mockExecuteTask, mockUpdateTaskStatus, async (userId) => {
        console.log(`模拟预登录用户: ${userId}`);
        return true;
    });
    const now = new Date();
    const executeTime = new Date(now.getTime() + 2000);
    const task = {
        active: true,
        task_id: 'test_repeat_status_001',
        repetive: true,
        repeat: [true, true, true, true, true, true, true],
        type: 'space',
        target: {
            id: ['room_147'],
            date: taskPool_1.TimeUtils.dateToString(now),
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 0,
            time: taskPool_1.TimeUtils.minutesToTimeString(executeTime.getHours() * 60 + executeTime.getMinutes())
        }
    };
    console.log('创建重复任务:', taskPool.createTask('user002', task));
    console.log('等待重复任务执行...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    const stats = taskPool.getStats();
    console.log('任务池统计:', stats);
    const allTasks = taskPool.getAllTasksInfo();
    console.log('所有任务信息:', allTasks);
    taskPool.shutdown();
    console.log('任务池已关闭');
}
function testTaskStatusFields() {
    console.log('\n=== 测试任务状态字段 ===');
    const now = new Date().toISOString();
    const task = {
        active: true,
        task_id: 'test_fields_001',
        repetive: false,
        type: 'space',
        target: {
            id: ['room_147'],
            date: '2024-01-15',
            start_time: '14:00',
            end_time: '16:00'
        },
        execute: {
            day: 1,
            time: '09:00'
        },
        status: 'pending',
        createdAt: now,
        updatedAt: now
    };
    console.log('任务初始状态:', {
        task_id: task.task_id,
        status: task.status,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt
    });
    task.status = 'success';
    task.lastExecutionTime = now;
    task.lastExecutionResult = {
        success: true,
        message: '任务执行成功',
        timestamp: now
    };
    task.updatedAt = new Date().toISOString();
    console.log('任务执行后状态:', {
        task_id: task.task_id,
        status: task.status,
        lastExecutionTime: task.lastExecutionTime,
        lastExecutionResult: task.lastExecutionResult,
        updatedAt: task.updatedAt
    });
}
async function runTaskStatusTests() {
    console.log('开始任务状态管理系统测试...\n');
    try {
        testTaskStatusFields();
        await testTaskStatusManagement();
        await testRepetitiveTaskStatus();
        console.log('\n✅ 所有状态管理测试完成！');
    }
    catch (error) {
        console.error('\n❌ 测试过程中出现错误:', error);
    }
}
if (require.main === module) {
    runTaskStatusTests();
}
