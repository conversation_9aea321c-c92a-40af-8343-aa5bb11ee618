# Installation
> `npm install --save @types/pouchdb`

# Summary
This package contains type definitions for pouchdb (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb/index.d.ts)
````ts
/// <reference types='pouchdb-adapter-cordova-sqlite' />
/// <reference types='pouchdb-adapter-fruitdown' />
/// <reference types='pouchdb-adapter-http' />
/// <reference types='pouchdb-adapter-idb' />
/// <reference types='pouchdb-adapter-leveldb' />
/// <reference types='pouchdb-adapter-localstorage' />
/// <reference types='pouchdb-adapter-memory' />
/// <reference types='pouchdb-adapter-node-websql' />
/// <reference types='pouchdb-adapter-websql' />
/// <reference types='pouchdb-browser' />
/// <reference types='pouchdb-core' />
/// <reference types='pouchdb-http' />
/// <reference types='pouchdb-mapreduce' />
/// <reference types='pouchdb-node' />
/// <reference types='pouchdb-replication' />

declare const plugin: PouchDB.Static;
export = plugin;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-adapter-cordova-sqlite](https://npmjs.com/package/@types/pouchdb-adapter-cordova-sqlite), [@types/pouchdb-adapter-fruitdown](https://npmjs.com/package/@types/pouchdb-adapter-fruitdown), [@types/pouchdb-adapter-http](https://npmjs.com/package/@types/pouchdb-adapter-http), [@types/pouchdb-adapter-idb](https://npmjs.com/package/@types/pouchdb-adapter-idb), [@types/pouchdb-adapter-leveldb](https://npmjs.com/package/@types/pouchdb-adapter-leveldb), [@types/pouchdb-adapter-localstorage](https://npmjs.com/package/@types/pouchdb-adapter-localstorage), [@types/pouchdb-adapter-memory](https://npmjs.com/package/@types/pouchdb-adapter-memory), [@types/pouchdb-adapter-node-websql](https://npmjs.com/package/@types/pouchdb-adapter-node-websql), [@types/pouchdb-adapter-websql](https://npmjs.com/package/@types/pouchdb-adapter-websql), [@types/pouchdb-browser](https://npmjs.com/package/@types/pouchdb-browser), [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core), [@types/pouchdb-http](https://npmjs.com/package/@types/pouchdb-http), [@types/pouchdb-mapreduce](https://npmjs.com/package/@types/pouchdb-mapreduce), [@types/pouchdb-node](https://npmjs.com/package/@types/pouchdb-node), [@types/pouchdb-replication](https://npmjs.com/package/@types/pouchdb-replication)

# Credits
These definitions were written by [Andy Brown](https://github.com/AGBrown), [Brian Geppert](https://github.com/geppy), and [Frederico Galvão](https://github.com/fredgalvao).
