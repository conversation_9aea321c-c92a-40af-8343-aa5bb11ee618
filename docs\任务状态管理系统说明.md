# 任务状态管理系统说明

## 概述

本次更新为任务池系统增加了完整的状态管理功能，包括任务状态跟踪、数据库同步、以及完善的API接口。

## 新增功能

### 1. 任务状态管理

#### 状态字段
```typescript
interface BookingTask {
    // 原有字段...
    
    // 新增状态字段
    status?: 'pending' | 'executing' | 'success' | 'failed';
    lastExecutionTime?: string; // ISO 时间字符串
    lastExecutionResult?: {
        success: boolean;
        message?: string;
        timestamp: string;
    };
    createdAt?: string; // 任务创建时间
    updatedAt?: string; // 任务最后更新时间
}
```

#### 状态流转
1. **pending**: 任务创建后的初始状态，等待执行
2. **executing**: 任务正在执行中
3. **success**: 任务执行成功
4. **failed**: 任务执行失败

### 2. 数据库同步

#### 自动状态同步
- 任务状态变更时自动同步到数据库
- 保持内存中的任务状态与数据库一致
- 支持执行结果和时间戳记录

#### 单次任务处理
- 执行完成后线程自动销毁
- 数据库中保留任务记录和执行结果
- 状态标记为最终执行结果

#### 重复任务处理
- 执行完成后重置为 `pending` 状态
- 继续调度下一次执行
- 保留最后一次执行结果

### 3. 新增API接口

#### 获取用户任务列表
```
GET /user/tasks
```
**响应:**
```json
{
    "success": true,
    "tasks": [
        {
            "task_id": "task_001",
            "active": true,
            "repetive": false,
            "type": "space",
            "status": "success",
            "lastExecutionTime": "2024-01-15T09:00:00.000Z",
            "lastExecutionResult": {
                "success": true,
                "message": "任务执行成功",
                "timestamp": "2024-01-15T09:00:00.000Z"
            },
            "createdAt": "2024-01-14T10:00:00.000Z",
            "updatedAt": "2024-01-15T09:00:00.000Z",
            // ... 其他字段
        }
    ]
}
```

#### 删除特定任务
```
DELETE /user/deletetask
```
**请求体:**
```json
{
    "task_id": "task_001"
}
```
**响应:**
```json
{
    "success": true,
    "message": "任务删除成功"
}
```

#### 获取任务状态统计
```
GET /user/taskstatus
```
**响应:**
```json
{
    "success": true,
    "stats": {
        "total": 5,
        "pending": 2,
        "executing": 1,
        "success": 1,
        "failed": 1,
        "repetitive": 3,
        "single": 2
    }
}
```

### 4. 完善现有接口

#### 创建任务接口 (POST /user/newtask)
- 自动设置初始状态为 `pending`
- 自动设置创建时间和更新时间
- 兼容原有参数格式

#### 更新任务接口 (POST /user/updatetask)
- 修复参数名称 (`task_id` 替代 `tasl_id`)
- 自动更新 `updatedAt` 时间戳
- 保持任务状态和执行历史

## 技术实现

### 1. 状态管理流程

```mermaid
graph TD
    A[创建任务] --> B[设置pending状态]
    B --> C[同步到数据库]
    C --> D[创建任务线程]
    D --> E[等待执行时间]
    E --> F[开始执行]
    F --> G[设置executing状态]
    G --> H[执行任务]
    H --> I{执行结果}
    I -->|成功| J[设置success状态]
    I -->|失败| K[设置failed状态]
    J --> L[记录执行结果]
    K --> L
    L --> M[同步到数据库]
    M --> N{重复任务?}
    N -->|是| O[重置为pending]
    N -->|否| P[销毁线程]
    O --> E
    P --> Q[保留数据库记录]
```

### 2. 数据库操作

#### 新增方法
- `deleteTask(userId: string, taskId: string)`: 删除特定任务
- `updateTask()`: 增强版本，保持任务历史信息

#### 状态同步
- 任务状态变更时自动调用 `updateTaskStatusInDatabase()`
- 确保内存和数据库状态一致性
- 支持并发操作的安全性

### 3. 任务池增强

#### 构造函数更新
```typescript
new TaskPool(
    executeCallback: (userId: string, task: BookingTask) => Promise<boolean>,
    updateTaskStatusCallback: (userId: string, task: BookingTask) => Promise<void>
)
```

#### 状态管理
- 任务线程自动管理状态流转
- 异常处理和状态恢复
- 执行结果记录和时间戳

## 使用示例

### 创建带状态的任务
```typescript
const task: BookingTask = {
    active: true,
    task_id: generateTaskId(),
    repetive: false,
    type: 'space',
    target: {
        id: ['room_147'],
        date: '2024-01-15',
        start_time: '14:00',
        end_time: '16:00'
    },
    execute: {
        day: 1,
        time: '09:00'
    },
    // 状态字段会自动设置
    status: 'pending',
    createdAt: '2024-01-14T10:00:00.000Z',
    updatedAt: '2024-01-14T10:00:00.000Z'
};
```

### 查询任务状态
```typescript
// 获取用户所有任务
const tasks = await fetch('/user/tasks');

// 获取状态统计
const stats = await fetch('/user/taskstatus');

// 删除特定任务
await fetch('/user/deletetask', {
    method: 'DELETE',
    body: JSON.stringify({ task_id: 'task_001' })
});
```

## 兼容性

### 向后兼容
- 所有原有API接口保持兼容
- 现有任务数据自动迁移状态字段
- 原有功能不受影响

### 数据迁移
- 现有任务自动设置默认状态
- 缺失的时间戳字段自动补充
- 平滑升级，无需手动迁移

## 注意事项

1. **状态一致性**: 任务状态变更会同时更新内存和数据库
2. **执行历史**: 单次任务执行完成后保留完整执行记录
3. **错误处理**: 状态更新失败不会影响任务执行
4. **性能优化**: 状态同步采用异步操作，不阻塞任务执行
5. **并发安全**: 支持多个任务同时执行和状态更新

## 监控和调试

### 日志记录
- 完整的状态变更日志
- 执行结果和错误信息
- 性能和时间统计

### 状态查询
- 实时任务状态查询
- 执行历史追踪
- 统计信息监控
