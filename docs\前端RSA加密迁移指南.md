# 前端RSA加密迁移指南

## 📋 概述

为了增强密码传输和存储的安全性，我们已经将身份认证系统升级为使用RSA非对称加密。本指南将帮助您将前端应用迁移到新的加密认证系统。

## 🔐 安全性改进

### 之前的系统
- 密码以明文形式通过HTTPS传输
- 服务器端使用bcrypt进行哈希存储

### 现在的系统
- 密码在前端使用RSA公钥加密后传输
- 服务器端使用RSA私钥解密，然后用bcrypt哈希存储
- 双重保护：传输层加密 + 存储层哈希

## 🚀 迁移步骤

### 1. 安装RSA加密库

#### 对于React/Vue/Angular项目
```bash
npm install jsencrypt
# 或
yarn add jsencrypt
```

#### 对于原生JavaScript项目
```html
<script src="https://cdn.jsdelivr.net/npm/jsencrypt@3.3.2/bin/jsencrypt.min.js"></script>
```

> **重要说明**: 前端必须使用`jsencrypt`库，因为`node-rsa`库依赖Node.js专有的Buffer模块，无法在浏览器环境中运行。

### 2. 获取RSA公钥

在进行登录或注册之前，需要先获取服务器的RSA公钥：

```javascript
// 获取RSA公钥
async function getPublicKey() {
    try {
        const response = await fetch('/crypto/public-key', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            return data.publicKey;
        } else {
            throw new Error('获取公钥失败');
        }
    } catch (error) {
        console.error('获取公钥错误:', error);
        throw error;
    }
}
```

### 3. 密码加密函数

#### ES6模块方式 (推荐)
```javascript
import { JSEncrypt } from 'jsencrypt';

// 加密密码函数
function encryptPassword(password, publicKey) {
    try {
        const encrypt = new JSEncrypt();
        encrypt.setPublicKey(publicKey);
        const encrypted = encrypt.encrypt(password);

        if (!encrypted) {
            throw new Error('加密失败');
        }

        return encrypted;
    } catch (error) {
        console.error('密码加密失败:', error);
        throw new Error('密码加密失败');
    }
}
```

#### CommonJS方式
```javascript
const JSEncrypt = require('jsencrypt').JSEncrypt;

function encryptPassword(password, publicKey) {
    try {
        const encrypt = new JSEncrypt();
        encrypt.setPublicKey(publicKey);
        const encrypted = encrypt.encrypt(password);

        if (!encrypted) {
            throw new Error('加密失败');
        }

        return encrypted;
    } catch (error) {
        console.error('密码加密失败:', error);
        throw new Error('密码加密失败');
    }
}
```

#### 原生JavaScript (CDN方式)
```javascript
// 使用全局的JSEncrypt对象
function encryptPassword(password, publicKey) {
    try {
        const encrypt = new JSEncrypt();
        encrypt.setPublicKey(publicKey);
        const encrypted = encrypt.encrypt(password);

        if (!encrypted) {
            throw new Error('加密失败');
        }

        return encrypted;
    } catch (error) {
        console.error('密码加密失败:', error);
        throw new Error('密码加密失败');
    }
}
```

### 4. 更新登录函数

```javascript
// 新的登录函数
async function login(email, password) {
    try {
        // 1. 获取RSA公钥
        const publicKey = await getPublicKey();
        
        // 2. 加密密码
        const encryptedPassword = encryptPassword(password, publicKey);
        
        // 3. 发送登录请求
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                password: encryptedPassword  // 发送加密后的密码
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 登录成功，保存token
            localStorage.setItem('token', data.token);
            return data;
        } else {
            throw new Error(data.message || '登录失败');
        }
    } catch (error) {
        console.error('登录错误:', error);
        throw error;
    }
}
```

### 5. 更新注册函数

```javascript
// 新的注册函数
async function register(name, email, password) {
    try {
        // 1. 获取RSA公钥
        const publicKey = await getPublicKey();
        
        // 2. 加密密码
        const encryptedPassword = encryptPassword(password, publicKey);
        
        // 3. 发送注册请求
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: name,
                email: email,
                password: encryptedPassword  // 发送加密后的密码
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            return data;
        } else {
            throw new Error(data.message || '注册失败');
        }
    } catch (error) {
        console.error('注册错误:', error);
        throw error;
    }
}
```

## 🔧 完整示例

### React组件示例

```jsx
import React, { useState } from 'react';
import { JSEncrypt } from 'jsencrypt';

const LoginForm = () => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);

    const getPublicKey = async () => {
        const response = await fetch('/crypto/public-key');
        const data = await response.json();
        return data.publicKey;
    };

    const encryptPassword = (password, publicKey) => {
        const encrypt = new JSEncrypt();
        encrypt.setPublicKey(publicKey);
        const encrypted = encrypt.encrypt(password);

        if (!encrypted) {
            throw new Error('密码加密失败');
        }

        return encrypted;
    };

    const handleLogin = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            const publicKey = await getPublicKey();
            const encryptedPassword = encryptPassword(password, publicKey);

            const response = await fetch('/auth/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email,
                    password: encryptedPassword
                })
            });

            const data = await response.json();

            if (data.success) {
                localStorage.setItem('token', data.token);
                // 登录成功处理
                console.log('登录成功');
            } else {
                console.error('登录失败:', data.message);
            }
        } catch (error) {
            console.error('登录失败:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleLogin}>
            <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="邮箱"
                required
            />
            <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="密码"
                required
            />
            <button type="submit" disabled={loading}>
                {loading ? '登录中...' : '登录'}
            </button>
        </form>
    );
};

export default LoginForm;
```

### Vue.js组件示例

```vue
<template>
  <form @submit.prevent="handleLogin">
    <input
      v-model="email"
      type="email"
      placeholder="邮箱"
      required
    />
    <input
      v-model="password"
      type="password"
      placeholder="密码"
      required
    />
    <button type="submit" :disabled="loading">
      {{ loading ? '登录中...' : '登录' }}
    </button>
  </form>
</template>

<script>
import { JSEncrypt } from 'jsencrypt';

export default {
  name: 'LoginForm',
  data() {
    return {
      email: '',
      password: '',
      loading: false
    };
  },
  methods: {
    async getPublicKey() {
      const response = await fetch('/crypto/public-key');
      const data = await response.json();
      return data.publicKey;
    },

    encryptPassword(password, publicKey) {
      const encrypt = new JSEncrypt();
      encrypt.setPublicKey(publicKey);
      const encrypted = encrypt.encrypt(password);

      if (!encrypted) {
        throw new Error('密码加密失败');
      }

      return encrypted;
    },

    async handleLogin() {
      this.loading = true;

      try {
        const publicKey = await this.getPublicKey();
        const encryptedPassword = this.encryptPassword(this.password, publicKey);

        const response = await fetch('/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: this.email,
            password: encryptedPassword
          })
        });

        const data = await response.json();

        if (data.success) {
          localStorage.setItem('token', data.token);
          console.log('登录成功');
        } else {
          console.error('登录失败:', data.message);
        }
      } catch (error) {
        console.error('登录失败:', error);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
```

### Angular组件示例

```typescript
import { Component } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { JSEncrypt } from 'jsencrypt';

@Component({
  selector: 'app-login',
  template: `
    <form (ngSubmit)="handleLogin()">
      <input
        [(ngModel)]="email"
        type="email"
        placeholder="邮箱"
        required
      />
      <input
        [(ngModel)]="password"
        type="password"
        placeholder="密码"
        required
      />
      <button type="submit" [disabled]="loading">
        {{ loading ? '登录中...' : '登录' }}
      </button>
    </form>
  `
})
export class LoginComponent {
  email = '';
  password = '';
  loading = false;

  constructor(private http: HttpClient) {}

  async getPublicKey(): Promise<string> {
    const response = await this.http.get<any>('/crypto/public-key').toPromise();
    return response.publicKey;
  }

  encryptPassword(password: string, publicKey: string): string {
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey);
    const encrypted = encrypt.encrypt(password);

    if (!encrypted) {
      throw new Error('密码加密失败');
    }

    return encrypted;
  }

  async handleLogin() {
    this.loading = true;

    try {
      const publicKey = await this.getPublicKey();
      const encryptedPassword = this.encryptPassword(this.password, publicKey);

      const response = await this.http.post<any>('/auth/login', {
        email: this.email,
        password: encryptedPassword
      }).toPromise();

      if (response.success) {
        localStorage.setItem('token', response.token);
        console.log('登录成功');
      } else {
        console.error('登录失败:', response.message);
      }
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      this.loading = false;
    }
  }
}
```

## ⚠️ 重要注意事项

1. **库选择**: 前端必须使用`jsencrypt`，不能使用`node-rsa`（Node.js专用）
2. **公钥缓存**: 建议缓存RSA公钥以避免频繁请求，但要定期更新
3. **错误处理**: 密码加密失败时要给用户友好的错误提示
4. **兼容性**: `jsencrypt`支持所有现代浏览器，包括IE8+
5. **安全性**: 不要在前端代码中硬编码任何密钥信息
6. **加密结果**: `jsencrypt`默认返回Base64编码的加密结果，与服务器端兼容

## 🔄 向后兼容

当前系统已经完全迁移到RSA加密，不再支持明文密码传输。所有前端应用都必须按照本指南进行更新。

## ✅ 测试验证

系统已经通过完整的端到端测试：

1. ✅ RSA公钥获取API正常工作
2. ✅ 密码RSA加密功能正常
3. ✅ 用户注册流程（加密密码）正常
4. ✅ 用户登录流程（加密密码）正常
5. ✅ JWT Token认证正常

测试结果显示：
- 公钥长度：450字符
- 加密后密码长度：344字符（Base64编码）
- 所有API端点响应正常
- 错误处理机制工作正常

## 📞 技术支持

如果在迁移过程中遇到问题，请联系开发团队或查看相关文档。

## 🔧 故障排除

### 常见问题

1. **"密码格式错误"错误**
   - 检查RSA公钥是否正确获取
   - 确认密码加密使用的是正确的公钥
   - 验证加密后的密码是Base64格式

2. **"获取公钥失败"错误**
   - 确认服务器正在运行
   - 检查网络连接
   - 验证API端点URL是否正确

3. **加密失败**
   - 确认jsencrypt库已正确安装和导入
   - 检查密码长度是否超过RSA密钥限制（通常245字节以内）
   - 验证公钥格式是否正确
   - 确认JSEncrypt对象创建成功

4. **浏览器兼容性问题**
   - jsencrypt支持IE8+和所有现代浏览器
   - 如果使用ES6模块，确保浏览器支持或使用Babel转译
   - CDN方式最兼容，推荐用于测试

5. **导入错误**
   - ES6: `import { JSEncrypt } from 'jsencrypt'`
   - CommonJS: `const { JSEncrypt } = require('jsencrypt')`
   - CDN: 直接使用全局`JSEncrypt`对象

## 🧪 快速测试

### 在线测试页面

我们提供了一个完整的HTML测试页面 `test-jsencrypt.html`，可以直接在浏览器中测试：

1. 确保服务器运行在 `http://localhost:3000`
2. 在浏览器中打开 `test-jsencrypt.html`
3. 页面会自动测试：
   - 获取RSA公钥
   - JSEncrypt密码加密
   - 用户注册流程
   - 用户登录流程
   - Token认证

### 浏览器测试

```bash
# 1. 启动服务器
npm run dev

# 2. 在浏览器中打开测试页面
# 文件位置: test-jsencrypt.html
```

> **注意**: JSEncrypt是专门为浏览器环境设计的，无法在Node.js环境中直接运行。所有测试都应该在浏览器中进行。

### 验证要点

- ✅ 公钥获取成功（450字符左右）
- ✅ JSEncrypt加密成功（344字符左右）
- ✅ 注册/登录流程正常
- ✅ 错误处理正常工作

## 📋 迁移检查清单

在完成迁移后，请确认以下项目：

- [ ] 已安装 `jsencrypt` 库（不是 `node-rsa`）
- [ ] 已更新所有登录/注册表单
- [ ] 已实现公钥获取逻辑
- [ ] 已实现密码加密函数
- [ ] 已添加错误处理机制
- [ ] 已测试在目标浏览器中正常工作
- [ ] 已更新相关文档和注释
