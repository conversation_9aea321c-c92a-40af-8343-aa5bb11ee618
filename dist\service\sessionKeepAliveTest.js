"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runSessionKeepAliveTests = runSessionKeepAliveTests;
const sessionPool_1 = require("./sessionPool");
class MockDBConnection {
    constructor() {
        this.users = new Map();
        this.db = null;
        this.dbPath = 'mock://test';
    }
    async createUser(userData) {
        throw new Error('Mock method not implemented');
    }
    async findUser(query) {
        if (query._id) {
            return this.users.get(query._id) || null;
        }
        return null;
    }
    async findAllUsers(query = {}) {
        return Array.from(this.users.values());
    }
    async updateUser(id, updates) {
        const user = this.users.get(id);
        if (!user)
            return 0;
        const updatedUser = { ...user, ...updates };
        this.users.set(id, updatedUser);
        return 1;
    }
    async setAccount(id, account) {
        throw new Error('Mock method not implemented');
    }
    async addTask(id, task) {
        throw new Error('Mock method not implemented');
    }
    async updateTask(id, updates) {
        throw new Error('Mock method not implemented');
    }
    async deleteTask(id, task_id) {
        throw new Error('Mock method not implemented');
    }
    async updateSessionLoginTime(id) {
        const user = this.users.get(id);
        if (!user)
            return 0;
        user.services = user.services || {};
        user.services.sessionLoginTime = new Date().toISOString();
        user.updatedAt = new Date().toISOString();
        this.users.set(id, user);
        console.log(`[模拟DB] 更新用户 ${id} 会话登录时间: ${user.services.sessionLoginTime}`);
        return 1;
    }
    async deleteUser(id) {
        throw new Error('Mock method not implemented');
    }
    addTestUser(user) {
        this.users.set(user._id, user);
    }
    getTestUser(id) {
        return this.users.get(id);
    }
    setUserSessionLoginTime(id, time) {
        const user = this.users.get(id);
        if (user) {
            user.services = user.services || {};
            user.services.sessionLoginTime = time;
            this.users.set(id, user);
        }
    }
}
async function testSessionTimeoutCheck() {
    console.log('\n=== 测试会话超时检查 ===');
    const mockDB = new MockDBConnection();
    const sessionPool = new sessionPool_1.SessionPool(mockDB);
    const testUser = {
        _id: 'test_user_001',
        name: 'testuser',
        email: '<EMAIL>',
        authentication: {
            password: 'testpass'
        },
        services: {
            account: {
                username: 'testuser',
                password: 'testpass',
                mobile: '***********'
            },
            sessionLoginTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    mockDB.addTestUser(testUser);
    console.log('用户会话登录时间:', testUser.services?.sessionLoginTime);
    console.log('当前时间:', new Date().toISOString());
    console.log('预期行为: 超过1小时，应该执行重新登录');
    const result = await sessionPool.checkAndRefreshSessionIfNeeded('test_user_001');
    console.log('检查结果:', result);
    const updatedUser = mockDB.getTestUser('test_user_001');
    console.log('更新后的会话登录时间:', updatedUser?.services?.sessionLoginTime);
}
async function testSessionNotExpired() {
    console.log('\n=== 测试会话未超时情况 ===');
    const mockDB = new MockDBConnection();
    const sessionPool = new sessionPool_1.SessionPool(mockDB);
    const testUser = {
        _id: 'test_user_002',
        name: 'testuser2',
        email: '<EMAIL>',
        authentication: {
            password: 'testpass'
        },
        services: {
            account: {
                username: 'testuser2',
                password: 'testpass',
                mobile: '***********'
            },
            sessionLoginTime: new Date(Date.now() - 30 * 60 * 1000).toISOString()
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    mockDB.addTestUser(testUser);
    console.log('用户会话登录时间:', testUser.services?.sessionLoginTime);
    console.log('当前时间:', new Date().toISOString());
    console.log('预期行为: 未超过1小时，不应该重新登录');
    const originalLoginTime = testUser.services?.sessionLoginTime;
    const result = await sessionPool.checkAndRefreshSessionIfNeeded('test_user_002');
    console.log('检查结果:', result);
    const updatedUser = mockDB.getTestUser('test_user_002');
    console.log('检查后的会话登录时间:', updatedUser?.services?.sessionLoginTime);
    console.log('登录时间是否变化:', originalLoginTime !== updatedUser?.services?.sessionLoginTime);
}
async function testNoSessionLoginTime() {
    console.log('\n=== 测试无会话登录时间记录的情况 ===');
    const mockDB = new MockDBConnection();
    const sessionPool = new sessionPool_1.SessionPool(mockDB);
    const testUser = {
        _id: 'test_user_003',
        name: 'testuser3',
        email: '<EMAIL>',
        authentication: {
            password: 'testpass'
        },
        services: {
            account: {
                username: 'testuser3',
                password: 'testpass',
                mobile: '***********'
            }
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    mockDB.addTestUser(testUser);
    console.log('用户会话登录时间:', testUser.services?.sessionLoginTime || '无记录');
    console.log('预期行为: 无登录时间记录，应该执行登录');
    const result = await sessionPool.checkAndRefreshSessionIfNeeded('test_user_003');
    console.log('检查结果:', result);
    const updatedUser = mockDB.getTestUser('test_user_003');
    console.log('设置后的会话登录时间:', updatedUser?.services?.sessionLoginTime);
}
async function testDatabaseUpdate() {
    console.log('\n=== 测试数据库更新功能 ===');
    const mockDB = new MockDBConnection();
    const testUser = {
        _id: 'test_user_004',
        name: 'testuser4',
        email: '<EMAIL>',
        authentication: {
            password: 'testpass'
        },
        services: {
            account: {
                username: 'testuser4',
                password: 'testpass',
                mobile: '***********'
            }
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    mockDB.addTestUser(testUser);
    console.log('更新前的会话登录时间:', testUser.services?.sessionLoginTime || '无');
    const result = await mockDB.updateSessionLoginTime('test_user_004');
    console.log('数据库更新结果:', result);
    const updatedUser = mockDB.getTestUser('test_user_004');
    console.log('更新后的会话登录时间:', updatedUser?.services?.sessionLoginTime);
}
function testSystemStatus() {
    console.log('\n=== 测试系统状态 ===');
    const mockDB = new MockDBConnection();
    const sessionPool = new sessionPool_1.SessionPool(mockDB);
    const status = sessionPool.getSystemStatus();
    console.log('系统状态:', JSON.stringify(status, null, 2));
}
async function runSessionKeepAliveTests() {
    console.log('开始新会话保活逻辑测试...\n');
    try {
        await testDatabaseUpdate();
        await testSessionTimeoutCheck();
        await testSessionNotExpired();
        await testNoSessionLoginTime();
        testSystemStatus();
        console.log('\n✅ 所有会话保活测试完成！');
    }
    catch (error) {
        console.error('\n❌ 测试过程中出现错误:', error);
    }
}
if (require.main === module) {
    runSessionKeepAliveTests();
}
