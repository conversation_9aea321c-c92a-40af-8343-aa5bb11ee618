# Installation
> `npm install --save @types/pouchdb-adapter-cordova-sqlite`

# Summary
This package contains type definitions for pouchdb-adapter-cordova-sqlite (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-cordova-sqlite.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-cordova-sqlite/index.d.ts)
````ts
/// <reference types="pouchdb-core" />

declare namespace PouchDB {
    namespace AdapterCordovaSqlite {
        interface Configuration extends Configuration.LocalDatabaseConfiguration {
            /**
             * Location of database e.g. 'Default'.
             */
            location?: string | undefined;

            /**
             * Location of database e.g. 'Default'. Only use 'location' or 'iosDatabaseLocation' not both.
             */
            iosDatabaseLocation?: string | undefined;

            /**
             * Version of android database to use.
             */
            androidDatabaseImplementation?: number | undefined;

            /**
             * Enable autocompation of database.
             */
            auto_compaction?: boolean | undefined;

            adapter: "cordova-sqlite";
        }
    }

    interface Static {
        new<Content extends {}>(name: string | null, options: AdapterCordovaSqlite.Configuration): Database<Content>;
    }
}

declare module "pouchdb-adapter-cordova-sqlite" {
    const plugin: PouchDB.Plugin;
    export = plugin;
}

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core)

# Credits
These definitions were written by [Simon Paulger](https://github.com/spaulg), [Brian Geppert](https://github.com/geppy), [Frederico Galvão](https://github.com/fredgalvao), and [Matthew Paul](https://github.com/coffeymatt).
