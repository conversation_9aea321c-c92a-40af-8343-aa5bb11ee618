"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = exports.DBConnection = void 0;
const pouchdb_1 = __importDefault(require("pouchdb"));
const pouchdb_find_1 = __importDefault(require("pouchdb-find"));
const path = __importStar(require("path"));
const log_1 = require("./log");
const helpers_1 = require("../helpers");
pouchdb_1.default.plugin(pouchdb_find_1.default);
class DBConnection {
    constructor(filename) {
        this.dbPath = filename || path.join(process.cwd(), 'data', 'users');
        this.db = new pouchdb_1.default(this.dbPath);
        log_1.DatabaseLogger.info('PouchDB 数据库初始化完成', {
            dbPath: this.dbPath
        });
    }
    async createUser(userData) {
        try {
            const newUser = {
                ...userData,
                _id: (0, helpers_1.generateUserId)(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            const response = await this.db.put(newUser);
            const createdUser = { ...newUser, _rev: response.rev };
            log_1.DatabaseLogger.info('用户创建成功', {
                userId: createdUser._id,
                email: createdUser.email
            });
            return createdUser;
        }
        catch (error) {
            log_1.DatabaseLogger.error('用户创建失败', {
                error: error.message,
                userData: { name: userData.name, email: userData.email }
            });
            throw error;
        }
    }
    async findUser(query) {
        try {
            if (query._id) {
                try {
                    const doc = await this.db.get(query._id);
                    log_1.DatabaseLogger.debug('通过ID查找用户成功', { userId: query._id });
                    return doc;
                }
                catch (error) {
                    if (error.status === 404) {
                        return null;
                    }
                    throw error;
                }
            }
            const response = await this.db.find({
                selector: query,
                limit: 1
            });
            const user = response.docs.length > 0 ? response.docs[0] : null;
            log_1.DatabaseLogger.debug('查找用户完成', {
                query,
                found: !!user,
                userId: user?._id
            });
            return user;
        }
        catch (error) {
            log_1.DatabaseLogger.error('查找用户失败', {
                error: error.message,
                query
            });
            throw error;
        }
    }
    async findAllUsers(query = {}) {
        try {
            try {
                await this.db.createIndex({
                    index: { fields: ['createdAt'] }
                });
            }
            catch (indexError) {
            }
            const response = await this.db.find({
                selector: query,
                sort: [{ createdAt: 'desc' }]
            });
            log_1.DatabaseLogger.debug('查找所有用户完成', {
                query,
                count: response.docs.length
            });
            return response.docs;
        }
        catch (error) {
            try {
                const response = await this.db.find({
                    selector: query
                });
                log_1.DatabaseLogger.debug('查找所有用户完成（无排序）', {
                    query,
                    count: response.docs.length
                });
                return response.docs.sort((a, b) => {
                    const aTime = new Date(a.createdAt || '').getTime();
                    const bTime = new Date(b.createdAt || '').getTime();
                    return bTime - aTime;
                });
            }
            catch (fallbackError) {
                log_1.DatabaseLogger.error('查找所有用户失败', {
                    error: error.message,
                    fallbackError: fallbackError.message,
                    query
                });
                throw error;
            }
        }
    }
    async updateUser(id, updates) {
        try {
            const currentDoc = await this.db.get(id);
            const updatedDoc = {
                ...currentDoc,
                ...updates,
                updatedAt: new Date().toISOString()
            };
            await this.db.put(updatedDoc);
            log_1.DatabaseLogger.info('用户更新成功', {
                userId: id,
                updatedFields: Object.keys(updates)
            });
            return 1;
        }
        catch (error) {
            if (error.status === 404) {
                log_1.DatabaseLogger.warn('更新用户失败：用户不存在', { userId: id });
                return 0;
            }
            log_1.DatabaseLogger.error('用户更新失败', {
                error: error.message,
                userId: id,
                updates
            });
            throw error;
        }
    }
    async setAccount(id, account) {
        try {
            const currentDoc = await this.db.get(id);
            const updatedDoc = {
                ...currentDoc,
                services: {
                    ...currentDoc.services,
                    account: account
                },
                updatedAt: new Date().toISOString()
            };
            await this.db.put(updatedDoc);
            log_1.DatabaseLogger.info('用户账户信息更新成功', {
                userId: id,
                username: account.username
            });
            return 1;
        }
        catch (error) {
            if (error.status === 404) {
                log_1.DatabaseLogger.warn('设置账户失败：用户不存在', { userId: id });
                return 0;
            }
            log_1.DatabaseLogger.error('设置账户失败', {
                error: error.message,
                userId: id
            });
            throw error;
        }
    }
    async addTask(id, task) {
        try {
            const currentDoc = await this.db.get(id);
            const currentTasks = currentDoc.services?.tasks || [];
            const updatedDoc = {
                ...currentDoc,
                services: {
                    ...currentDoc.services,
                    tasks: [...currentTasks, task]
                },
                updatedAt: new Date().toISOString()
            };
            await this.db.put(updatedDoc);
            log_1.DatabaseLogger.info('任务添加成功', {
                userId: id,
                taskId: task.task_id,
                taskType: task.type
            });
            return 1;
        }
        catch (error) {
            if (error.status === 404) {
                log_1.DatabaseLogger.warn('添加任务失败：用户不存在', { userId: id });
                return 0;
            }
            log_1.DatabaseLogger.error('添加任务失败', {
                error: error.message,
                userId: id,
                task
            });
            throw error;
        }
    }
    async updateTask(id, updates) {
        const task_id = updates.task_id;
        let flag = false;
        try {
            const currentDoc = await this.db.get(id);
            const currentTasks = currentDoc.services?.tasks || [];
            const updatedTasks = currentTasks.map((task) => {
                if (flag)
                    return task;
                if (task.task_id === task_id) {
                    flag = true;
                    return {
                        ...task,
                        ...updates,
                        updatedAt: new Date().toISOString()
                    };
                }
                return task;
            });
            if (!flag)
                throw new Error('任务不存在');
            const updatedDoc = {
                ...currentDoc,
                services: {
                    ...currentDoc.services,
                    tasks: updatedTasks
                },
                updatedAt: new Date().toISOString()
            };
            await this.db.put(updatedDoc);
            log_1.DatabaseLogger.info('任务更新成功', {
                userId: id,
                taskId: task_id,
                updatedFields: Object.keys(updates)
            });
            return 1;
        }
        catch (error) {
            if (error.status === 404) {
                log_1.DatabaseLogger.warn('更新任务失败：用户不存在', { userId: id });
                return 0;
            }
            log_1.DatabaseLogger.error('更新任务失败', {
                error: error.message,
                userId: id,
                taskId: task_id,
                updates
            });
            throw error;
        }
    }
    async deleteTask(id, task_id) {
        try {
            const currentDoc = await this.db.get(id);
            const currentTasks = currentDoc.services?.tasks || [];
            const filteredTasks = currentTasks.filter((task) => task.task_id !== task_id);
            if (filteredTasks.length === currentTasks.length) {
                throw new Error('任务不存在');
            }
            const updatedDoc = {
                ...currentDoc,
                services: {
                    ...currentDoc.services,
                    tasks: filteredTasks
                },
                updatedAt: new Date().toISOString()
            };
            await this.db.put(updatedDoc);
            log_1.DatabaseLogger.info('任务删除成功', {
                userId: id,
                taskId: task_id
            });
            return 1;
        }
        catch (error) {
            if (error.status === 404) {
                log_1.DatabaseLogger.warn('删除任务失败：用户不存在', { userId: id });
                return 0;
            }
            log_1.DatabaseLogger.error('删除任务失败', {
                error: error.message,
                userId: id,
                taskId: task_id
            });
            throw error;
        }
    }
    async updateSessionLoginTime(id) {
        try {
            const currentDoc = await this.db.get(id);
            const updatedDoc = {
                ...currentDoc,
                services: {
                    ...currentDoc.services,
                    sessionLoginTime: new Date().toISOString()
                },
                updatedAt: new Date().toISOString()
            };
            await this.db.put(updatedDoc);
            log_1.DatabaseLogger.info('会话登录时间更新成功', {
                userId: id,
                sessionLoginTime: updatedDoc.services?.sessionLoginTime
            });
            return 1;
        }
        catch (error) {
            if (error.status === 404) {
                log_1.DatabaseLogger.warn('更新会话登录时间失败：用户不存在', { userId: id });
                return 0;
            }
            log_1.DatabaseLogger.error('更新会话登录时间失败', {
                error: error.message,
                userId: id
            });
            throw error;
        }
    }
    async setRoomStar(id, room_id, starred) {
        try {
            const currentDoc = await this.db.get(id);
            const currentStars = currentDoc.services?.stars || [];
            let updatedStars;
            if (starred) {
                if (!currentStars.includes(room_id)) {
                    updatedStars = [...currentStars, room_id];
                }
                else {
                    updatedStars = currentStars;
                }
            }
            else {
                updatedStars = currentStars.filter((starId) => starId !== room_id);
            }
            const updatedDoc = {
                ...currentDoc,
                services: {
                    ...currentDoc.services,
                    stars: updatedStars
                },
                updatedAt: new Date().toISOString()
            };
            await this.db.put(updatedDoc);
            log_1.DatabaseLogger.info('房间收藏状态更新成功', {
                userId: id,
                roomId: room_id,
                starred: starred,
                totalStars: updatedStars.length
            });
            return 1;
        }
        catch (error) {
            if (error.status === 404) {
                log_1.DatabaseLogger.warn('更新房间收藏失败：用户不存在', { userId: id });
                return 0;
            }
            log_1.DatabaseLogger.error('更新房间收藏失败', {
                error: error.message,
                userId: id,
                roomId: room_id,
                starred: starred
            });
            throw error;
        }
    }
    async deleteUser(id) {
        try {
            const currentDoc = await this.db.get(id);
            await this.db.remove(currentDoc);
            log_1.DatabaseLogger.info('用户删除成功', { userId: id });
            return 1;
        }
        catch (error) {
            if (error.status === 404) {
                log_1.DatabaseLogger.warn('删除用户失败：用户不存在', { userId: id });
                return 0;
            }
            log_1.DatabaseLogger.error('删除用户失败', {
                error: error.message,
                userId: id
            });
            throw error;
        }
    }
}
exports.DBConnection = DBConnection;
exports.db = new DBConnection("users.db");
