# Installation
> `npm install --save @types/pouchdb-adapter-node-websql`

# Summary
This package contains type definitions for pouchdb-adapter-node-websql (https://pouchdb.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-node-websql.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-node-websql/index.d.ts)
````ts
/// <reference types="pouchdb-core" />
/// <reference types="pouchdb-adapter-websql" />

declare const plugin: PouchDB.Plugin;
export = plugin;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/pouchdb-adapter-websql](https://npmjs.com/package/@types/pouchdb-adapter-websql), [@types/pouchdb-core](https://npmjs.com/package/@types/pouchdb-core)

# Credits
These definitions were written by [<PERSON>](https://github.com/spaulg), [Brian Geppert](https://github.com/geppy), and [Frederico Galvão](https://github.com/fredgalvao).
