{"name": "glob-parent", "version": "2.0.0", "description": "Strips glob magic from a string to provide the parent path", "main": "index.js", "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/es128/glob-parent"}, "keywords": ["glob", "parent", "strip", "path", "directory", "base"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "homepage": "https://github.com/es128/glob-parent", "dependencies": {"is-glob": "^2.0.0"}, "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}}