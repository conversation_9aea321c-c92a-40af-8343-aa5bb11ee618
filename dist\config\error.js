"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserError = exports.AuthError = void 0;
exports.raiseError = raiseError;
const log_1 = require("./log");
class AuthError extends Error {
    constructor(message, statusCode) {
        super(message);
        this.statusCode = statusCode;
    }
}
exports.AuthError = AuthError;
class UserError extends Error {
    constructor(message, statusCode) {
        super(message);
        this.statusCode = statusCode;
    }
}
exports.UserError = UserError;
function raiseError(err, req, res, next) {
    log_1.APILogger.error('API请求失败', {
        requestId: req.headers['x-request-id'],
        method: req.method,
        url: req.url,
        statusCode: err.statusCode,
        error: err.message
    });
    res.status(err.statusCode || 400).json({ success: false, msg: err.message });
}
