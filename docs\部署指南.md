# 图书馆预约系统部署指南

## 概述

本指南详细说明如何将图书馆预约系统部署到Linux云服务器，包括传统部署和Docker部署两种方式。

## 系统要求

### 最低配置
- **CPU**: 1核心
- **内存**: 1GB RAM
- **存储**: 10GB 可用空间
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **网络**: 公网IP，开放80/443端口

### 推荐配置
- **CPU**: 2核心
- **内存**: 2GB RAM
- **存储**: 20GB SSD
- **操作系统**: Ubuntu 22.04 LTS

## 部署方式一：Docker部署（推荐）

### 1. 环境准备

#### 安装Docker和Docker Compose

**Ubuntu/Debian:**
```bash
# 更新包索引
sudo apt update

# 安装必要的包
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组
sudo usermod -aG docker $USER
```

**CentOS/RHEL:**
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

### 2. 部署应用

#### 下载项目代码
```bash
# 克隆项目（或上传代码包）
git clone <your-repository-url> lib-api
cd lib-api

# 或者上传代码包
scp -r ./lib-api user@your-server:/home/<USER>/
```

#### 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env.production

# 编辑生产环境配置
nano .env.production
```

**生产环境配置示例：**
```bash
# 基础配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
FRONTEND_URL=https://yourdomain.com

# SSL配置
SSL_CERT=/app/ssl/server.crt
SSL_KEY=/app/ssl/server.key

# JWT配置（必须设置强密钥）
JWT_SECRET=your-super-secure-jwt-secret-key-here

# 数据库配置
DB_PATH=/app/data/users.db

# 日志配置
LOG_LEVEL=info
LOG_DIR=/app/logs

# 安全配置
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

#### 生成SSL证书
```bash
# 开发环境自签名证书
./scripts/generate-ssl.sh

# 生产环境Let's Encrypt证书
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ./ssl/server.crt
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ./ssl/server.key
```

#### 启动服务
```bash
# 生产环境部署
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f app
```

### 3. 配置反向代理（可选）

如果不使用Docker中的Nginx，可以在宿主机配置：

#### 安装Nginx
```bash
sudo apt install nginx
```

#### 配置Nginx
```bash
sudo nano /etc/nginx/sites-available/lib-api
```

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/ssl/server.crt;
    ssl_certificate_key /path/to/ssl/server.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/lib-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 部署方式二：传统部署

### 1. 环境准备

#### 安装Node.js
```bash
# 使用NodeSource仓库安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### 安装PM2（进程管理器）
```bash
sudo npm install -g pm2
```

### 2. 部署应用

#### 上传代码
```bash
# 上传项目文件
scp -r ./lib-api user@your-server:/home/<USER>/
cd /home/<USER>/lib-api
```

#### 安装依赖
```bash
# 安装生产依赖
npm ci --only=production
```

#### 构建应用
```bash
# 编译TypeScript
npm run build
```

#### 配置环境
```bash
# 复制环境变量
cp .env.example .env
nano .env
```

#### 启动服务
```bash
# 使用PM2启动
pm2 start dist/index.js --name "lib-api"

# 设置开机自启
pm2 startup
pm2 save
```

### 3. 配置系统服务

#### 创建systemd服务
```bash
sudo nano /etc/systemd/system/lib-api.service
```

```ini
[Unit]
Description=Library API Service
After=network.target

[Service]
Type=simple
User=nodejs
WorkingDirectory=/home/<USER>/lib-api
Environment=NODE_ENV=production
ExecStart=/usr/bin/node dist/index.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
sudo systemctl enable lib-api
sudo systemctl start lib-api
sudo systemctl status lib-api
```

## 监控和维护

### 1. 日志管理

#### Docker环境
```bash
# 查看应用日志
docker-compose -f docker-compose.prod.yml logs -f app

# 查看系统日志
docker-compose -f docker-compose.prod.yml logs -f nginx
```

#### 传统部署
```bash
# PM2日志
pm2 logs lib-api

# 系统日志
sudo journalctl -u lib-api -f
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:3000/health

# 检查SSL证书
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com
```

### 3. 备份策略

#### 数据备份
```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/lib-api"
mkdir -p $BACKUP_DIR

# 备份数据库
cp -r data/ $BACKUP_DIR/data_$DATE/

# 备份配置
cp .env $BACKUP_DIR/env_$DATE

# 备份日志
cp -r logs/ $BACKUP_DIR/logs_$DATE/

# 清理7天前的备份
find $BACKUP_DIR -type d -mtime +7 -exec rm -rf {} +
EOF

chmod +x backup.sh
```

#### 定时备份
```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /home/<USER>/lib-api/backup.sh
```

### 4. 更新部署

#### Docker更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建和部署
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d
```

#### 传统部署更新
```bash
# 拉取代码
git pull origin main

# 安装依赖
npm ci --only=production

# 重新构建
npm run build

# 重启服务
pm2 restart lib-api
```

## 安全配置

### 1. 防火墙设置
```bash
# UFW防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
```

### 2. SSL证书自动更新
```bash
# Let's Encrypt自动更新
sudo crontab -e

# 每月1号凌晨3点更新证书
0 3 1 * * /usr/bin/certbot renew --quiet && systemctl reload nginx
```

### 3. 系统安全
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 配置自动安全更新
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

## 故障排除

### 常见问题

1. **端口占用**
   ```bash
   sudo netstat -tlnp | grep :3000
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   sudo chown -R $USER:$USER /home/<USER>/lib-api
   chmod +x scripts/*.sh
   ```

3. **内存不足**
   ```bash
   # 添加swap
   sudo fallocate -l 1G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

4. **Docker问题**
   ```bash
   # 清理Docker
   docker system prune -a
   docker-compose down && docker-compose up -d
   ```

## 性能优化

### 1. 应用优化
```bash
# 启用gzip压缩（已在代码中配置）
# 配置静态文件缓存
# 使用CDN加速静态资源
```

### 2. 数据库优化
```bash
# PouchDB性能调优
# 定期清理日志文件
find logs/ -name "*.log" -mtime +30 -delete
```

### 3. 系统优化
```bash
# 调整文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
sysctl -p
```

## 监控告警

### 1. 基础监控
```bash
# 安装htop
sudo apt install htop

# 监控脚本
cat > monitor.sh << 'EOF'
#!/bin/bash
# 检查服务状态
if ! curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "Service is down!" | mail -s "Alert: Library API Down" <EMAIL>
fi
EOF
```

### 2. 日志监控
```bash
# 监控错误日志
tail -f logs/error.log | grep -i error
```

## 扩展部署

### 1. 负载均衡
```nginx
upstream lib_api {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
}

server {
    location / {
        proxy_pass http://lib_api;
    }
}
```

### 2. 集群部署
```bash
# 使用PM2集群模式
pm2 start dist/index.js -i max --name "lib-api-cluster"
```

通过以上步骤，您的图书馆预约系统应该能够成功部署到Linux云服务器上。
